# main.py
import asyncio
import logging
import sys
import os
from pathlib import Path

from config import load_config
from logger import setup_logger
from influx_writer import InfluxWriter
from config_validator import validate_config, ConfigValidationError
from monitors.opcua_monitor import monitor_opcua_machine
# from monitors.modbus_monitor import monitor_modbus_machine

# Global logger will be configured after setup_logger is called
log = None

def initialize_application():
    """
    Initialize the application with proper logging and configuration setup.
    
    Returns:
        tuple: (logger, config) if successful, (None, None) if failed
    """
    global log
    
    try:
        print("Initializing Manufacturing Monitor...")
        print("Setting up logging system...")
        
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        print(f"Logs directory: {logs_dir.absolute()}")
        
        # Generate timestamped log file name
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"manufacturing_monitor_{timestamp}.log"
        
        # Determine log level from environment or default to INFO
        log_level_str = os.getenv('LOG_LEVEL', 'INFO').upper()
        log_level = getattr(logging, log_level_str, logging.INFO)
        
        # Setup the logger with timestamped log file in logs directory
        log_file_path = logs_dir / log_filename
        logger = setup_logger(log_level=log_level, log_file=str(log_file_path))
        
        log = logging.getLogger("manufacturing_monitor.main")
        log.info("="*60)
        log.info("Manufacturing Monitor Starting Up")
        log.info("="*60)
        log.info(f"Log level set to: {log_level_str}")
        log.info(f"Log file location: {log_file_path.absolute()}")
        log.info("Logging system initialized successfully")
        
        # Config will be loaded separately
        return logger, None
        
    except Exception as e:
        print(f"CRITICAL: Failed to initialize logging system: {e}")
        print("Application cannot continue without logging. Exiting.")
        return None, None

def load_application_config():
    """
    Load application configuration with comprehensive error handling.
    
    Returns:
        dict: Configuration dictionary if successful, None if failed
    """
    try:
        log.info("Loading application configuration...")
        
        # Check if config file exists
        config_path = "config.yaml"
        if not Path(config_path).exists():
            log.error(f"Configuration file '{config_path}' not found in current directory")
            log.info(f"Current working directory: {os.getcwd()}")
            log.info("Please ensure config.yaml exists in the working directory")
            return None
        
        # Load configuration
        config = load_config(config_path)
        
        # Validate essential configuration sections
        try:
            validate_config(config)
            log.info("Configuration loaded and validated successfully")
            return config
        except ConfigValidationError as e:
            log.error(f"Configuration validation failed: {e}")
            return None
            
    except FileNotFoundError as e:
        log.critical(f"Configuration file not found: {e}")
        log.critical("Application cannot start without configuration file")
        return None
    except Exception as e:
        log.critical(f"Failed to load configuration: {e}")
        log.critical("Application cannot start with invalid configuration")
        return None

async def main():
    """The main entry point for the manufacturing monitoring service."""
    
    # Step 1: Initialize logging system
    logger, _ = initialize_application()
    if not logger:
        sys.exit(1)
    
    try:
        # Step 2: Load and validate configuration
        config = load_application_config()
        if not config:
            log.critical("Failed to load configuration. Exiting.")
            sys.exit(1)
        
        # Step 3: Determine environment
        environment = os.getenv('ENVIRONMENT', 'development').lower()
        env_config = config[environment]
        log.info(f"Running in {environment} mode")
        
        # Step 4: Initialize InfluxDB writer
        log.info("Initializing InfluxDB connection...")
        try:
            influx_writer = InfluxWriter(env_config['influxdb'])
            log.info("InfluxDB writer initialized successfully")
        except Exception as e:
            log.critical(f"Failed to initialize InfluxDB writer: {e}")
            log.critical("Cannot continue without database connection")
            sys.exit(1)
        
        # Step 5: Start monitoring machines
        machines = env_config['machines']
        if not machines:
            log.warning("No machines configured for monitoring")
            log.info("Service will run but no data will be collected")
            
            # Keep the service running
            try:
                while True:
                    await asyncio.sleep(60)
                    log.info("Service running with no machines to monitor...")
            except KeyboardInterrupt:
                log.info("Service stopped by user")
            return
        
        log.info(f"Starting monitoring for {len(machines)} machines...")
        
        # Create monitoring tasks for each machine
        monitoring_tasks = []
        for machine_name, machine_config in machines.items():
            log.info(f"Creating monitoring task for machine: {machine_name}")
            
            # Determine protocol type
            protocol = machine_config.get('protocol', 'opcua')
            
            if protocol == 'opcua':
                task = asyncio.create_task(
                    monitor_opcua_machine(machine_name, machine_config, influx_writer),
                    name=f"monitor_opcua_{machine_name}"
                )
            # elif protocol == 'modbus':
            #     task = asyncio.create_task(
            #         monitor_modbus_machine(machine_name, machine_config, influx_writer),
            #         name=f"monitor_modbus_{machine_name}"
            #     )
            else:
                log.error(f"Unsupported protocol '{protocol}' for machine '{machine_name}'. Skipping.")
                continue
                
            monitoring_tasks.append(task)
            log.info(f"Created {protocol.upper()} monitoring task for machine: {machine_name}")
        
        if not monitoring_tasks:
            log.warning("No valid monitoring tasks were created")
            log.info("Service will run but no data will be collected")
            
            # Keep the service running
            try:
                while True:
                    await asyncio.sleep(60)
                    log.info("Service running with no valid machines to monitor...")
            except KeyboardInterrupt:
                log.info("Service stopped by user")
            return
        
        log.info("All monitoring tasks created successfully")
        log.info("Manufacturing Monitor is now running...")
        log.info("Press Ctrl+C to stop the service")
        
        # Wait for all monitoring tasks to complete (they should run indefinitely) 
        # Exits infinite loop in monitoring tasks if 'Ctrl+C' is triggered for graceful shutdown
        try:
            await asyncio.gather(*monitoring_tasks)
        except KeyboardInterrupt:
            log.info("Shutdown signal received. Stopping monitoring tasks...")
            
            for task in monitoring_tasks:
                if not task.done():
                    task.cancel()
            
            await asyncio.gather(*monitoring_tasks, return_exceptions=True)
            log.info("All monitoring tasks stopped")
        
    except Exception as e:
        log.critical(f"Unexpected error in main application: {e}")
        log.critical("Application will exit")
        sys.exit(1)
    
    finally:
        log.info("Manufacturing Monitor shutdown complete")
        log.info("="*60)

if __name__ == "__main__":
    try:
        # Run the async main function
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error starting application: {e}")
        sys.exit(1)