import socket
import time
import yaml
from lib_loggers import set_logger

# Load configuration
try:
    with open("network_config.yaml") as config_file:
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The network_config.yaml file should be present.')

logger = set_logger()

def test_tcp_connection(host, port, timeout=3):
    """Test if a TCP connection can be established to host:port"""
    # TODO: Implement basic socket connection test
    # Steps:
    # 1. Create a socket using socket.socket()
    # 2. Set timeout using socket.settimeout()
    # 3. Try to connect using socket.connect()
    # 4. Return True if successful, False if failed
    # 5. Handle exceptions gracefully
    logger.info(f"Testing connection to {host}:{port}")
    
    try:
        # Create a socket using Address Family: Internet (IPv4) & Stream Socket (TCP)
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # Prevents our code from hanging if the connection takes too long
        sock.settimeout(timeout)
        # Connect the socket to the host and port aka the slave
        sock.connect((host, port))
        logger.info(f"Connection to {host}:{port} successful")
        return True
    except socket.timeout:
        logger.warning(f"Connection to {host}:{port} timed out after {timeout}s")
        return False
    except ConnectionRefusedError:
        logger.warning(f"Connection to {host}:{port} refused (service not running)")
        return False
    except socket.gaierror as e:
        logger.warning(f"DNS resolution failed for {host}: {e}")
        return False
    except Exception as e:
        logger.error(f"Connection to {host}:{port} failed: {e}")
        return False
    finally:
        if sock:
            sock.close()
    

def scan_network_range(base_ip, start_port, end_port, timeout=1):
    """Scan a range of ports on a single IP address"""
    # TODO: Implement port scanning
    # This teaches how networks and services work
    # Steps:
    # 1. Loop through port range
    # 2. Test each port using test_tcp_connection()
    # 3. Log which ports are open
    # 4. Return list of open ports
    
    logger.info(f"Scanning {base_ip} ports {start_port}-{end_port}")
    open_ports = []
    
    for port in range(start_port, end_port + 1):
        if test_tcp_connection(base_ip, port, timeout):
            open_ports.append(port)
            logger.info(f"Port {port} is open")
    
    logger.info(f"Scan complete. Found {len(open_ports)} open ports: {open_ports}")
    return open_ports

def test_network_targets():
    """Test connectivity to all configured targets"""
    config_env = config['development']
    targets = config_env['test_targets']
    timeout = config_env['network']['default_timeout']
    
    logger.info("=== NETWORK CONNECTIVITY TEST ===")
    
    results = {}
    successful_connections = 0
    total_connections = len(targets)
    
    for target_name, target_config in targets.items():
        host = target_config['host']
        port = target_config['port']
        description = target_config['description']
        
        logger.info(f"Testing {target_name}: {description}")
        
        if test_tcp_connection(host, port, timeout):
            results[target_name] = True
            successful_connections += 1
        else:
            results[target_name] = False
        
    # TODO: Generate summary report
    # Your code here
    # Hint: Calculate success rate
    # Hint: Log summary statistics
    success_rate = (successful_connections / total_connections) * 100
    logger.info(f"=== CONNECTIVITY TEST SUMMARY ===")
    logger.info(f"Successful connections: {successful_connections}/{total_connections} ({success_rate:.1f}%)")
    
    logger.info("=== CONNECTIVITY TEST COMPLETE ===")
    return results

def demonstrate_network_concepts():
    """Educational demonstration of network concepts"""
    logger.info("=== NETWORK CONCEPTS DEMONSTRATION ===")
    
    # Demonstrate different types of network failures
    logger.info("1. Testing unreachable host (should timeout)")
    test_tcp_connection("***************", 80, timeout=2)
    
    logger.info("2. Testing invalid hostname (should fail DNS)")
    test_tcp_connection("this-host-does-not-exist.invalid", 80, timeout=2)
    
    logger.info("3. Testing closed port on reachable host")
    test_tcp_connection("*******", 12345, timeout=2)
    
    logger.info("4. Testing open port on reachable host")
    test_tcp_connection("*******", 53, timeout=2)

def main():
    """Phase 1: Network Fundamentals Practice"""
    logger.info("=== PHASE 1: NETWORK FUNDAMENTALS ===")
    
    # Test 1: Basic connectivity testing
    logger.info("Test 1: Basic connectivity testing")
    test_network_targets()
    
    # Test 2: Network concepts demonstration
    logger.info("Test 2: Network concepts demonstration")
    demonstrate_network_concepts()
    
    # Test 3: Port scanning (educational)
    logger.info("Test 3: Port scanning demonstration")
    # Scan common ports on localhost
    scan_network_range("127.0.0.1", 7990, 8001, timeout=0.5)
    
    logger.info("Phase 1 complete! Ready for Phase 2 (Modbus Protocol)")
    logger.info("Next step: Run 'python phase2_data_conversion.py'")

if __name__ == "__main__":
    main()
