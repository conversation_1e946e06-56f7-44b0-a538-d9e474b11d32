"""Configuration Generation"""
import os
import yaml
import logging
from datetime import datetime
from urllib.parse import urlparse
from .field_normalizer import FieldNameNormalizer

log = logging.getLogger(__name__)


class ConfigGenerator:
    """Generates machine configuration from discovered nodes."""
    
    def __init__(self, server_url):
        """
        Initialize the configuration generator.
        
        Args:
            server_url (str): OPC UA server URL
        """
        self.server_url = server_url
    
    def generate_machine_config(self, discovered_nodes, machine_name=None, machine_type="unknown"):
        """
        Generate a machine configuration dictionary from discovered nodes.
        
        This method coordinates the entire configuration generation process:
        1. Determine machine name (use provided or generate default)
        2. Extract and filter namespaces (exclude system namespace 0)
        3. Process discovered nodes to create clean field name mappings
        4. Build the final configuration structure
        
        Args:
            discovered_nodes (dict): Dictionary of discovered nodes
            machine_name (str): Name for the machine (optional)
            machine_type (str): Type of machine (e.g., '3d_printer', 'cnc_machine')
            
        Returns:
            dict: Machine configuration dictionary ready for YAML export
        """
        log.info("Starting machine configuration generation...")

        if not machine_name:
            machine_name = self._generate_default_machine_name()
            log.info(f"No machine name provided, using default: '{machine_name}'")
        
        discovered_namespaces = self._extract_namespaces(discovered_nodes)
        selected_nodes = self._process_discovered_nodes(discovered_nodes)
        
        machine_config = self._create_machine_config_structure(
            machine_name, machine_type, discovered_namespaces, selected_nodes
        )
        
        log.info(f"Generated config for machine '{machine_name}' with {len(selected_nodes)} nodes from namespaces {discovered_namespaces}")
        return machine_config
    
    def _extract_namespaces(self, discovered_nodes):
        """
        Extract and sort all non-default namespaces from discovered nodes.
        
        Args:
            discovered_nodes (dict): Dictionary of discovered nodes
            
        Returns:
            list: Sorted list of namespace indices (excluding ns=0)
        """
        namespaces = set()
        for node_info in discovered_nodes.values():
            if 'namespace' in node_info:
                namespaces.add(node_info.get('namespace'))
        
        log.debug(f"Discovered unordered non-default namespaces: {namespaces}")
        
        # Filter out system namespace (0) and return sorted list
        discovered_namespaces = sorted(ns for ns in namespaces if ns > 0)
        log.info(f"Discovered sorted non-default namespaces: {discovered_namespaces}")
        return discovered_namespaces
    
    def _process_discovered_nodes(self, discovered_nodes):
        """
        Process all discovered nodes and convert them into clean field name -> node_id mappings.
        
        This function orchestrates the field name generation process:
        1. For each discovered node, extract and normalize the field name
        2. Resolve any naming collisions
        3. Build the final mapping of field names to node IDs
        
        Args:
            discovered_nodes (dict): Dictionary of discovered nodes
            
        Returns:
            dict: Mapping of field names to node IDs (e.g., {"temperature": "ns=4;i=234"})
        """
        selected_nodes = {}
        log.info(f"Processing {len(discovered_nodes)} discovered nodes...")
        
        for path, node_info in discovered_nodes.items():
            node_id = node_info.get('node_id')
            field_name = FieldNameNormalizer.normalize_field_name(path)
            unique_field_name = FieldNameNormalizer.resolve_field_name_collisions(
                selected_nodes, field_name, path, node_id
            )
            selected_nodes[unique_field_name] = node_id
            log.debug(f"Mapped field '{unique_field_name}' -> node '{node_id}' (from path: {path})")
        
        log.info(f"Generated {len(selected_nodes)} field name mappings")
        return selected_nodes
    
    def _generate_default_machine_name(self):
        """
        Generate a default machine name based on the server URL.
        
        Returns:
            str: Default machine name (e.g., "Machine_localhost_4840")
        """
        parsed_url = urlparse(self.server_url)
        return f"Machine_{parsed_url.hostname}_{parsed_url.port}"
    
    def _create_machine_config_structure(self, machine_name, machine_type, discovered_namespaces, selected_nodes):
        """
        Create the final machine configuration dictionary structure.
        
        Args:
            machine_name (str): Name of the machine
            machine_type (str): Type of machine
            discovered_namespaces (list): List of discovered namespace indices
            selected_nodes (dict): Mapping of field names to node IDs
            
        Returns:
            dict: Complete machine configuration dictionary
        """
        return {
            machine_name: {
                "type": machine_type,
                "opcua": {
                    "url": self.server_url,
                    "namespaces": discovered_namespaces,
                    "authentication": {
                        "enabled": False,
                        "username": None,
                        "password": None
                    }
                },
                "nodes": selected_nodes,
                "monitoring": {
                    "subscription_interval_ms": 1000,
                    "connection_timeout_seconds": 10,
                    "retry_attempts": 3,
                    "retry_delay_seconds": 5
                }
            }
        }
    
    def save_config_to_file(self, config, filename=None):
        """
        Save the configuration to a YAML file.
        
        Args:
            config (dict): Configuration dictionary
            filename (str): Output filename (optional)
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            parsed_url = urlparse(self.server_url)
            filename = f"discovered_config_{parsed_url.hostname}_{parsed_url.port}_{timestamp}.yaml"
        
        try:
            # Try to load existing config and merge
            existing_config = {}
            if os.path.exists("config.yaml"):
                try:
                    with open("config.yaml", 'r') as f:
                        existing_config = yaml.safe_load(f) or {}
                    log.info("Loaded existing config.yaml")
                except Exception as e:
                    log.warning(f"Could not load existing config.yaml: {e}")
            
            # Create development.machines structure if it doesn't exist
            if 'development' not in existing_config:
                existing_config['development'] = {}
            if 'machines' not in existing_config['development']:
                existing_config['development']['machines'] = {}
            
            # Merge the new machine config
            existing_config['development']['machines'].update(config)
            
            # Save to file
            with open(filename, 'w') as f:
                yaml.dump(existing_config, f, default_flow_style=False, indent=2)
            
            log.info(f"Configuration saved to: {filename}")
            
        except Exception as e:
            log.error(f"Error saving configuration to file: {e}")
