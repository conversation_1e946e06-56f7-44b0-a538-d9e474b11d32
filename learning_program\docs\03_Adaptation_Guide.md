# Adapting This Project for Different Machines

## Step-by-Step Adaptation Guide

### Phase 1: Understanding Your Target Machine

#### 1.1 Identify Communication Protocols
**Questions to Ask:**
- Does your machine support OPC UA?
- What other protocols are available? (Modbus, MQTT, REST API)
- Are there existing integrations or documentation?
- What network access do you have?

**How to Find Out:**
```bash
# Network discovery - find what ports are open
nmap -p 1-65535 <machine_ip_address>

# Common industrial ports:
# 4840: OPC UA
# 502: Modbus TCP  
# 1883: MQTT
# 80/443: HTTP/HTTPS APIs
```

#### 1.2 Discover Available Data Points
**For OPC UA Machines:**
```python
# Use UaExpert (free OPC UA client) or this Python script:
from asyncua import Client

async def discover_nodes():
    async with <PERSON>lient(url="opc.tcp://your_machine_ip:4840/") as client:
        root = client.nodes.objects
        await print_node_tree(root, level=0)

async def print_node_tree(node, level):
    children = await node.get_children()
    for child in children:
        name = await child.read_browse_name()
        print("  " * level + f"{name}")
        await print_node_tree(child, level + 1)
```

### Phase 2: Copying and Modifying the Code Structure

#### 2.1 Create Your Project Structure
```
your_project/
├── config/
│   ├── env.yaml              # Database credentials
│   └── machine_config.yaml   # Machine-specific settings
├── data_collection/
│   ├── opcua_collector.py    # Based on main.py from EOS folder
│   ├── modbus_collector.py   # Based on main.py from power meter folder
│   └── custom_collector.py   # For your specific protocols
├── analysis/
│   ├── basic_plots.py        # Based on pull_data.py
│   ├── process_analysis.py   # Based on compare_m290.py
│   └── custom_analysis.py    # Your specific analysis needs
├── docs/
│   ├── machine_manual.md     # Document your machine's specifics
│   └── deployment_guide.md   # How to set up your system
└── tests/
    ├── test_connection.py    # Basic connectivity tests
    └── test_data_flow.py     # End-to-end testing
```

#### 2.2 Configuration Template

**config/machine_config.yaml**
```yaml
# Machine-specific configuration
machine:
  name: "YOUR_MACHINE_NAME"
  type: "CNC_MILL"  # or LASER_CUTTER, INJECTION_MOLDER, etc.
  serial_number: "ABC123"
  location: "Factory_Floor_A"

# Communication settings
communication:
  primary:
    protocol: "opcua"  # or "modbus", "mqtt", "http"
    host: "*************"
    port: 4840
    authentication:
      username: "operator"
      password: "password123"
  
  secondary:  # For power monitoring, etc.
    protocol: "modbus"
    host: "*************"
    port: 502

# Data collection settings
data_collection:
  interval_seconds: 5
  batch_size: 100
  timeout_seconds: 30
  
# What data to collect
sensors:
  critical:  # Always collect these
    - "temperature"
    - "pressure"
    - "machine_status"
  
  standard:  # Collect these normally
    - "vibration"
    - "power_consumption"
    - "cycle_count"
    
  detailed:  # Collect these occasionally for detailed analysis
    - "acoustic_signature"
    - "detailed_diagnostics"

# Data filtering rules (like update_criteria in original)
filtering:
  temperature:
    max_diff: 5.0      # Only upload if change > 5°C
    min_time: 30       # Wait at least 30s between uploads
    max_time: 300      # Force upload after 5 minutes
  
  pressure:
    max_diff: 0.5      # Only upload if change > 0.5 bar
    min_time: 60
    max_time: 600
```

### Phase 3: Modifying the Data Collection Code

#### 3.1 Generic OPC UA Collector Template

**data_collection/opcua_collector.py**
```python
import yaml
import asyncio
from asyncua import Client, Node
from datetime import datetime
import logging

# Load your machine configuration
with open("config/machine_config.yaml") as f:
    config = yaml.safe_load(f)

class GenericOPCUACollector:
    def __init__(self, config):
        self.config = config
        self.machine_name = config['machine']['name']
        self.client = None
        self.subscription = None
        
    async def connect(self):
        """Connect to OPC UA server"""
        comm_config = self.config['communication']['primary']
        
        if comm_config.get('authentication'):
            auth = comm_config['authentication']
            url = f"opc.tcp://{auth['username']}:{auth['password']}@{comm_config['host']}:{comm_config['port']}/"
        else:
            url = f"opc.tcp://{comm_config['host']}:{comm_config['port']}/"
            
        self.client = Client(url=url)
        await self.client.connect()
        
    async def discover_and_subscribe(self):
        """Find relevant nodes and subscribe to them"""
        # This is where you'd adapt the node discovery logic
        # from the original EOS script to your machine
        
        # Example for a generic machine:
        try:
            # Look for common industrial node patterns
            root_nodes = [
                "Machine",
                "Process", 
                "Sensors",
                "Status"
            ]
            
            all_nodes = []
            for root_name in root_nodes:
                try:
                    root = await self.client.nodes.objects.get_child([root_name])
                    nodes = await self.get_all_children(root)
                    all_nodes.extend(nodes)
                except:
                    logging.warning(f"Root node '{root_name}' not found")
                    
            # Create subscription
            handler = DataChangeHandler(self.config)
            self.subscription = await self.client.create_subscription(
                self.config['data_collection']['interval_seconds'] * 1000, 
                handler
            )
            
            await self.subscription.subscribe_data_change(all_nodes)
            
        except Exception as e:
            logging.error(f"Failed to set up subscription: {e}")
            
    async def get_all_children(self, node):
        """Recursively get all child nodes"""
        children = []
        try:
            direct_children = await node.get_children()
            for child in direct_children:
                children.append(child)
                # Recursively get grandchildren
                grandchildren = await self.get_all_children(child)
                children.extend(grandchildren)
        except:
            pass
        return children

class DataChangeHandler:
    def __init__(self, config):
        self.config = config
        self.filtering_rules = config.get('filtering', {})
        self.last_upload_times = {}
        self.last_values = {}
        
    def datachange_notification(self, node: Node, val, data):
        """Handle incoming data changes"""
        node_id = str(node.nodeid.Identifier)
        
        # Apply your filtering logic here
        should_upload = self.should_upload_data(node_id, val)
        
        if should_upload:
            self.upload_to_database(node_id, val, data.monitored_item.Value.SourceTimestamp)
            
    def should_upload_data(self, node_id, value):
        """Implement your filtering logic"""
        # Similar to update_criteria in original code
        rules = self.filtering_rules.get(node_id, {})
        
        # Check time constraints
        now = time.time()
        last_upload = self.last_upload_times.get(node_id, 0)
        time_since_upload = now - last_upload
        
        min_time = rules.get('min_time', 0)
        max_time = rules.get('max_time', float('inf'))
        
        if time_since_upload < min_time:
            return False
            
        if time_since_upload > max_time:
            return True
            
        # Check value change constraints
        max_diff = rules.get('max_diff')
        if max_diff is not None:
            last_value = self.last_values.get(node_id)
            if last_value is not None:
                if abs(value - last_value) < max_diff:
                    return False
                    
        return True
        
    def upload_to_database(self, node_id, value, timestamp):
        """Upload data to InfluxDB"""
        # Use the same InfluxDB upload logic from original
        from influxdb_client import Point
        
        point = Point("machine_data") \
            .tag("machine", self.config['machine']['name']) \
            .tag("sensor", node_id) \
            .field("value", value) \
            .time(timestamp)
            
        # Upload to InfluxDB (implement similar to original)
        pass
```

### Phase 4: Testing and Validation

#### 4.1 Connection Test Script

**tests/test_connection.py**
```python
import asyncio
import yaml
from asyncua import Client

async def test_opcua_connection():
    """Test basic OPC UA connectivity"""
    with open("config/machine_config.yaml") as f:
        config = yaml.safe_load(f)
    
    comm = config['communication']['primary']
    url = f"opc.tcp://{comm['host']}:{comm['port']}/"
    
    try:
        async with Client(url=url) as client:
            # Test basic operations
            server_time = await client.get_node("i=2258").read_value()  # Server timestamp
            print(f"✅ Connected successfully! Server time: {server_time}")
            
            # Try to browse root objects
            objects = client.nodes.objects
            children = await objects.get_children()
            print(f"✅ Found {len(children)} root objects")
            
            for child in children[:5]:  # Show first 5
                name = await child.read_browse_name()
                print(f"  - {name}")
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_opcua_connection())
```

### Phase 5: Deployment Checklist

#### 5.1 Pre-deployment Verification
- [ ] Can connect to machine reliably
- [ ] Identified all relevant data points  
- [ ] Tested data collection for 24+ hours
- [ ] Verified InfluxDB connectivity
- [ ] Set up monitoring/alerting for script health
- [ ] Documented any machine-specific quirks

#### 5.2 Common Adaptation Challenges

**Authentication Issues:**
```python
# Different machines use different auth methods
# EOS uses username:token, others might use certificates

# Certificate-based auth:
await client.set_security_string("Basic256Sha256,SignAndEncrypt,certificate.pem,private_key.pem")

# Anonymous access:
client = Client(url="opc.tcp://machine_ip:4840/", timeout=4)
```

**Node Structure Differences:**
```python
# EOS machines: EOS.Machine.Sensors.Temperature
# Siemens PLCs: DB1.Temperature
# Fanuc CNCs: Status.SpindleTemp
# Custom machines: Process.Cell1.Temp

# Make your code flexible:
def find_temperature_nodes(client):
    patterns = [
        "**/*temp*",
        "**/*Temperature*", 
        "**/temp",
        "**/Temp"
    ]
    # Search with multiple patterns
```

**Data Type Variations:**
```python
# Some machines return strings, others floats
def safe_convert_value(value):
    if isinstance(value, str):
        try:
            return float(value)
        except ValueError:
            return None
    return value
```

## Machine-Specific Examples

### CNC Machine Adaptation
```yaml
# Focus on spindle data, tool wear, part counts
sensors:
  critical:
    - "spindle_speed"
    - "spindle_load"
    - "coolant_pressure"
  standard:
    - "part_count"
    - "tool_position"
    - "axis_positions"
```

### Injection Molding Machine
```yaml
# Focus on pressure, temperature cycles
sensors:
  critical:
    - "injection_pressure"
    - "barrel_temperature"
    - "cycle_time"
  standard:
    - "shot_count"
    - "mold_temperature"
    - "clamp_force"
```

### Laser Cutting Machine  
```yaml
# Focus on laser power, gas pressure, material handling
sensors:
  critical:
    - "laser_power"
    - "assist_gas_pressure"
    - "cutting_head_height"
  standard:
    - "sheet_position"
    - "cut_quality_metrics"
```

Remember: **Start simple!** Get basic connectivity and a few key sensors working first, then gradually add more sophisticated features.
