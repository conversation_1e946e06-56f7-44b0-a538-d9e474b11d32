# https://github.com/FreeOpcUa/opcua-asyncio

import time
import asyncio
from asyncua import Client

async def send_command():
    #async with <PERSON><PERSON>(url='opc.tcp://10.100.113.104:4843/') as client:
    async with <PERSON><PERSON>(url='opc.tcp://client1:DbTM83LoagK2Wnxi1SIXUjTOmbWd9bxzbGB8XzknbmM=@10.100.113.104:4843/') as client:
    
        node = client.get_node('ns=4;s=EOS.Machine.Jobs.PauseCommand.StateDetailed')
        #print(node.__dir__())
        print(await node.read_data_value())

async def print_values():
    #async with <PERSON>lient(url='opc.tcp://10.100.113.104:4843/') as client:
    async with <PERSON>lient(url='opc.tcp://client1:DbTM83LoagK2Wnxi1SIXUjTOmbWd9bxzbGB8XzknbmM=@10.100.113.104:4843/') as client:
    
        #node = client.get_node('ns=4;s=EOS.Machine.Recoater.AxisTorque')
        node = client.get_node('ns=4;s=EOS.Machine.ProcessChamber.HumidityAbsolute')
        #node = await client.nodes.objects.get_child(["4:V2",'4:EOS','4:Machine','4:Info','4:SerialNumber'])
        
        print(node) # ns=4;s=EOS.Machine.Jobs.Last.HeightTotal
        print(await node.read_value()) # SI4490
        print(await node.get_value()) # SI4490

        print(await node.read_data_value()) # DataValue(Value=Variant(Value='SI4490', VariantType=<VariantType.String: 12>, Dimensions=None, is_array=False), StatusCode_=StatusCode(value=0), SourceTimestamp=datetime.datetime(2022, 2, 7, 10, 13, 52, 472390), ServerTimestamp=None, SourcePicoseconds=None, ServerPicoseconds=None)
        print((await node.read_data_value()).SourceTimestamp) # <class 'datetime.datetime'> 2022-02-07 10:13:52.462221
        print(await node.read_value_rank()) # ValueRank.Any
        
        print(await node.get_references())
        print(await node.read_display_name()) # LocalizedText(Locale=None, Text='SerialNumber')
        print(await node.read_browse_name()) # QualifiedName(NamespaceIndex=4, Name='SerialNumber')
        print(await node.read_data_type()) # NodeId(Identifier=12, NamespaceIndex=0, NodeIdType=<NodeIdType.TwoByte: 0>)
        
    
        while True:
            # Do something with client
            #node = client.get_node('i=85')
            
            value = await node.read_value()
            print(value)
            time.sleep(0.05)
            print('Cycling')
            
def main():
    loop = asyncio.get_event_loop()
    loop.run_until_complete(print_values())
    #loop.run_until_complete(send_command())
    loop.close()

if __name__ == '__main__':

    main()