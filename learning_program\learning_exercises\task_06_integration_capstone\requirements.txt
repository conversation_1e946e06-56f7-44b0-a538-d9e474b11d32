# Core async programming and system integration
asyncio>=3.4.3

# OPC UA client library for industrial communication
asyncua>=1.0.0

# Modbus client library for power meters and devices
pyModbusTCP>=0.2.0

# InfluxDB client for time series data storage
influxdb-client>=1.36.0

# Configuration file handling
PyYAML>=6.0

# Data analysis and visualization
matplotlib>=3.5.0
numpy>=1.21.0
pandas>=1.3.0

# Network discovery and scanning
python-nmap>=0.7.1
scapy>=2.4.5

# System monitoring and health checking
psutil>=5.8.0

# HTTP client for REST API testing
requests>=2.28.0
aiohttp>=3.8.0

# Email and alerting
smtplib-ssl>=1.0.0

# Async file operations
aiofiles>=0.8.0

# Logging and monitoring
structlog>=22.0.0

# For datetime handling
python-dateutil>=2.8.0

# Cryptography for secure connections
cryptography>=3.4.0

# Template engine for documentation generation
Jinja2>=3.1.0

# Command line interface
click>=8.0.0

# Testing framework
pytest>=7.0.0
pytest-asyncio>=0.21.0

# Development and debugging tools
ipython>=8.0.0
pdb++>=0.10.0

# Performance monitoring
memory-profiler>=0.60.0
line-profiler>=4.0.0

# Documentation generation
mkdocs>=1.4.0
mkdocs-material>=8.5.0

# Data validation
pydantic>=1.10.0

# Environment variable management
python-dotenv>=0.19.0

# Process management for production deployment
supervisor>=4.2.0

# Database utilities (for advanced InfluxDB operations)
sqlalchemy>=1.4.0

# Serialization for complex data structures
pickle5>=0.0.12

# Advanced networking utilities
netaddr>=0.8.0
ipaddress>=1.0.23

# Time zone handling
pytz>=2022.1

# Advanced plotting and dashboards
plotly>=5.11.0
dash>=2.7.0

# Machine learning for predictive maintenance (optional)
scikit-learn>=1.1.0

# Image processing for documentation (optional)
Pillow>=9.3.0
