2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Config loaded: {'development': {'influxdb': {'url': 'http://localhost:8086', 'token': 'your-development-token', 'org': 'your-org', 'bucket': 'manufacturing_sensors'}, 'sensors': {'EOS_SI3654_temp': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}, 'field_name': 'temperature_celsius', 'min_value': 200.0, 'max_value': 280.0, 'normal_range': [240.0, 260.0], 'response_time_seconds': 1.5, 'failure_rate': 0.02}, 'EOS_SI3654_pressure': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}, 'field_name': 'pressure_bar', 'min_value': 0.8, 'max_value': 1.2, 'normal_range': [0.95, 1.05], 'response_time_seconds': 2.0, 'failure_rate': 0.01}, 'EOS_SI3654_power': {'measurement': 'power_consumption', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'source': 'power_meter'}, 'field_name': 'power_kw', 'min_value': 5.0, 'max_value': 25.0, 'normal_range': [12.0, 18.0], 'response_time_seconds': 1.0, 'failure_rate': 0.015}}, 'simulation': {'duration_minutes': 10, 'collection_interval_seconds': 2, 'enable_data_quality_issues': True, 'enable_network_failures': True, 'batch_write_size': 10}, 'analysis': {'anomaly_detection': {'std_dev_threshold': 2.5, 'window_size_minutes': 30}, 'reporting': {'dashboard_update_interval_seconds': 30, 'generate_hourly_reports': True}}}, 'production': {'influxdb': {'url': 'http://influxdb.ami.modelfactory.sg:8086', 'token': 'your-production-token', 'org': 'AMI', 'bucket': 'ami_eqpt'}}}
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Client created: <influxdb_client.client.influxdb_client.InfluxDBClient object at 0x00000261E83138C0>
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Write API created: <influxdb_client.client.write_api.WriteApi object at 0x00000261E84AC440>
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Point created: 
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber'}
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Fields added: {'temperature_celsius': 245.6}
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Timestamp added: 2025-08-05 07:38:33.519277+00:00
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Point created: 
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Timestamp added: 2025-08-05 07:38:33.519277+00:00
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Point created: 
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Tags added: {'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:38:33	phase1_time_series_basics	4568	DEBUG	Timestamp added: 2025-08-05 07:38:33.519277+00:00
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Config loaded: {'development': {'influxdb': {'url': 'http://localhost:8086', 'token': 'your-development-token', 'org': 'your-org', 'bucket': 'manufacturing_sensors'}, 'sensors': {'EOS_SI3654_temp': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}, 'field_name': 'temperature_celsius', 'min_value': 200.0, 'max_value': 280.0, 'normal_range': [240.0, 260.0], 'response_time_seconds': 1.5, 'failure_rate': 0.02}, 'EOS_SI3654_pressure': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}, 'field_name': 'pressure_bar', 'min_value': 0.8, 'max_value': 1.2, 'normal_range': [0.95, 1.05], 'response_time_seconds': 2.0, 'failure_rate': 0.01}, 'EOS_SI3654_power': {'measurement': 'power_consumption', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'source': 'power_meter'}, 'field_name': 'power_kw', 'min_value': 5.0, 'max_value': 25.0, 'normal_range': [12.0, 18.0], 'response_time_seconds': 1.0, 'failure_rate': 0.015}}, 'simulation': {'duration_minutes': 10, 'collection_interval_seconds': 2, 'enable_data_quality_issues': True, 'enable_network_failures': True, 'batch_write_size': 10}, 'analysis': {'anomaly_detection': {'std_dev_threshold': 2.5, 'window_size_minutes': 30}, 'reporting': {'dashboard_update_interval_seconds': 30, 'generate_hourly_reports': True}}}, 'production': {'influxdb': {'url': 'http://influxdb.ami.modelfactory.sg:8086', 'token': 'your-production-token', 'org': 'AMI', 'bucket': 'ami_eqpt'}}}
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Client created: <influxdb_client.client.influxdb_client.InfluxDBClient object at 0x0000023EFF323A10>
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Write API created: <influxdb_client.client.write_api.WriteApi object at 0x0000023EFF4B4590>
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Point created: 
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber'}
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Fields added: {'temperature_celsius': 245.6}
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Timestamp added: 2025-08-05 07:41:39.141334+00:00
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Point created: 
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Timestamp added: 2025-08-05 07:41:39.141334+00:00
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Point created: 
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Tags added: {'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Timestamp added: 2025-08-05 07:41:39.141334+00:00
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Validating 245.6 for temperature
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Value (245.6) is valid and within normal range for temperature
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Value is -999.0. Check against list of error codes.
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Validating 500.0 for temperature
2025-08-05 15:41:39	phase1_time_series_basics	19952	CRITICAL	Value (500.0) out of range for temperature
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Value is None
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Value is not a number
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Validating 250.0 for temperature
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Value (250.0) is valid and within normal range for temperature
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Point created: 
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Fields added: {'temperature_celsius': 250.0}
2025-08-05 15:41:39	phase1_time_series_basics	19952	DEBUG	Timestamp added: 2025-08-05 07:41:39.147482+00:00
2025-08-05 15:41:43	phase1_time_series_basics	19952	ERROR	Error writing data point: <urllib3.connection.HTTPConnection object at 0x0000023EFF4B4D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it
2025-08-05 15:41:43	phase1_time_series_basics	19952	DEBUG	Validating 1.0 for pressure
2025-08-05 15:41:43	phase1_time_series_basics	19952	DEBUG	Value (1.0) is valid and within normal range for pressure
2025-08-05 15:41:43	phase1_time_series_basics	19952	DEBUG	Point created: 
2025-08-05 15:41:43	phase1_time_series_basics	19952	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 15:41:43	phase1_time_series_basics	19952	DEBUG	Fields added: {'pressure_bar': 1.0}
2025-08-05 15:41:43	phase1_time_series_basics	19952	DEBUG	Timestamp added: 2025-08-05 07:41:39.147487+00:00
2025-08-05 15:41:47	phase1_time_series_basics	19952	ERROR	Error writing data point: <urllib3.connection.HTTPConnection object at 0x0000023EFF363B10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it
2025-08-05 15:41:47	phase1_time_series_basics	19952	ERROR	Error writing data point: 'sensor_type'
2025-08-05 15:41:47	phase1_time_series_basics	19952	DEBUG	Validating 260.0 for temperature
2025-08-05 15:41:47	phase1_time_series_basics	19952	DEBUG	Value (260.0) is valid and within normal range for temperature
2025-08-05 15:41:47	phase1_time_series_basics	19952	DEBUG	Point created: 
2025-08-05 15:41:47	phase1_time_series_basics	19952	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 15:41:47	phase1_time_series_basics	19952	DEBUG	Fields added: {'temperature_celsius': 260.0}
2025-08-05 15:41:47	phase1_time_series_basics	19952	DEBUG	Timestamp added: 2025-08-05 07:41:39.147489+00:00
2025-08-05 15:41:51	phase1_time_series_basics	19952	ERROR	Error writing data point: <urllib3.connection.HTTPConnection object at 0x0000023EFF363D90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it
2025-08-05 15:41:51	phase1_time_series_basics	19952	DEBUG	Validating 0.85 for pressure
2025-08-05 15:41:51	phase1_time_series_basics	19952	WARNING	Value (0.85) is valid, but out of normal range for pressure
2025-08-05 15:41:51	phase1_time_series_basics	19952	DEBUG	Value (0.85) is valid and within normal range for pressure
2025-08-05 15:41:51	phase1_time_series_basics	19952	DEBUG	Point created: 
2025-08-05 15:41:51	phase1_time_series_basics	19952	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 15:41:51	phase1_time_series_basics	19952	DEBUG	Fields added: {'pressure_bar': 0.85}
2025-08-05 15:41:51	phase1_time_series_basics	19952	DEBUG	Timestamp added: 2025-08-05 07:41:39.147490+00:00
2025-08-05 15:41:55	phase1_time_series_basics	19952	ERROR	Error writing data point: <urllib3.connection.HTTPConnection object at 0x0000023EFF3E35C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Config loaded: {'development': {'influxdb': {'url': 'http://localhost:8086', 'token': 'your-development-token', 'org': 'your-org', 'bucket': 'manufacturing_sensors'}, 'sensors': {'EOS_SI3654_temp': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}, 'field_name': 'temperature_celsius', 'min_value': 200.0, 'max_value': 280.0, 'normal_range': [240.0, 260.0], 'response_time_seconds': 1.5, 'failure_rate': 0.02}, 'EOS_SI3654_pressure': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}, 'field_name': 'pressure_bar', 'min_value': 0.8, 'max_value': 1.2, 'normal_range': [0.95, 1.05], 'response_time_seconds': 2.0, 'failure_rate': 0.01}, 'EOS_SI3654_power': {'measurement': 'power_consumption', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'source': 'power_meter'}, 'field_name': 'power_kw', 'min_value': 5.0, 'max_value': 25.0, 'normal_range': [12.0, 18.0], 'response_time_seconds': 1.0, 'failure_rate': 0.015}}, 'simulation': {'duration_minutes': 10, 'collection_interval_seconds': 2, 'enable_data_quality_issues': True, 'enable_network_failures': True, 'batch_write_size': 10}, 'analysis': {'anomaly_detection': {'std_dev_threshold': 2.5, 'window_size_minutes': 30}, 'reporting': {'dashboard_update_interval_seconds': 30, 'generate_hourly_reports': True}}}, 'production': {'influxdb': {'url': 'http://influxdb.ami.modelfactory.sg:8086', 'token': 'your-production-token', 'org': 'AMI', 'bucket': 'ami_eqpt'}}}
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Client created: <influxdb_client.client.influxdb_client.InfluxDBClient object at 0x000001D51E223A10>
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Write API created: <influxdb_client.client.write_api.WriteApi object at 0x000001D51E3BC590>
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Point created: 
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber'}
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Fields added: {'temperature_celsius': 245.6}
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Timestamp added: 2025-08-05 07:55:14.865614+00:00
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Point created: 
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Timestamp added: 2025-08-05 07:55:14.865614+00:00
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Point created: 
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Tags added: {'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Timestamp added: 2025-08-05 07:55:14.865614+00:00
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Validating 245.6 for temperature
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Value (245.6) is valid and within normal range for temperature
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Value is -999.0. Check against list of error codes.
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Validating 500.0 for temperature
2025-08-05 15:55:14	phase1_time_series_basics	18124	CRITICAL	Value (500.0) out of range for temperature
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Value is None
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Value is not a number
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Validating 250.0 for temperature
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Value (250.0) is valid and within normal range for temperature
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Point created: 
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Fields added: {'temperature_celsius': 250.0}
2025-08-05 15:55:14	phase1_time_series_basics	18124	DEBUG	Timestamp added: 2025-08-05 07:55:14.878558+00:00
2025-08-05 15:55:15	phase1_time_series_basics	18124	ERROR	Error writing data point: (401)
Reason: Unauthorized
HTTP response headers: HTTPHeaderDict({'Content-Type': 'application/json; charset=utf-8', 'X-Influxdb-Build': 'OSS', 'X-Influxdb-Version': 'v2.7.12', 'X-Platform-Error-Code': 'unauthorized', 'Date': 'Tue, 05 Aug 2025 07:55:15 GMT', 'Content-Length': '55'})
HTTP response body: {"code":"unauthorized","message":"unauthorized access"}

2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Validating 1.0 for pressure
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Value (1.0) is valid and within normal range for pressure
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Point created: 
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Fields added: {'pressure_bar': 1.0}
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Timestamp added: 2025-08-05 07:55:14.878566+00:00
2025-08-05 15:55:15	phase1_time_series_basics	18124	ERROR	Error writing data point: (401)
Reason: Unauthorized
HTTP response headers: HTTPHeaderDict({'Content-Type': 'application/json; charset=utf-8', 'X-Influxdb-Build': 'OSS', 'X-Influxdb-Version': 'v2.7.12', 'X-Platform-Error-Code': 'unauthorized', 'Date': 'Tue, 05 Aug 2025 07:55:15 GMT', 'Content-Length': '55'})
HTTP response body: {"code":"unauthorized","message":"unauthorized access"}

2025-08-05 15:55:15	phase1_time_series_basics	18124	ERROR	Error writing data point: 'sensor_type'
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Validating 260.0 for temperature
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Value (260.0) is valid and within normal range for temperature
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Point created: 
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Fields added: {'temperature_celsius': 260.0}
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Timestamp added: 2025-08-05 07:55:14.878569+00:00
2025-08-05 15:55:15	phase1_time_series_basics	18124	ERROR	Error writing data point: (401)
Reason: Unauthorized
HTTP response headers: HTTPHeaderDict({'Content-Type': 'application/json; charset=utf-8', 'X-Influxdb-Build': 'OSS', 'X-Influxdb-Version': 'v2.7.12', 'X-Platform-Error-Code': 'unauthorized', 'Date': 'Tue, 05 Aug 2025 07:55:15 GMT', 'Content-Length': '55'})
HTTP response body: {"code":"unauthorized","message":"unauthorized access"}

2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Validating 0.85 for pressure
2025-08-05 15:55:15	phase1_time_series_basics	18124	WARNING	Value (0.85) is valid, but out of normal range for pressure
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Value (0.85) is valid and within normal range for pressure
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Point created: 
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Fields added: {'pressure_bar': 0.85}
2025-08-05 15:55:15	phase1_time_series_basics	18124	DEBUG	Timestamp added: 2025-08-05 07:55:14.878571+00:00
2025-08-05 15:55:15	phase1_time_series_basics	18124	ERROR	Error writing data point: (401)
Reason: Unauthorized
HTTP response headers: HTTPHeaderDict({'Content-Type': 'application/json; charset=utf-8', 'X-Influxdb-Build': 'OSS', 'X-Influxdb-Version': 'v2.7.12', 'X-Platform-Error-Code': 'unauthorized', 'Date': 'Tue, 05 Aug 2025 07:55:15 GMT', 'Content-Length': '55'})
HTTP response body: {"code":"unauthorized","message":"unauthorized access"}

2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Config loaded: {'development': {'influxdb': {'url': 'http://localhost:8086', 'token': 'MyInitialAdminToken0==', 'org': 'docs', 'bucket': 'manufacturing_sensors'}, 'sensors': {'EOS_SI3654_temp': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}, 'field_name': 'temperature_celsius', 'min_value': 200.0, 'max_value': 280.0, 'normal_range': [240.0, 260.0], 'response_time_seconds': 1.5, 'failure_rate': 0.02}, 'EOS_SI3654_pressure': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}, 'field_name': 'pressure_bar', 'min_value': 0.8, 'max_value': 1.2, 'normal_range': [0.95, 1.05], 'response_time_seconds': 2.0, 'failure_rate': 0.01}, 'EOS_SI3654_power': {'measurement': 'power_consumption', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'source': 'power_meter'}, 'field_name': 'power_kw', 'min_value': 5.0, 'max_value': 25.0, 'normal_range': [12.0, 18.0], 'response_time_seconds': 1.0, 'failure_rate': 0.015}}, 'simulation': {'duration_minutes': 10, 'collection_interval_seconds': 2, 'enable_data_quality_issues': True, 'enable_network_failures': True, 'batch_write_size': 10}, 'analysis': {'anomaly_detection': {'std_dev_threshold': 2.5, 'window_size_minutes': 30}, 'reporting': {'dashboard_update_interval_seconds': 30, 'generate_hourly_reports': True}}}, 'production': {'influxdb': {'url': 'http://influxdb.ami.modelfactory.sg:8086', 'token': 'your-production-token', 'org': 'AMI', 'bucket': 'ami_eqpt'}}}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Client created: <influxdb_client.client.influxdb_client.InfluxDBClient object at 0x000001C6C0383A10>
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Write API created: <influxdb_client.client.write_api.WriteApi object at 0x000001C6C0514590>
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Point created: 
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber'}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Fields added: {'temperature_celsius': 245.6}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Timestamp added: 2025-08-05 07:57:23.397773+00:00
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Point created: 
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Timestamp added: 2025-08-05 07:57:23.397773+00:00
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Point created: 
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Tags added: {'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Timestamp added: 2025-08-05 07:57:23.397773+00:00
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Validating 245.6 for temperature
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Value (245.6) is valid and within normal range for temperature
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Value is -999.0. Check against list of error codes.
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Validating 500.0 for temperature
2025-08-05 15:57:23	phase1_time_series_basics	29400	CRITICAL	Value (500.0) out of range for temperature
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Value is None
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Value is not a number
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Validating 250.0 for temperature
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Value (250.0) is valid and within normal range for temperature
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Point created: 
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Fields added: {'temperature_celsius': 250.0}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Timestamp added: 2025-08-05 07:57:23.405484+00:00
2025-08-05 15:57:23	phase1_time_series_basics	29400	ERROR	Error writing data point: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Content-Type': 'application/json; charset=utf-8', 'X-Influxdb-Build': 'OSS', 'X-Influxdb-Version': 'v2.7.12', 'X-Platform-Error-Code': 'not found', 'Date': 'Tue, 05 Aug 2025 07:57:23 GMT', 'Content-Length': '75'})
HTTP response body: {"code":"not found","message":"bucket \"manufacturing_sensors\" not found"}

2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Validating 1.0 for pressure
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Value (1.0) is valid and within normal range for pressure
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Point created: 
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Fields added: {'pressure_bar': 1.0}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Timestamp added: 2025-08-05 07:57:23.405489+00:00
2025-08-05 15:57:23	phase1_time_series_basics	29400	ERROR	Error writing data point: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Content-Type': 'application/json; charset=utf-8', 'X-Influxdb-Build': 'OSS', 'X-Influxdb-Version': 'v2.7.12', 'X-Platform-Error-Code': 'not found', 'Date': 'Tue, 05 Aug 2025 07:57:23 GMT', 'Content-Length': '75'})
HTTP response body: {"code":"not found","message":"bucket \"manufacturing_sensors\" not found"}

2025-08-05 15:57:23	phase1_time_series_basics	29400	ERROR	Error writing data point: 'sensor_type'
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Validating 260.0 for temperature
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Value (260.0) is valid and within normal range for temperature
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Point created: 
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Fields added: {'temperature_celsius': 260.0}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Timestamp added: 2025-08-05 07:57:23.405491+00:00
2025-08-05 15:57:23	phase1_time_series_basics	29400	ERROR	Error writing data point: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Content-Type': 'application/json; charset=utf-8', 'X-Influxdb-Build': 'OSS', 'X-Influxdb-Version': 'v2.7.12', 'X-Platform-Error-Code': 'not found', 'Date': 'Tue, 05 Aug 2025 07:57:23 GMT', 'Content-Length': '75'})
HTTP response body: {"code":"not found","message":"bucket \"manufacturing_sensors\" not found"}

2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Validating 0.85 for pressure
2025-08-05 15:57:23	phase1_time_series_basics	29400	WARNING	Value (0.85) is valid, but out of normal range for pressure
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Value (0.85) is valid and within normal range for pressure
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Point created: 
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Fields added: {'pressure_bar': 0.85}
2025-08-05 15:57:23	phase1_time_series_basics	29400	DEBUG	Timestamp added: 2025-08-05 07:57:23.405492+00:00
2025-08-05 15:57:23	phase1_time_series_basics	29400	ERROR	Error writing data point: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Content-Type': 'application/json; charset=utf-8', 'X-Influxdb-Build': 'OSS', 'X-Influxdb-Version': 'v2.7.12', 'X-Platform-Error-Code': 'not found', 'Date': 'Tue, 05 Aug 2025 07:57:23 GMT', 'Content-Length': '75'})
HTTP response body: {"code":"not found","message":"bucket \"manufacturing_sensors\" not found"}

2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Config loaded: {'development': {'influxdb': {'url': 'http://localhost:8086', 'token': 'MyInitialAdminToken0==', 'org': 'docs', 'bucket': 'phase4_manufacturing_sensors'}, 'sensors': {'EOS_SI3654_temp': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}, 'field_name': 'temperature_celsius', 'min_value': 200.0, 'max_value': 280.0, 'normal_range': [240.0, 260.0], 'response_time_seconds': 1.5, 'failure_rate': 0.02}, 'EOS_SI3654_pressure': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}, 'field_name': 'pressure_bar', 'min_value': 0.8, 'max_value': 1.2, 'normal_range': [0.95, 1.05], 'response_time_seconds': 2.0, 'failure_rate': 0.01}, 'EOS_SI3654_power': {'measurement': 'power_consumption', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'source': 'power_meter'}, 'field_name': 'power_kw', 'min_value': 5.0, 'max_value': 25.0, 'normal_range': [12.0, 18.0], 'response_time_seconds': 1.0, 'failure_rate': 0.015}}, 'simulation': {'duration_minutes': 10, 'collection_interval_seconds': 2, 'enable_data_quality_issues': True, 'enable_network_failures': True, 'batch_write_size': 10}, 'analysis': {'anomaly_detection': {'std_dev_threshold': 2.5, 'window_size_minutes': 30}, 'reporting': {'dashboard_update_interval_seconds': 30, 'generate_hourly_reports': True}}}, 'production': {'influxdb': {'url': 'http://influxdb.ami.modelfactory.sg:8086', 'token': 'your-production-token', 'org': 'AMI', 'bucket': 'ami_eqpt'}}}
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Client created: <influxdb_client.client.influxdb_client.InfluxDBClient object at 0x0000024EA2D73A10>
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Write API created: <influxdb_client.client.write_api.WriteApi object at 0x0000024EA2F0C590>
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Point created: 
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber'}
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Fields added: {'temperature_celsius': 245.6}
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Timestamp added: 2025-08-05 07:57:31.688222+00:00
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Point created: 
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Timestamp added: 2025-08-05 07:57:31.688222+00:00
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Point created: 
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Tags added: {'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Fields added: {'value': 245.6}
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Timestamp added: 2025-08-05 07:57:31.688222+00:00
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Validating 245.6 for temperature
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Value (245.6) is valid and within normal range for temperature
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Value is -999.0. Check against list of error codes.
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Validating 500.0 for temperature
2025-08-05 15:57:31	phase1_time_series_basics	31888	CRITICAL	Value (500.0) out of range for temperature
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Value is None
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Value is not a number
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Validating 250.0 for temperature
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Value (250.0) is valid and within normal range for temperature
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Point created: 
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Fields added: {'temperature_celsius': 250.0}
2025-08-05 15:57:31	phase1_time_series_basics	31888	DEBUG	Timestamp added: 2025-08-05 07:57:31.696753+00:00
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=250 1754380651696753000
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Validating 1.0 for pressure
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Value (1.0) is valid and within normal range for pressure
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Point created: 
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Fields added: {'pressure_bar': 1.0}
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Timestamp added: 2025-08-05 07:57:31.696759+00:00
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=1 1754380651696759000
2025-08-05 15:57:32	phase1_time_series_basics	31888	ERROR	Error writing data point: 'sensor_type'
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Validating 260.0 for temperature
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Value (260.0) is valid and within normal range for temperature
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Point created: 
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Fields added: {'temperature_celsius': 260.0}
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Timestamp added: 2025-08-05 07:57:31.696763+00:00
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=260 1754380651696763000
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Validating 0.85 for pressure
2025-08-05 15:57:32	phase1_time_series_basics	31888	WARNING	Value (0.85) is valid, but out of normal range for pressure
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Value (0.85) is valid and within normal range for pressure
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Point created: 
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Fields added: {'pressure_bar': 0.85}
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Timestamp added: 2025-08-05 07:57:31.696765+00:00
2025-08-05 15:57:32	phase1_time_series_basics	31888	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.85 1754380651696765000
2025-08-05 16:24:23	phase1_time_series_basics	32468	DEBUG	Config loaded: {'development': {'influxdb': {'url': 'http://localhost:8086', 'token': 'MyInitialAdminToken0==', 'org': 'docs', 'bucket': 'phase4_manufacturing_sensors'}, 'sensors': {'EOS_SI3654_temp': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}, 'field_name': 'temperature_celsius', 'min_value': 200.0, 'max_value': 280.0, 'normal_range': [240.0, 260.0], 'response_time_seconds': 1.5, 'failure_rate': 0.02}, 'EOS_SI3654_pressure': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}, 'field_name': 'pressure_bar', 'min_value': 0.8, 'max_value': 1.2, 'normal_range': [0.95, 1.05], 'response_time_seconds': 2.0, 'failure_rate': 0.01}, 'EOS_SI3654_power': {'measurement': 'power_consumption', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'source': 'power_meter'}, 'field_name': 'power_kw', 'min_value': 5.0, 'max_value': 25.0, 'normal_range': [12.0, 18.0], 'response_time_seconds': 1.0, 'failure_rate': 0.015}}, 'simulation': {'duration_minutes': 10, 'collection_interval_seconds': 2, 'enable_data_quality_issues': True, 'enable_network_failures': True, 'batch_write_size': 10}, 'analysis': {'anomaly_detection': {'std_dev_threshold': 2.5, 'window_size_minutes': 30}, 'reporting': {'dashboard_update_interval_seconds': 30, 'generate_hourly_reports': True}}}, 'production': {'influxdb': {'url': 'http://influxdb.ami.modelfactory.sg:8086', 'token': 'your-production-token', 'org': 'AMI', 'bucket': 'ami_eqpt'}}}
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Client created: <influxdb_client.client.influxdb_client.InfluxDBClient object at 0x000001F8C87F38C0>
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Write API created: <influxdb_client.client.write_api.WriteApi object at 0x000001F8C897C440>
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Point created: 
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber'}
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Fields added: {'temperature_celsius': 245.6}
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Timestamp added: 2025-08-05 08:24:24.010476+00:00
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Point created: 
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Fields added: {'value': 245.6}
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Timestamp added: 2025-08-05 08:24:24.010476+00:00
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Point created: 
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Tags added: {'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Fields added: {'value': 245.6}
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Timestamp added: 2025-08-05 08:24:24.010476+00:00
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Validating 245.6 for temperature
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Value (245.6) is valid and within normal range for temperature
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Value is -999.0. Check against list of error codes.
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Validating 500.0 for temperature
2025-08-05 16:24:24	phase1_time_series_basics	32468	CRITICAL	Value (500.0) out of range for temperature
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Value is None
2025-08-05 16:24:24	phase1_time_series_basics	32468	DEBUG	Value is not a number
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Config loaded: {'development': {'influxdb': {'url': 'http://localhost:8086', 'token': 'MyInitialAdminToken0==', 'org': 'docs', 'bucket': 'phase4_manufacturing_sensors'}, 'sensors': {'EOS_SI3654_temp': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}, 'field_name': 'temperature_celsius', 'min_value': 200.0, 'max_value': 280.0, 'normal_range': [240.0, 260.0], 'response_time_seconds': 1.5, 'failure_rate': 0.02}, 'EOS_SI3654_pressure': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}, 'field_name': 'pressure_bar', 'min_value': 0.8, 'max_value': 1.2, 'normal_range': [0.95, 1.05], 'response_time_seconds': 2.0, 'failure_rate': 0.01}, 'EOS_SI3654_power': {'measurement': 'power_consumption', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'source': 'power_meter'}, 'field_name': 'power_kw', 'min_value': 5.0, 'max_value': 25.0, 'normal_range': [12.0, 18.0], 'response_time_seconds': 1.0, 'failure_rate': 0.015}}, 'simulation': {'duration_minutes': 10, 'collection_interval_seconds': 2, 'enable_data_quality_issues': True, 'enable_network_failures': True, 'batch_write_size': 10}, 'analysis': {'anomaly_detection': {'std_dev_threshold': 2.5, 'window_size_minutes': 30}, 'reporting': {'dashboard_update_interval_seconds': 30, 'generate_hourly_reports': True}}}, 'production': {'influxdb': {'url': 'http://influxdb.ami.modelfactory.sg:8086', 'token': 'your-production-token', 'org': 'AMI', 'bucket': 'ami_eqpt'}}}
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Client created: <influxdb_client.client.influxdb_client.InfluxDBClient object at 0x000001F6DC4538C0>
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Write API created: <influxdb_client.client.write_api.WriteApi object at 0x000001F6DC5E4440>
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber'}
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 245.6}
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:24:53.660940+00:00
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Fields added: {'value': 245.6}
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:24:53.660940+00:00
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Tags added: {'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Fields added: {'value': 245.6}
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:24:53.660940+00:00
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Validating 245.6 for temperature
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Value (245.6) is valid and within normal range for temperature
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Value is -999.0. Check against list of error codes.
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Validating 500.0 for temperature
2025-08-05 16:24:53	phase1_time_series_basics	32252	CRITICAL	Value (500.0) out of range for temperature
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Value is None
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Value is not a number
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Validating 250.5 for temperature
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Value (250.5) is valid and within normal range for temperature
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 250.5}
2025-08-05 16:24:53	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:24:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=250.5 1754382293668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 1.01 for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (1.01) is valid and within normal range for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 1.01}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:24:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=1.01 1754382293668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 251.0 for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (251.0) is valid and within normal range for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 251.0}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:25:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=251 1754382353668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 0.99 for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (0.99) is valid and within normal range for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.99}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:25:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.99 1754382353668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 251.5 for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (251.5) is valid and within normal range for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 251.5}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:26:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=251.5 1754382413668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 1.0 for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (1.0) is valid and within normal range for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 1.0}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:26:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=1 1754382413668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 252.0 for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (252.0) is valid and within normal range for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 252.0}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:27:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=252 1754382473668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 0.98 for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (0.98) is valid and within normal range for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.98}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:27:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.98 1754382473668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 252.5 for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (252.5) is valid and within normal range for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 252.5}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:28:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=252.5 1754382533668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 0.99 for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (0.99) is valid and within normal range for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.99}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:28:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.99 1754382533668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 253.0 for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (253.0) is valid and within normal range for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 253.0}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:29:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=253 1754382593668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 0.97 for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (0.97) is valid and within normal range for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.97}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:29:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.97 1754382593668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 253.5 for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (253.5) is valid and within normal range for temperature
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 253.5}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:30:53.668777+00:00
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=253.5 1754382653668777000
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Validating 0.98 for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Value (0.98) is valid and within normal range for pressure
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.98}
2025-08-05 16:24:54	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:30:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.98 1754382653668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 254.0 for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (254.0) is valid and within normal range for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 254.0}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:31:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=254 1754382713668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 0.96 for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (0.96) is valid and within normal range for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.96}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:31:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.96 1754382713668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 254.5 for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (254.5) is valid and within normal range for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 254.5}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:32:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=254.5 1754382773668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 0.97 for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (0.97) is valid and within normal range for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.97}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:32:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.97 1754382773668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 255.0 for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (255.0) is valid and within normal range for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 255.0}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:33:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=255 1754382833668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 0.95 for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (0.95) is valid and within normal range for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.95}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:33:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.95 1754382833668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 254.6 for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (254.6) is valid and within normal range for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 254.6}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:34:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=254.6 1754382893668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 0.96 for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (0.96) is valid and within normal range for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.96}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:34:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.96 1754382893668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 254.2 for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (254.2) is valid and within normal range for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 254.2}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:35:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=254.2 1754382953668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 0.94 for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	WARNING	Value (0.94) is valid, but out of normal range for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (0.94) is valid and within normal range for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.94}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:35:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.94 1754382953668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 253.8 for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (253.8) is valid and within normal range for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 253.8}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:36:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=253.8 1754383013668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 0.95 for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (0.95) is valid and within normal range for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.95}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:36:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.95 1754383013668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 253.4 for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (253.4) is valid and within normal range for temperature
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 253.4}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:37:53.668777+00:00
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=253.4 1754383073668777000
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Validating 0.93 for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	WARNING	Value (0.93) is valid, but out of normal range for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Value (0.93) is valid and within normal range for pressure
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.93}
2025-08-05 16:24:55	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:37:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.93 1754383073668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 253.0 for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (253.0) is valid and within normal range for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 253.0}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:38:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=253 1754383133668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 0.94 for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	WARNING	Value (0.94) is valid, but out of normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (0.94) is valid and within normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.94}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:38:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.94 1754383133668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 252.6 for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (252.6) is valid and within normal range for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 252.6}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:39:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=252.6 1754383193668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 0.92 for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	WARNING	Value (0.92) is valid, but out of normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (0.92) is valid and within normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.92}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:39:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.92 1754383193668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 252.2 for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (252.2) is valid and within normal range for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 252.2}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:40:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=252.2 1754383253668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 0.93 for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	WARNING	Value (0.93) is valid, but out of normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (0.93) is valid and within normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.93}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:40:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.93 1754383253668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 251.8 for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (251.8) is valid and within normal range for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 251.8}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:41:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=251.8 1754383313668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 0.91 for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	WARNING	Value (0.91) is valid, but out of normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (0.91) is valid and within normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.91}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:41:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.91 1754383313668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 251.4 for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (251.4) is valid and within normal range for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 251.4}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:42:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=251.4 1754383373668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 0.92 for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	WARNING	Value (0.92) is valid, but out of normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (0.92) is valid and within normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.92}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:42:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.92 1754383373668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 251.0 for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (251.0) is valid and within normal range for temperature
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'temperature_celsius': 251.0}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:43:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=251 1754383433668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Validating 0.9 for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	WARNING	Value (0.9) is valid, but out of normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Value (0.9) is valid and within normal range for pressure
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Point created: 
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Fields added: {'pressure_bar': 0.9}
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Timestamp added: 2025-08-05 08:43:53.668777+00:00
2025-08-05 16:24:56	phase1_time_series_basics	32252	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.9 1754383433668777000
2025-08-05 16:24:56	phase1_time_series_basics	32252	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Config loaded: {'development': {'influxdb': {'url': 'http://localhost:8086', 'token': 'MyInitialAdminToken0==', 'org': 'docs', 'bucket': 'phase4_manufacturing_sensors'}, 'sensors': {'EOS_SI3654_temp': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}, 'field_name': 'temperature_celsius', 'min_value': 200.0, 'max_value': 280.0, 'normal_range': [240.0, 260.0], 'response_time_seconds': 1.5, 'failure_rate': 0.02}, 'EOS_SI3654_pressure': {'measurement': 'machine_sensors', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}, 'field_name': 'pressure_bar', 'min_value': 0.8, 'max_value': 1.2, 'normal_range': [0.95, 1.05], 'response_time_seconds': 2.0, 'failure_rate': 0.01}, 'EOS_SI3654_power': {'measurement': 'power_consumption', 'tags': {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'source': 'power_meter'}, 'field_name': 'power_kw', 'min_value': 5.0, 'max_value': 25.0, 'normal_range': [12.0, 18.0], 'response_time_seconds': 1.0, 'failure_rate': 0.015}}, 'simulation': {'duration_minutes': 10, 'collection_interval_seconds': 2, 'enable_data_quality_issues': True, 'enable_network_failures': True, 'batch_write_size': 10}, 'analysis': {'anomaly_detection': {'std_dev_threshold': 2.5, 'window_size_minutes': 30}, 'reporting': {'dashboard_update_interval_seconds': 30, 'generate_hourly_reports': True}}}, 'production': {'influxdb': {'url': 'http://influxdb.ami.modelfactory.sg:8086', 'token': 'your-production-token', 'org': 'AMI', 'bucket': 'ami_eqpt'}}}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Client created: <influxdb_client.client.influxdb_client.InfluxDBClient object at 0x00000295C1DF38C0>
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Write API created: <influxdb_client.client.write_api.WriteApi object at 0x00000295C1F94440>
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'temperature_celsius': 245.6}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:46:58.517534+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'value': 245.6}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:46:58.517534+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'location': 'print_chamber', 'sensor_type': 'temperature'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'value': 245.6}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:46:58.517534+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 245.6 for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value (245.6) is valid and within normal range for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value is -999.0. Check against list of error codes.
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 500.0 for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	CRITICAL	Value (500.0) out of range for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value is None
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value is not a number
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 250.0 for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value (250.0) is valid and within normal range for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'temperature_celsius': 250.0}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:46:58.525265+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=250 1754383618525265000
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 1.0 for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value (1.0) is valid and within normal range for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'pressure_bar': 1.0}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:46:58.525268+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=1 1754383618525268000
2025-08-05 16:46:58	phase1_time_series_basics	22844	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 260.0 for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value (260.0) is valid and within normal range for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'temperature_celsius': 260.0}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:36:58.525271+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=260 1754383018525271000
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 0.85 for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	WARNING	Value (0.85) is valid, but out of normal range for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value (0.85) is valid and within normal range for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'pressure_bar': 0.85}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:36:58.525515+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.85 1754383018525515000
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 250.0 for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value (250.0) is valid and within normal range for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'temperature_celsius': 250.0}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:36:58.525518+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=250 1754383018525518000
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 1.0 for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value (1.0) is valid and within normal range for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'pressure_bar': 1.0}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:36:58.525521+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=1 1754383018525521000
2025-08-05 16:46:58	phase1_time_series_basics	22844	ERROR	Error writing data point: 'sensor_type'
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 260.0 for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value (260.0) is valid and within normal range for temperature
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'temperature', 'location': 'print_chamber'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'temperature_celsius': 260.0}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:36:58.525524+00:00
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Data point written: machine_sensors,location=print_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=temperature temperature_celsius=260 1754383018525524000
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Validating 0.85 for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	WARNING	Value (0.85) is valid, but out of normal range for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Value (0.85) is valid and within normal range for pressure
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Point created: 
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Tags added: {'machine_id': 'EOS_SI3654', 'machine_type': '3d_printer', 'sensor_type': 'pressure', 'location': 'build_chamber'}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Fields added: {'pressure_bar': 0.85}
2025-08-05 16:46:58	phase1_time_series_basics	22844	DEBUG	Timestamp added: 2025-08-05 08:36:58.525526+00:00
2025-08-05 16:46:59	phase1_time_series_basics	22844	DEBUG	Data point written: machine_sensors,location=build_chamber,machine_id=EOS_SI3654,machine_type=3d_printer,sensor_type=pressure pressure_bar=0.85 1754383018525526000
