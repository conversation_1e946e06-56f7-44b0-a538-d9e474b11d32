# In logger.py

import logging
import sys
from logging.handlers import TimedRotatingFileHandler

# Define a standard format for our logs
LOG_FORMAT = logging.Formatter(
    "%(asctime)s - [%(levelname)s] - %(name)s - (%(filename)s).%(funcName)s(%(lineno)d) - %(message)s"
)

def get_console_handler():
    """Returns a handler that logs to the console."""
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(LOG_FORMAT)
    return console_handler

def get_file_handler(log_file=None):
    """Returns a time-based rotating file handler."""
    # Ensure the directory exists
    import os
    from pathlib import Path
    from datetime import datetime
    
    # Generate default timestamped filename if none provided
    if log_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"logs/monitor_{timestamp}.log"
    
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # This is the function we change
    file_handler = TimedRotatingFileHandler(
        filename=log_file,
        when='midnight',      # The type of interval. 'midnight' rolls over at midnight.
        interval=1,           # The number of 'when' intervals to wait. 1=daily.
        backupCount=28         # How many old log files to keep. 28=keep a month's worth.
    )
    file_handler.setFormatter(LOG_FORMAT)
    return file_handler

def setup_logger(log_level=logging.INFO, log_file=None):
    """
    Configures the root logger for the application.
    
    This function should be called ONLY ONCE at the start of the application.
    
    Args:
        log_level: The logging level (default: INFO)
        log_file: Path to log file (default: timestamped file in logs/ directory)
    """
    # Get the root logger for our application namespace
    logger = logging.getLogger("manufacturing_monitor")
    logger.setLevel(log_level)
    
    # Add handlers
    logger.addHandler(get_console_handler())
    logger.addHandler(get_file_handler(log_file))
    
    # Prevent the logger from propagating to the absolute root logger
    logger.propagate = False
    
    return logger