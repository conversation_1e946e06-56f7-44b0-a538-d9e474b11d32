import time
import struct
import socket
import threading
import yaml
from pyModbusTCP.client import ModbusClient
from lib_loggers import set_logger

# Load configuration
try:
    with open("config.yaml") as config_file:
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The config.yaml file should be present.')

logger = set_logger()

def check_network_connectivity(host, port, timeout=3):
    """Test if a device is reachable on the network"""
    # TODO: Implement socket-based connectivity test
    # Hint: Use socket.create_connection() with timeout
    # Return True if connection successful, False otherwise
    try:
        sock = socket.create_connection((host, port), timeout=timeout)
        sock.close()
        return True
    except Exception as e:
        logger.debug(f"Connection test failed for {host}:{port} - {e}")
        return False

def registers_to_float(register_pair):
    """Convert two Modbus registers back to IEEE 754 float"""
    if len(register_pair) != 2:
        raise ValueError("Expected exactly 2 registers for float conversion")
    
    try:
        combined_value = (register_pair[0] << 16) | register_pair[1]
        packed_value = struct.pack('>I', combined_value)
        float_value = struct.unpack('>f', packed_value)[0]
        return float_value
    except Exception as e:
        logger.error(f"Error converting registers {register_pair} to float: {e}")
        return None

def read_single_register_pair(client, register_address):
    """Read 2 consecutive registers (for one IEEE 754 float)"""
    logger.debug(f"Reading registers starting at 0x{register_address:04X}")
    
    try:
        # Use holding registers (not input registers) for simulator compatibility
        response = client.read_holding_registers(register_address, 2)
        if response and len(response) == 2:
            logger.debug(f"Raw register values at 0x{register_address:04X}: {response}")
            return response
        else:
            logger.debug(f"No valid response at 0x{register_address:04X}: {response}")
            return None
    except Exception as e:
        logger.error(f"Failed to read registers at 0x{register_address:04X}: {e}")
        return None

def read_power_meter_register(client, register_name, register_address):
    """Read a single power meter measurement"""
    logger.debug(f"Reading {register_name} from address 0x{register_address:04X}")
    
    try:
        register_pair = read_single_register_pair(client, register_address)
        if register_pair:
            return registers_to_float(register_pair)  # Changed from decode_float_from_registers
        else:
            return None
    except Exception as e:
        logger.error(f"Failed to read {register_name}: {e}")
        return None

def read_power_meter_data(meter_name, meter_config):
    """Read all configured registers from a power meter"""
    logger.info(f"Connecting to {meter_name} at {meter_config['ip_address']}:{meter_config['port']}")
    
    ip_address = meter_config['ip_address']
    port = meter_config['port']
    registers = meter_config['registers']
    
    # Fix: Access modbus config from development environment
    config_env = config['development']
    timeout = config_env.get('modbus', {}).get('timeout', 3.0)
    unit_id = config_env.get('modbus', {}).get('unit_id', 1)
    
    # Test connectivity first
    if not check_network_connectivity(ip_address, port):
        logger.error(f"{meter_name}: Cannot reach device at {ip_address}:{port}")
        return None
    
    try:
        client = ModbusClient(host=ip_address, port=port, unit_id=unit_id, timeout=timeout)
        if client.open():
            for register_name, register_addr in registers.items():
                value = read_power_meter_register(client, register_name, register_addr)
                if value is not None:
                    # match register_name:
                    #     case 'voltage_l1' | 'voltage_l2' | 'voltage_l3':
                    #         logger.info(f"{meter_name} {register_name}: {value:.1f} V")
                    #     case 'current_l1' | 'current_l2' | 'current_l3':
                    #         logger.info(f"{meter_name} {register_name}: {value:.2f} A")
                    #     case 'power_total':
                    #         logger.info(f"{meter_name} {register_name}: {value:.2f} kW")
                    #     case _:
                    #         logger.info(f"{meter_name} {register_name}: {value}")
                    if register_name.contains('voltage'):
                        logger.info(f"{meter_name} {register_name}: {value:.1f} V")
                    elif register_name.contains('current'):
                        logger.info(f"{meter_name} {register_name}: {value:.2f} A")
                    elif register_name.contains('power'):
                        logger.info(f"{meter_name} {register_name}: {value:.2f} kW")
                    else:
                        logger.info(f"{meter_name} {register_name}: {value}")
            client.close()
            return True
        else:
            logger.error(f"{meter_name}: Failed to connect to Modbus device")
            return None
    except Exception as e:
        logger.error(f"{meter_name}: Error reading data - {e}")
        return None
    finally:
        if 'client' in locals() and client.is_open:
            client.close()

def monitor_single_meter(meter_name, meter_config, interval_duration, monitoring_duration):
    """Monitor one power meter for specified duration"""
    logger.info(f"Starting monitoring for {meter_name}")
    
    start_time = time.time()
    read_count = 0
    error_count = 0
    
    while time.time() - start_time < monitoring_duration:
        try:
            result = read_power_meter_data(meter_name, meter_config)
            if result is not None:
                read_count += 1
            else:
                error_count += 1
        except Exception as e:
            logger.error(f"{meter_name}: Monitoring error - {e}")
            error_count += 1
        
        time.sleep(interval_duration)
    
    logger.info(f"Completed monitoring for {meter_name}: {read_count} successful reads, {error_count} errors")
    return read_count

def main():
    env_choice = 'development'
    config_env = config[env_choice]
    
    logger.info("Starting power meter monitoring system...")
    
    meters = config_env['power_meters']
    monitoring_config = config_env['monitoring']
    
    logger.info(f"Monitoring {len(meters)} power meters")
    
    threads = {}
    
    for meter_name, meter_config in meters.items():
        thread = threading.Thread(
            target=monitor_single_meter, 
            args=(meter_name, meter_config, monitoring_config['read_interval'], monitoring_config['duration_seconds'])
        )
        threads[meter_name] = thread
        thread.start()
        logger.info(f"Started monitoring thread for {meter_name}")
    
    for meter_name, thread in threads.items():
        thread.join()
        logger.info(f"Thread for {meter_name} completed")
    
    logger.info("Power meter monitoring completed")

if __name__ == "__main__":
    main()

"""
1. Setup development environment
2. Identify and define target sensors to monitor using sub-dictionaries
3. Create threads for each sensor and add to a dictionary
4. Define the 'monitor_single_sensor' function to read and log data for specified duration
5. Define the 'read_power_meter_data' function to read and log data from specified sensor address
6. Define the 'read_power_meter_register' function to read and log float from registers
7. Start the monitoring threads
8. Join the monitoring threads
9. Log completion message
"""


