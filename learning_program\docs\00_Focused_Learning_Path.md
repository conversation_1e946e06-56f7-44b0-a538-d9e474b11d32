# Focused Learning Path - One Technology at a Time

## 🎯 Learning Strategy: Build Understanding Layer by Layer

Instead of trying to understand everything at once, we'll build your knowledge **incrementally**, focusing on one technology at a time and seeing how it connects to what you already know.

---

## 📚 Phase 1: Python Foundations (Week 1-2)
**Goal**: Understand the Python patterns used throughout the project

### Day 1-3: Basic Python Patterns
**Files to examine**: `For power meter/lib_loggers.py`
```python
# Start here - simple, clean Python code
# Focus on understanding:
- Class definitions
- Function definitions  
- Import statements
- Basic error handling
```

**Learning Tasks**:
1. Read through `lib_loggers.py` line by line
2. Understand what each function does
3. Practice creating your own simple logging script

**Key Concepts**:
- Classes and methods
- File I/O operations
- String formatting
- Exception handling

**External Resources for Days 1-3**:
- **Practice**: [Python Official Tutorial - Classes](https://docs.python.org/3/tutorial/classes.html)
- **Reference**: [Python Logging Documentation](https://docs.python.org/3/library/logging.html)
- **Book**: "Automate the Boring Stuff with Python" (Chapters 1-3)

### Day 4-7: Configuration and Data Structures
**Files to examine**: `For power meter/env.yaml.template`
```yaml
# Simple configuration file - easy to understand
development:
    influxdb_token: BAUefNIXOaHhiGcJlHSOXUPeZFsECwKuw3Cz72EUr9fOtWZdLwYlHLUQn7Pzq__qqesPFjXYV2hyjCGItHx__Q== 
    influxdb_bucket_name: testbucket
```

**Learning Tasks**:
1. Understand YAML format
2. Learn how Python loads YAML files
3. Practice creating configuration files

**External Resources for Days 4-7**:
- **YAML Tutorial**: [Learn YAML in Y Minutes](https://learnxinyminutes.com/docs/yaml/)
- **Python YAML**: [PyYAML Documentation](https://pyyaml.org/wiki/PyYAMLDocumentation)
- **Best Practices**: [Configuration Management in Python](https://docs.python-guide.org/writing/structure/#regarding-django-applications)

**Practice Exercise**:
```python
# Create your own config loader
import yaml

with open("my_config.yaml") as f:
    config = yaml.safe_load(f)
print(config['development']['influxdb_token'])
```

### Day 8-14: Threading Basics
**Files to examine**: `For power meter/main.py` (lines 580-600 only)
```python
# Just focus on this threading section:
threads = {}
for eqpt, (ip, port) in eqpts.items():
    threads[eqpt] = threading.Thread(target=thread_starter, args=(eqpt, ip, port))
    threads[eqpt].start()
```

**Learning Tasks**:
1. Understand what threads are (like having multiple workers)
2. See how to start multiple threads
3. Practice with simple threading examples

**External Resources for Days 8-14**:
- **Threading Tutorial**: [Real Python - Threading in Python](https://realpython.com/intro-to-python-threading/)
- **Official Docs**: [Python Threading Documentation](https://docs.python.org/3/library/threading.html)
- **Book**: "Effective Python" by Brett Slatkin (Chapter on Concurrency)
- **Video**: "Threading vs Multiprocessing in Python" (YouTube)

**Key Threading Patterns You'll See**:
```python
# Thread management pattern (like in power meter script)
threads = {}
for device in devices:
    thread = threading.Thread(target=monitor_device, args=(device,))
    threads[device] = thread
    thread.start()

# Thread-safe communication with locks
with lock:
    shared_data[key] = value
```

**Practice Exercise**:
```python
import threading
import time

def worker(name):
    for i in range(5):
        print(f"Worker {name} doing task {i}")
        time.sleep(1)

# Start 3 workers
for i in range(3):
    t = threading.Thread(target=worker, args=(f"Worker-{i}",))
    t.start()
```

---

## 🔌 Phase 2: Understanding Data Communication (Week 3-4)  
**Goal**: Learn how machines communicate over networks

### Day 15-17: Network Basics
**Files to examine**: `For power meter/main.py` (lines 35-50 only)
```python
# Focus just on these simple functions:
def check_socket(host, port, timeout_ms=500,type='ipv4'):
def check_ping(host):
```

**Learning Tasks**:
1. Understand what IP addresses and ports are
2. Learn what "ping" means
3. Practice checking if devices are online

**External Resources for Days 15-17**:
- **Networking Basics**: [Computer Networking - Khan Academy](https://www.khanacademy.org/computing/computers-and-internet/xcae6f4a7ff015e7d:the-internet)
- **TCP/IP Tutorial**: [TCP/IP Guide](http://www.tcpipguide.com/)
- **Industrial Networks**: [Industrial Ethernet Overview](https://www.automationworld.com/factory/networks-communication/article/13316969/industrial-ethernet-basics)
- **Tools**: Download [Wireshark](https://www.wireshark.org/) for network analysis

**Key Networking Concepts for Manufacturing**:
- **IP Addresses**: How machines are identified (*************)
- **Ports**: Different services on same machine (4840 for OPC UA, 502 for Modbus)
- **Protocols**: TCP (reliable) vs UDP (fast)
- **Industrial Networks**: Often isolated from internet for security

**Practice Exercise**:
```python
import socket

# Check if a website is reachable
def is_online(host, port):
    try:
        socket.create_connection((host, port), timeout=3)
        return True
    except:
        return False

print(is_online("google.com", 80))  # Should return True
```

### Day 18-21: Modbus Communication (Simpler Protocol) ⭐⭐
**Files to examine**: `For power meter/main.py` (class SDM630PowerMeter - read_registers method only)
```python
# Just focus on this one method:
def read_registers(self,reg_addr,read_count=1,register_type='input'):
    response = self.client.read_input_registers(reg_addr=reg_addr,reg_nb=read_count)
    return response
```

**Learning Tasks**:
1. Understand that Modbus is like "asking a device for data"
2. Learn that registers are like "memory addresses"
3. See how to convert raw data to meaningful values

**External Resources for Days 18-21**:
- **Start Here**: [Simply Modbus - Protocol Overview](http://www.simplymodbus.ca/)
- **Python Library**: [pyModbusTCP documentation](https://pymodbustcp.readthedocs.io/)
- **Tool**: Download [QModMaster](https://github.com/ed-chemnitz/qmodmaster) to test Modbus connections
- **Video**: "Modbus Protocol Explained" (YouTube)

**Key Modbus Concepts**:
- **Registers**: Memory locations on devices (like address 0x0000 = voltage)
- **Function Codes**: Different ways to read/write data
- **Floating Point Encoding**: How power values are stored as raw bytes
- **TCP vs RTU**: TCP for Ethernet, RTU for serial connections

**Practice Exercise** (with simulator):
```python
# Use a free Modbus simulator or this fake example:
class FakeModbusClient:
    def read_input_registers(self, reg_addr, reg_nb):
        # Fake temperature data
        return [100, 50]  # Represents 25.0°C when decoded

# Practice reading "temperature" from a fake device
client = FakeModbusClient()
raw_data = client.read_input_registers(0, 2)
temperature = decode_temperature(raw_data)  # You'll write this function
```

### Day 22-28: Data Conversion and Storage
**Files to examine**: `For power meter/main.py` (decode_for_float method)
```python
@staticmethod
def decode_for_float(byte_pair):
    # This converts raw bytes to actual numbers
    combined_value = (byte_pair[0] << 16) | byte_pair[1]
    packed_value = struct.pack('>I', combined_value)
    float_value = struct.unpack('>f', packed_value)[0]
    return float_value
```

**Learning Tasks**:
1. Understand that machines send data as "raw bytes"
2. Learn how to convert bytes to meaningful numbers
3. Practice data conversion

---

## 📊 Phase 3: Time Series Data and Databases (Week 5-6)
**Goal**: Understand how to store and retrieve time-based data

### Day 29-31: Time Series Concepts ⭐⭐
**Files to examine**: `InfluxDB/upload_data.py` (write_method_1 only)
```python
def write_method_1():
    p = influxdb_client.Point("python_testscript").tag("location", "Singapore").field("temperature", 35.0)
    write_api.write(bucket=bucket_name, org=org_id, record=p)
```

**Learning Tasks**:
1. Understand what time-series data is (value + timestamp)
2. Learn the concepts: measurement, tags, fields
3. Practice creating simple data points

**External Resources for Days 29-31**:
- **Start Here**: [InfluxDB University](https://university.influxdata.com/)
- **Documentation**: [InfluxDB Python Client](https://influxdb-client.readthedocs.io/)
- **Concepts**: [Time Series Database Explained](https://www.influxdata.com/what-is-a-time-series-database/)
- **Tutorial**: [Getting Started with InfluxDB](https://docs.influxdata.com/influxdb/v2.0/get-started/)

**Key Time Series Concepts**:
- **Measurements**: Like database tables (e.g., "printer_stats", "power_consumption")
- **Tags**: Metadata for grouping (machine ID, location) - indexed for fast queries
- **Fields**: Actual sensor values (temperature, pressure) - the data you're measuring
- **Timestamps**: When data was recorded - automatically added if not specified

**Practice Exercise**:
```python
# Create fake temperature readings
from datetime import datetime

data_points = [
    {"time": datetime.now(), "location": "Room1", "temperature": 22.5},
    {"time": datetime.now(), "location": "Room2", "temperature": 24.1},
]

# Later: learn to store these in InfluxDB
```

### Day 32-35: Database Operations
**Files to examine**: `InfluxDB/upload_data.py` (read_method_1 only)
```python
def read_method_1():
    query_text = '''from(bucket: "testbucket") |> range(start: -60m, stop: 0d) |> last()'''
    result = query_api.query(query_text, org=org_id)
```

**Learning Tasks**:
1. Learn basic database queries
2. Understand how to retrieve recent data
3. Practice querying time ranges

**External Resources for Days 32-35**:
- **Flux Language**: [Flux Query Language Tutorial](https://docs.influxdata.com/influxdb/v2.0/query-data/get-started/)
- **Query Examples**: [Common InfluxDB Queries](https://docs.influxdata.com/influxdb/v2.0/query-data/common-queries/)
- **Practice**: [InfluxDB University - Flux Course](https://university.influxdata.com/courses/flux-fundamentals/)

**Key Flux Query Patterns You'll Use**:
```flux
// Get last hour of temperature data
from(bucket: "sensors")
  |> range(start: -1h)
  |> filter(fn: (r) => r["_measurement"] == "temperature")
  |> filter(fn: (r) => r["location"] == "room1")

// Get average power consumption per hour
from(bucket: "power")
  |> range(start: -24h)
  |> aggregateWindow(every: 1h, fn: mean)
```

### Day 36-42: Data Analysis Basics ⭐⭐
**Files to examine**: `InfluxDB/pull_data.py` (lines 1-50 only)
```python
# Focus on the simple plotting part:
plt.plot(times, values, '.-', linewidth=0.5, markersize=1)
plt.show()
```

**Learning Tasks**:
1. Learn to plot simple time series data
2. Understand how to extract data from database
3. Practice creating basic charts

**External Resources for Days 36-42**:
- **Matplotlib Tutorial**: [Matplotlib Tutorial](https://matplotlib.org/stable/tutorials/index.html)
- **Book**: "Python Data Science Handbook" by Jake VanderPlas
- **Time Series Plotting**: [Plotting Time Series Data](https://matplotlib.org/stable/gallery/recipes/common_date_problems.html)
- **Industrial Dashboards**: [Grafana Getting Started](https://grafana.com/docs/grafana/latest/getting-started/)

**Data Visualization Patterns for Manufacturing**:
```python
# Real-time monitoring plot
plt.figure(figsize=(12, 6))
plt.subplot(2, 1, 1)
plt.plot(timestamps, temperatures, label='Temperature')
plt.ylabel('Temperature (°C)')

plt.subplot(2, 1, 2)
plt.plot(timestamps, pressures, label='Pressure', color='red')
plt.ylabel('Pressure (bar)')
plt.xlabel('Time')
```

---

## ⚡ Phase 4: Asynchronous Programming (Week 7-8)
**Goal**: Understand async/await for real-time data collection

### Day 43-45: Async Basics (Theory) ⭐⭐⭐
**Files to examine**: Simple async examples (create new file)
```python
import asyncio

async def simple_task():
    print("Starting task...")
    await asyncio.sleep(2)  # Simulate waiting for data
    print("Task complete!")

asyncio.run(simple_task())
```

**Learning Tasks**:
1. Understand the difference between sync and async
2. Learn what `await` does (waits without blocking)
3. Practice with simple async functions

**External Resources for Days 43-45**:
- **Start Here**: [Real Python - Async IO in Python](https://realpython.com/async-io-python/)
- **Official Docs**: [asyncio documentation](https://docs.python.org/3/library/asyncio.html)
- **Book**: "Effective Python" by Brett Slatkin (Chapter on Concurrency)
- **Video**: "Async/Await Explained" (YouTube)

**Why Async is Critical for Industrial Monitoring**:
- **Multiple Machines**: Monitor 10+ machines simultaneously without blocking
- **Real-time Response**: Handle incoming data while maintaining connections
- **Network Delays**: Don't freeze while waiting for slow network responses
- **Concurrent Operations**: Read sensors + upload data + health checks all at once

**Basic Async Patterns You'll Encounter**:
```python
# Non-blocking sleep (good for industrial monitoring)
await asyncio.sleep(10)  # Check machine every 10 seconds

# Context manager pattern (automatic cleanup)
async with client:
    # Connection automatically closes even if error occurs
    await client.read_data()
```

### Day 46-49: Real-world Async Patterns ⭐⭐⭐
**Files to examine**: `For EOS M290 OPCUA/opcua_test.py` (simplified version)
```python
# Focus on this pattern:
async with Client(url='opc.tcp://machine_ip:4840/') as client:
    node = client.get_node('temperature_sensor')
    value = await node.read_value()
    print(value)
```

**Learning Tasks**:
1. Understand `async with` for resource management
2. Learn how to connect to machines asynchronously
3. Practice reading data from devices

**External Resources for Days 46-49**:
- **OPC UA Protocol**: [OPC Foundation - What is OPC UA?](https://opcfoundation.org/about/opc-technologies/opc-ua/)
- **Python Library**: [asyncua documentation](https://asyncua.readthedocs.io/)
- **Industrial Automation**: "OPC UA - Unified Architecture" by Mahnke, Leitner & Damm
- **Tool**: Download [UaExpert](https://www.unified-automation.com/products/development-tools/uaexpert.html) (free OPC UA client)

**Key OPC UA Concepts for Manufacturing**:
- **Nodes**: Data points on machines (like sensors, actuators, status indicators)
- **Subscriptions**: Real-time data monitoring (get notified when values change)
- **Security**: Authentication and encryption for industrial networks
- **Data Types**: How machine data is structured (integers, floats, strings, arrays)

**OPC UA Patterns You'll Use**:
```python
# Subscribe to multiple sensors at once
nodes = [
    client.get_node("ns=2;s=Temperature"),
    client.get_node("ns=2;s=Pressure"), 
    client.get_node("ns=2;s=Status")
]

subscription = await client.create_subscription(500, handler)
await subscription.subscribe_data_change(nodes)
```

### Day 50-56: Putting It All Together
**Files to examine**: `For EOS M290 OPCUA/main.py` (main function structure only)
```python
# Just understand the overall structure:
async def main():
    async with client:
        # 1. Connect to machine
        # 2. Set up data subscriptions  
        # 3. Keep running and collect data
        while True:
            await asyncio.sleep(10)
```

---

## 🔧 Phase 5: Integration and Customization (Week 9-10)
**Goal**: Adapt the system for your own use

### Day 57-59: Machine Discovery
**Files to examine**: `For EOS M290 OPCUA/opcua_test.py` (full file)
**Learning Tasks**:
1. Learn to discover what data is available on a machine
2. Practice connecting to different types of devices
3. Understand how to explore machine capabilities

### Day 60-63: Configuration and Adaptation
**Files to examine**: `docs/03_Adaptation_Guide.md`
**Learning Tasks**:
1. Learn to configure the system for new machines
2. Practice modifying configuration files
3. Understand how to add new data sources

**External Resources for Days 60-63**:
- **Machine Documentation**: Always start with manufacturer's OPC UA/Modbus manuals
- **Network Tools**: [Advanced IP Scanner](https://www.advanced-ip-scanner.com/) for device discovery
- **Protocol Testing**: Use UaExpert (OPC UA) and QModMaster (Modbus) to test before coding
- **Configuration Management**: [12-Factor App Methodology](https://12factor.net/config)

**Integration Checklist for New Machines**:
```yaml
# Your machine adaptation checklist:
□ Identify communication protocol (OPC UA, Modbus, MQTT, HTTP API)
□ Test connectivity with appropriate client tool
□ Document available data points and their meanings
□ Determine update frequencies and filtering requirements
□ Set up configuration files and authentication
□ Create test scripts before full integration
□ Plan error handling and reconnection strategies
```

### Day 64-70: Building Your Own System
**Practice Project**: Create monitoring for a simple device
1. Choose a target (even if it's simulated)
2. Set up basic data collection
3. Store data in InfluxDB
4. Create simple visualizations

**Final Integration Resources**:
- **Complete Setup**: Follow `docs/05_Deployment_Guide.md` for production deployment
- **Visualization**: Use `docs/06_Grafana_Setup.md` for dashboard creation
- **Grafana Integration**: [Grafana InfluxDB Data Source](https://grafana.com/docs/grafana/latest/datasources/influxdb/)
- **Community**: Join [InfluxDB Community](https://community.influxdata.com/) and [OPC UA forums](https://opcfoundation.org/forum/)

**Your Final Project Goals**:
```python
# By end of week 10, you should be able to:
1. Connect to any OPC UA or Modbus device
2. Configure data collection with appropriate filtering
3. Store data efficiently in InfluxDB
4. Create basic Grafana dashboards
5. Set up automated alerts for important conditions
6. Handle errors and network disruptions gracefully
7. Deploy your system for 24/7 operation
```

**Capstone Project Ideas**:
- Monitor a 3D printer (if available) or CNC machine
- Create a home automation monitoring system
- Build a weather station data collector
- Monitor server room equipment (UPS, temperature sensors)
- Set up monitoring for any Modbus-enabled device

---

## 📋 Daily Study Schedule

### **Morning (30 minutes)**: Read and understand concepts
- Read the assigned files for that day
- Take notes on what each piece of code does
- Look up any unfamiliar terms

### **Afternoon (30 minutes)**: Hands-on practice
- Try the practice exercises
- Modify existing code to see what happens
- Create simple test scripts

### **Evening (15 minutes)**: Review and connect
- Review what you learned
- Think about how it connects to previous knowledge
- Prepare questions for the next day

---

## 🎯 Success Milestones

### **End of Week 2**: You can read and understand basic Python scripts
### **End of Week 4**: You can connect to devices and read data
### **End of Week 6**: You can store and query time-series data  
### **End of Week 8**: You can write async programs for real-time monitoring
### **End of Week 10**: You can adapt this system for new machines

---

## 🚨 When You Feel Overwhelmed

**Take a break and remember**:
1. **Focus on just ONE file per day**
2. **Don't worry about understanding everything immediately**
3. **It's okay to re-read files multiple times**
4. **Ask questions about specific lines of code**
5. **Practice with simple examples before complex ones**

**Most Important**: Each technology builds on the previous one. Master the basics before moving to advanced concepts!

---

## ️ Quick Reference Cheat Sheets

### Async Python Patterns:
```python
# Connection pattern
async with client:
    # Do work here
    pass

# Subscription pattern  
async def data_handler(node, value, data):
    print(f"Got {value} from {node}")

# Error handling pattern
try:
    await risky_operation()
except ConnectionError:
    await reconnect()
```

### InfluxDB Write Pattern:
```python
from influxdb_client import Point

point = Point("measurement_name") \
    .tag("machine", "EOS_SI3654") \
    .field("temperature", 25.3) \
    .time(datetime.now())
    
write_api.write(bucket="my_bucket", record=point)
```

### OPC UA Node Reading:
```python
# Read a single value
value = await node.read_value()

# Subscribe to changes
handler = MyHandler()
subscription = await client.create_subscription(500, handler)
await subscription.subscribe_data_change([node])
```

### Modbus Register Reading:
```python
# Read power meter data
client = ModbusClient(host="*************", port=502)
raw_data = client.read_input_registers(reg_addr=0x0000, reg_nb=2)
voltage = decode_float(raw_data)  # Convert bytes to actual voltage
```

---

## ⚠️ Common Pitfalls to Avoid

1. **Async/Sync Mixing**: Don't use `time.sleep()` in async functions (use `asyncio.sleep()`)
2. **Connection Management**: Always use `async with` for proper cleanup
3. **Error Handling**: Industrial networks are unreliable - always plan for reconnection
4. **Data Validation**: Sensors can return invalid data - validate before storing
5. **Threading**: Be careful mixing threading with async code (like in the power meter script)
6. **Over-monitoring**: Don't collect data faster than needed - respect network bandwidth
7. **Authentication**: Store credentials securely, never hardcode passwords
8. **Time Zones**: Always use UTC for timestamps in industrial systems
