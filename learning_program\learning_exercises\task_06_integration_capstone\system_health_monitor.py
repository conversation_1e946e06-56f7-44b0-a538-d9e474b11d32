"""
Phase 3: Production Deployment & Monitoring

Learning Goals:
- Master production deployment procedures
- Implement system health monitoring and alerting
- Build performance optimization strategies
- Develop maintenance and troubleshooting procedures

This phase ensures systems are production-ready and maintainable.
"""

import asyncio
import psutil
import yaml
import smtplib
import json
from datetime import datetime, timezone, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from pathlib import Path
import logging
import subprocess
import shutil

class SystemHealthMonitor:
    """
    Comprehensive system health monitoring and alerting.
    
    Learning Goal: Master production system monitoring
    Real Project Pattern: Health monitoring for 24/7 operation
    """
    
    def __init__(self, config):
        """
        Initialize system health monitor.
        
        TODO: Set up health monitoring infrastructure
        """
        self.config = config
        self.health_metrics = {}
        self.alert_thresholds = config.get('health_monitoring', {}).get('alert_thresholds', {})
        self.alert_history = {}
        self.last_alert_times = {}
        
        # Set up logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        print("=== SYSTEM HEALTH MONITOR ===")
        print("Goal: Ensure 24/7 production system reliability\n")
        
    async def monitor_system_health(self):
        """
        Continuously monitor system health metrics.
        
        TODO: Implement comprehensive health monitoring
        """
        check_interval = self.config.get('health_monitoring', {}).get('check_interval_seconds', 30)
        
        print(f"🏥 Starting health monitoring (interval: {check_interval}s)")
        
        while True:
            try:
                # TODO: Collect system metrics
                metrics = await self.collect_health_metrics()
                
                # TODO: Analyze metrics against thresholds
                alerts = await self.analyze_health_metrics(metrics)
                
                # TODO: Send alerts if necessary
                if alerts:
                    await self.send_health_alerts(alerts)
                
                # TODO: Store health metrics for trending
                await self.store_health_metrics(metrics)
                
                # TODO: Display current status
                await self.display_health_status(metrics)
                
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                self.logger.error(f"Health monitoring error: {e}")
                print(f"❌ Health monitoring error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
                
    async def collect_health_metrics(self):
        """
        Collect comprehensive system health metrics.
        
        TODO: Implement health metrics collection
        """
        metrics = {
            'timestamp': datetime.now(timezone.utc),
            'system': {},
            'application': {},
            'network': {},
            'storage': {}
        }
        
        try:
            # TODO: System metrics (CPU, memory, disk)
            metrics['system'] = {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent,
                'load_average': psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0,
                'boot_time': psutil.boot_time(),
                'process_count': len(psutil.pids())
            }
            
            # TODO: Application metrics
            metrics['application'] = await self.collect_application_metrics()
            
            # TODO: Network connectivity metrics
            metrics['network'] = await self.collect_network_metrics()
            
            # TODO: Storage metrics
            metrics['storage'] = await self.collect_storage_metrics()
            
        except Exception as e:
            self.logger.error(f"Metrics collection error: {e}")
            metrics['error'] = str(e)
            
        return metrics
        
    async def collect_application_metrics(self):
        """
        Collect application-specific metrics.
        
        TODO: Implement application monitoring
        """
        app_metrics = {
            'monitoring_processes': 0,
            'active_connections': 0,
            'data_collection_rate': 0,
            'error_rate': 0,
            'uptime_seconds': 0
        }
        
        try:
            # TODO: Count monitoring processes
            monitoring_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if any('monitoring' in str(cmd).lower() for cmd in proc.info['cmdline'] or []):
                        monitoring_processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
                    
            app_metrics['monitoring_processes'] = len(monitoring_processes)
            
            # TODO: Check for specific application files/logs
            log_files = list(Path('logs').glob('*.log')) if Path('logs').exists() else []
            app_metrics['log_files_count'] = len(log_files)
            
            # TODO: Calculate uptime from log files or process start time
            if monitoring_processes:
                oldest_process = min(monitoring_processes, 
                                   key=lambda p: psutil.Process(p['pid']).create_time())
                create_time = psutil.Process(oldest_process['pid']).create_time()
                app_metrics['uptime_seconds'] = time.time() - create_time
                
        except Exception as e:
            self.logger.error(f"Application metrics error: {e}")
            
        return app_metrics
        
    async def collect_network_metrics(self):
        """
        Collect network connectivity metrics.
        
        TODO: Implement network monitoring
        """
        network_metrics = {
            'internet_connectivity': False,
            'local_network_connectivity': False,
            'influxdb_connectivity': False,
            'opcua_connectivity': False,
            'modbus_connectivity': False
        }
        
        try:
            # TODO: Test internet connectivity
            network_metrics['internet_connectivity'] = await self.test_connectivity('*******', 53)
            
            # TODO: Test local network
            network_metrics['local_network_connectivity'] = await self.test_connectivity('***********', 80)
            
            # TODO: Test InfluxDB connectivity
            influx_config = self.config.get('influxdb', {})
            if influx_config.get('url'):
                influx_host = influx_config['url'].replace('http://', '').replace('https://', '').split(':')[0]
                network_metrics['influxdb_connectivity'] = await self.test_connectivity(influx_host, 8086)
                
            # TODO: Test OPC UA connectivity
            machines = self.config.get('machines', {}).get('opcua_machines', {})
            opcua_tests = []
            for machine_config in machines.values():
                opcua_config = machine_config.get('opcua', {})
                if opcua_config.get('url'):
                    url = opcua_config['url']
                    host = url.replace('opc.tcp://', '').split(':')[0]
                    port = int(url.split(':')[-1].rstrip('/'))
                    opcua_tests.append(self.test_connectivity(host, port))
                    
            if opcua_tests:
                opcua_results = await asyncio.gather(*opcua_tests, return_exceptions=True)
                network_metrics['opcua_connectivity'] = any(r for r in opcua_results if not isinstance(r, Exception))
                
        except Exception as e:
            self.logger.error(f"Network metrics error: {e}")
            
        return network_metrics
        
    async def test_connectivity(self, host, port, timeout=5):
        """
        Test connectivity to a specific host and port.
        
        TODO: Implement connectivity testing
        """
        try:
            future = asyncio.open_connection(host, port)
            reader, writer = await asyncio.wait_for(future, timeout=timeout)
            writer.close()
            await writer.wait_closed()
            return True
        except Exception:
            return False
            
    async def collect_storage_metrics(self):
        """
        Collect storage and file system metrics.
        
        TODO: Implement storage monitoring
        """
        storage_metrics = {
            'disk_usage_percent': 0,
            'log_directory_size_mb': 0,
            'data_directory_size_mb': 0,
            'available_space_gb': 0
        }
        
        try:
            # TODO: Disk usage
            disk_usage = psutil.disk_usage('/')
            storage_metrics['disk_usage_percent'] = (disk_usage.used / disk_usage.total) * 100
            storage_metrics['available_space_gb'] = disk_usage.free / (1024**3)
            
            # TODO: Log directory size
            if Path('logs').exists():
                log_size = sum(f.stat().st_size for f in Path('logs').rglob('*') if f.is_file())
                storage_metrics['log_directory_size_mb'] = log_size / (1024**2)
                
            # TODO: Data directory size
            if Path('data').exists():
                data_size = sum(f.stat().st_size for f in Path('data').rglob('*') if f.is_file())
                storage_metrics['data_directory_size_mb'] = data_size / (1024**2)
                
        except Exception as e:
            self.logger.error(f"Storage metrics error: {e}")
            
        return storage_metrics
        
    async def analyze_health_metrics(self, metrics):
        """
        Analyze metrics and identify issues.
        
        TODO: Implement intelligent health analysis
        """
        alerts = []
        
        try:
            # TODO: Check system resource thresholds
            system_metrics = metrics.get('system', {})
            
            # CPU threshold
            cpu_threshold = self.alert_thresholds.get('cpu_percent', 80)
            if system_metrics.get('cpu_percent', 0) > cpu_threshold:
                alerts.append({
                    'type': 'cpu_high',
                    'severity': 'warning',
                    'message': f"CPU usage {system_metrics['cpu_percent']:.1f}% exceeds threshold {cpu_threshold}%",
                    'value': system_metrics['cpu_percent'],
                    'threshold': cpu_threshold
                })
                
            # Memory threshold
            memory_threshold = self.alert_thresholds.get('memory_percent', 85)
            if system_metrics.get('memory_percent', 0) > memory_threshold:
                alerts.append({
                    'type': 'memory_high',
                    'severity': 'warning',
                    'message': f"Memory usage {system_metrics['memory_percent']:.1f}% exceeds threshold {memory_threshold}%",
                    'value': system_metrics['memory_percent'],
                    'threshold': memory_threshold
                })
                
            # Disk threshold
            disk_threshold = self.alert_thresholds.get('disk_percent', 90)
            if system_metrics.get('disk_percent', 0) > disk_threshold:
                alerts.append({
                    'type': 'disk_high',
                    'severity': 'critical',
                    'message': f"Disk usage {system_metrics['disk_percent']:.1f}% exceeds threshold {disk_threshold}%",
                    'value': system_metrics['disk_percent'],
                    'threshold': disk_threshold
                })
                
            # TODO: Check application performance metrics
            app_metrics = metrics.get('application', {})
            
            if app_metrics.get('monitoring_processes', 0) == 0:
                alerts.append({
                    'type': 'no_monitoring_processes',
                    'severity': 'critical',
                    'message': "No monitoring processes detected",
                    'value': 0,
                    'threshold': 1
                })
                
            # TODO: Check connectivity metrics
            network_metrics = metrics.get('network', {})
            
            if not network_metrics.get('influxdb_connectivity', True):
                alerts.append({
                    'type': 'influxdb_connectivity',
                    'severity': 'critical',
                    'message': "InfluxDB connectivity lost",
                    'value': False,
                    'threshold': True
                })
                
        except Exception as e:
            self.logger.error(f"Health analysis error: {e}")
            
        return alerts
        
    async def send_health_alerts(self, alerts):
        """
        Send health alerts via configured channels.
        
        TODO: Implement alerting system
        """
        alerting_config = self.config.get('health_monitoring', {}).get('alerting', {})
        
        for alert in alerts:
            # TODO: Check alert cooldown
            alert_key = f"{alert['type']}_{alert['severity']}"
            last_alert_time = self.last_alert_times.get(alert_key)
            cooldown_minutes = alerting_config.get('alert_cooldown_minutes', 15)
            
            if last_alert_time:
                time_since_last = datetime.now(timezone.utc) - last_alert_time
                if time_since_last.total_seconds() < cooldown_minutes * 60:
                    continue  # Skip alert due to cooldown
                    
            # TODO: Send email alert
            if alerting_config.get('email', {}).get('enabled', False):
                await self.send_email_alert(alert, alerting_config['email'])
                
            # TODO: Log alert
            self.logger.warning(f"ALERT: {alert['message']}")
            print(f"🚨 ALERT: {alert['message']}")
            
            # TODO: Update alert history
            self.last_alert_times[alert_key] = datetime.now(timezone.utc)
            
    async def send_email_alert(self, alert, email_config):
        """
        Send email alert.
        
        TODO: Implement email alerting
        """
        try:
            # TODO: Create email message
            msg = MIMEMultipart()
            msg['From'] = email_config.get('username', '<EMAIL>')
            msg['To'] = ', '.join(email_config.get('recipients', []))
            msg['Subject'] = f"System Alert: {alert['type']} - {alert['severity']}"
            
            # TODO: Create email body
            body = f"""
System Health Alert

Alert Type: {alert['type']}
Severity: {alert['severity']}
Message: {alert['message']}
Current Value: {alert['value']}
Threshold: {alert['threshold']}
Timestamp: {datetime.now(timezone.utc)}

Please investigate and take appropriate action.

System Health Monitor
"""
            
            msg.attach(MIMEText(body, 'plain'))
            
            # TODO: Send email
            server = smtplib.SMTP(email_config.get('smtp_server'), email_config.get('smtp_port', 587))
            server.starttls()
            server.login(email_config.get('username'), email_config.get('password'))
            server.send_message(msg)
            server.quit()
            
            print(f"📧 Email alert sent for {alert['type']}")
            
        except Exception as e:
            self.logger.error(f"Email alert error: {e}")
            
    async def store_health_metrics(self, metrics):
        """
        Store health metrics for historical analysis.
        
        TODO: Implement metrics storage
        """
        try:
            # TODO: Store in InfluxDB if available
            # TODO: Store in local file as backup
            
            # Simple file-based storage for learning
            metrics_file = Path('logs/health_metrics.jsonl')
            metrics_file.parent.mkdir(exist_ok=True)
            
            with open(metrics_file, 'a') as f:
                json.dump(metrics, f, default=str)
                f.write('\n')
                
        except Exception as e:
            self.logger.error(f"Metrics storage error: {e}")
            
    async def display_health_status(self, metrics):
        """
        Display current health status.
        
        TODO: Implement status display
        """
        try:
            system = metrics.get('system', {})
            network = metrics.get('network', {})
            
            # TODO: Create status summary
            status_lines = [
                f"🖥️  CPU: {system.get('cpu_percent', 0):.1f}%",
                f"💾 Memory: {system.get('memory_percent', 0):.1f}%", 
                f"💿 Disk: {system.get('disk_percent', 0):.1f}%",
                f"🌐 Network: {'✅' if network.get('internet_connectivity') else '❌'}",
                f"📊 InfluxDB: {'✅' if network.get('influxdb_connectivity') else '❌'}"
            ]
            
            # TODO: Display status (every 5th check to avoid spam)
            if not hasattr(self, '_status_counter'):
                self._status_counter = 0
            self._status_counter += 1
            
            if self._status_counter % 5 == 0:
                print(f"📊 Health Status: {' | '.join(status_lines)}")
                
        except Exception as e:
            self.logger.error(f"Status display error: {e}")

class DeploymentManager:
    """
    Manage production deployment procedures.
    
    Learning Goal: Master production deployment practices
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    async def deploy_system(self, environment="production"):
        """
        Deploy system to production environment.
        
        TODO: Implement production deployment procedures
        """
        print(f"=== DEPLOYING TO {environment.upper()} ===")
        
        try:
            # TODO: Pre-deployment checks
            print("1. Running pre-deployment checks...")
            await self.run_pre_deployment_checks()
            
            # TODO: Deploy configuration
            print("2. Deploying configuration...")
            await self.deploy_configuration(environment)
            
            # TODO: Deploy application
            print("3. Deploying application...")
            await self.deploy_application()
            
            # TODO: Post-deployment verification
            print("4. Running post-deployment verification...")
            await self.run_post_deployment_checks()
            
            print("✅ Deployment complete")
            
        except Exception as e:
            print(f"❌ Deployment failed: {e}")
            self.logger.error(f"Deployment error: {e}")
            
    async def run_pre_deployment_checks(self):
        """
        Run checks before deployment.
        
        TODO: Implement pre-deployment validation
        """
        checks = [
            ("System requirements", self.check_system_requirements),
            ("Configuration files", self.check_configuration_files),
            ("Database connectivity", self.check_database_connectivity),
            ("Network access", self.check_network_access)
        ]
        
        for check_name, check_func in checks:
            try:
                result = await check_func()
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}")
                if not result:
                    raise Exception(f"Pre-deployment check failed: {check_name}")
            except Exception as e:
                print(f"   ❌ {check_name}: {e}")
                raise
                
    async def check_system_requirements(self):
        """Check system requirements."""
        # TODO: Check Python version, available memory, disk space
        return True
        
    async def check_configuration_files(self):
        """Check configuration files."""
        # TODO: Validate configuration files exist and are valid
        return True
        
    async def check_database_connectivity(self):
        """Check database connectivity."""
        # TODO: Test InfluxDB connection
        return True
        
    async def check_network_access(self):
        """Check network access."""
        # TODO: Test network connectivity to required services
        return True
        
    async def deploy_configuration(self, environment):
        """Deploy configuration for environment."""
        # TODO: Copy environment-specific configuration
        pass
        
    async def deploy_application(self):
        """Deploy application files."""
        # TODO: Copy application files, set permissions
        pass
        
    async def run_post_deployment_checks(self):
        """Run post-deployment verification."""
        # TODO: Verify deployment was successful
        pass

class PerformanceOptimizer:
    """
    Optimize system performance for production scale.
    
    Learning Goal: Master performance optimization
    """
    
    def __init__(self, monitoring_system):
        self.monitoring_system = monitoring_system
        
    async def optimize_performance(self):
        """
        Continuously optimize system performance.
        
        TODO: Implement performance optimization
        """
        while True:
            try:
                # TODO: Monitor performance metrics
                # TODO: Identify bottlenecks
                # TODO: Apply optimization strategies
                # TODO: Validate improvements
                
                await asyncio.sleep(300)  # Optimize every 5 minutes
                
            except Exception as e:
                print(f"Performance optimization error: {e}")
                await asyncio.sleep(600)

async def main():
    """Phase 3: Master production deployment and monitoring"""
    print("=== PHASE 3: PRODUCTION DEPLOYMENT & MONITORING ===")
    print("Building production-ready deployment and monitoring\n")
    
    # Load configuration
    try:
        with open("config/environments.yaml") as f:
            config = yaml.safe_load(f)
            # Use development environment for learning
            config = config.get('development', config)
    except FileNotFoundError:
        print("Warning: config/environments.yaml not found. Using defaults.")
        config = {
            'health_monitoring': {
                'check_interval_seconds': 30,
                'alert_thresholds': {
                    'cpu_percent': 80,
                    'memory_percent': 85,
                    'disk_percent': 90
                }
            }
        }
    
    # TODO: Initialize health monitoring
    health_monitor = SystemHealthMonitor(config)
    
    # TODO: Initialize deployment manager
    deployment_manager = DeploymentManager(config)
    
    try:
        # TODO: Run deployment procedures
        await deployment_manager.deploy_system("development")
        
        # TODO: Start health monitoring
        await health_monitor.monitor_system_health()
        
    except KeyboardInterrupt:
        print("\nHealth monitoring stopped by user")
    except Exception as e:
        print(f"System error: {e}")

if __name__ == "__main__":
    import time
    asyncio.run(main())
