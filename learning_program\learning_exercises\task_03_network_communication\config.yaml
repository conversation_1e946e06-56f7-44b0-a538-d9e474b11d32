# Phase 3 Configuration - Complete Power Meter Monitoring
# This combines Phase 1 (networking) + Phase 2 (Modbus) + Task 2 (threading)

development:
  power_meters:
    PowerMeter_A:
      ip_address: "127.0.0.1"  # Localhost for simulation
      port: 5020
      device_id: 1
      registers:
        voltage_l1: 0x0000    # Register address for L1 voltage
        voltage_l2: 0x0002    # Register address for L2 voltage
        voltage_l3: 0x0004    # Register address for L3 voltage
        current_l1: 0x0006    # Register address for L1 current
        current_l2: 0x0008    # Register address for L2 current
        current_l3: 0x000A    # Register address for L3 current
        power_total: 0x0034   # Register address for total power
    PowerMeter_B:
      ip_address: "127.0.0.1"
      port: 5021
      device_id: 2
      registers:
        voltage_l1: 0x0000
        voltage_l2: 0x0002
        current_l1: 0x0006
        power_total: 0x0034
  monitoring:
    read_interval: 2.0
    connection_timeout: 3.0
    max_retries: 3
    duration_seconds: 30

production:
  power_meters:
    PowerMeter_A:
      ip_address: "*************"
      port: 502
      device_id: 1
      registers:
        voltage_l1: 0x0000
        voltage_l2: 0x0002
        voltage_l3: 0x0004
        current_l1: 0x0006
        current_l2: 0x0008
        current_l3: 0x000A
        power_total: 0x0034
    PowerMeter_B:
      ip_address: "*************"
      port: 502
      device_id: 2
      registers:
        voltage_l1: 0x0000
        voltage_l2: 0x0002
        voltage_l3: 0x0004
        current_l1: 0x0006
        current_l2: 0x0008
        current_l3: 0x000A
        power_total: 0x0034
  monitoring:
    read_interval: 5.0
    connection_timeout: 5.0
    max_retries: 5
    duration_seconds: 300
