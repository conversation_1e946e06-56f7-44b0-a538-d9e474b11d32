''' To use:
from lib_loggers import set_logger
logger = set_logger()
'''

import os
import logging
import inspect

def set_logger():
    caller_frame = inspect.currentframe().f_back
    caller_file = os.path.basename(inspect.getframeinfo(caller_frame).filename)
    
    # Configures the logger
    log_dir = os.path.join(os.path.dirname(__file__),'logs')
    perferred_log_filename = os.path.join(os.path.dirname(__file__),'logs',caller_file.rsplit('.',1)[0]+'.log')
    
    try:
        os.makedirs(os.path.dirname(perferred_log_filename))
    except FileExistsError:
        pass
    logging.basicConfig(format='%(asctime)s\t%(name)s\t%(thread)d\t%(levelname)s\t%(message)s',
                        datefmt="%Y-%m-%d %H:%M:%S",
                        level=logging.ERROR)
    logger = logging.getLogger(caller_file.rsplit('.',1)[0])
    logger.propagate = False
    formatter = logging.Formatter('%(asctime)s\t%(name)s\t%(thread)d\t%(levelname)s\t%(message)s',"%Y-%m-%d %H:%M:%S")
    fh = logging.FileHandler(perferred_log_filename)
    fh.setLevel(logging.DEBUG)
    fh.setFormatter(formatter)
    logger.addHandler(fh)
    ch = logging.StreamHandler()
    ch.setFormatter(formatter)
    logger.addHandler(ch)
    logger.setLevel(logging.DEBUG)
    return logger
