# 🎯 Task 1: Python Logging Practice

## **Your Mission**
Create a simple temperature monitoring simulator that uses the logging system from `lib_loggers.py`. This will help you understand:
- How to import and use the logger
- Different log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- How logs are written to both file and console

---

## **Step-by-Step Instructions**

### **Step 1: Copy the Logger**
1. Copy the `lib_loggers.py` file into this `learning_exercises` folder
2. This ensures your practice code can import it

### **Step 2: Create Your Practice Script**
Create a file called `temperature_monitor.py` with this basic structure:

```python
# Import the logger from the lib_loggers file
from lib_loggers import set_logger
import time
import random

# Set up the logger
logger = set_logger()

def simulate_temperature_reading():
    """Simulate reading temperature from a sensor"""
    # Generate a random temperature between 18-30°C
    temperature = random.uniform(18.0, 30.0)
    return round(temperature, 1)

def check_temperature_status(temp):
    """Check if temperature is in acceptable range"""
    if temp < 20:
        return "COLD"
    elif temp > 28:
        return "HOT" 
    else:
        return "NORMAL"

def main():
    logger.info("🌡️ Starting temperature monitoring system...")
    
    # TODO: Add your code here (see instructions below)
    
    logger.info("📊 Temperature monitoring completed")

if __name__ == "__main__":
    main()
```

### **Step 3: Your Coding Tasks**

Inside the `main()` function, write code to:

1. **Log the start**: Use `logger.info()` to announce the monitoring start
2. **Simulate 10 temperature readings** in a loop:
   - Call `simulate_temperature_reading()` to get a temperature
   - Call `check_temperature_status(temp)` to get the status
   - Use different log levels based on the status:
     - `logger.debug()` for NORMAL temperatures
     - `logger.warning()` for COLD temperatures  
     - `logger.error()` for HOT temperatures
   - Add a 1-second delay with `time.sleep(1)`
3. **Log completion**: Use `logger.info()` to announce when done

### **Step 4: Expected Log Output**
Your program should create logs like this:
```
2025-07-31 14:30:15    temperature_monitor    12345    INFO    🌡️ Starting temperature monitoring system...
2025-07-31 14:30:16    temperature_monitor    12345    DEBUG   Temperature: 22.4°C - Status: NORMAL
2025-07-31 14:30:17    temperature_monitor    12345    WARNING Temperature: 19.1°C - Status: COLD
2025-07-31 14:30:18    temperature_monitor    12345    ERROR   Temperature: 29.2°C - Status: HOT
...
2025-07-31 14:30:25    temperature_monitor    12345    INFO    📊 Temperature monitoring completed
```

---

## **Bonus Challenges** 🏆

Once you complete the basic task:

### **Challenge 1: Add Error Simulation**
- Randomly simulate a "sensor failure" (5% chance)
- When this happens, use `logger.critical()` to log a critical error
- Skip that reading and continue

### **Challenge 2: Add Statistics**
- Keep track of how many readings were NORMAL, COLD, HOT
- Log a summary at the end using `logger.info()`

### **Challenge 3: Configuration**
- Create a simple config file to set temperature thresholds
- Modify your code to read these values instead of hardcoding 20 and 28

---

## **What You'll Learn**

✅ **Import statements** - How to use code from other files  
✅ **Function calls** - Using the logger functions  
✅ **String formatting** - Creating informative log messages  
✅ **Control flow** - Loops and if/else statements  
✅ **File I/O** - Logs are automatically written to files  
✅ **Error handling** - Different types of log levels  

---

## **Getting Help**

- Check the `logs/` folder that will be created automatically
- Look at both the console output AND the log file
- Compare your output with the expected format above
- Remember: the logger automatically handles file creation and formatting!

**Good luck! 🚀**
