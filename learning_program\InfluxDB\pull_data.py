'''
This is an example to show how to pull lots of data from the InfluxDB database in bulk for plotting.
'''

import numpy as np
import matplotlib.pyplot as plt
from influxdb_client import InfluxDBClient

client = InfluxDBClient(url="http://influxdb.ami.modelfactory.sg:8086",
                        token='OLZ2GVjo1uEFHzbFr35HSED04JXOk9WEzta8BeYeK1b9hCJBUrxwpmhzKwS7EDtMr6wN6A8P6EpghaXif2F35Q==', # Token for "Read endpoint for Python analysis scripts"
                        #token='BAUefNIXOaHhiGcJlHSOXUPeZFsECwKuw3Cz72EUr9fOtWZdLwYlHLUQn7Pzq__qqesPFjXYV2hyjCGItHx__Q==', # Token for "Generic RW token for test bucket"
                        org='266e2e2e067fbe5b',
                        )

query_text = '''from(bucket: "printers_30days")
  |> range(start: -1d, stop: 0d)
  |> filter(fn: (r) => r["_measurement"] == "power_consumption")
  |> filter(fn: (r) => r["eqpt"] == "DMG_SLM30")
  |> filter(fn: (r) => r["_field"] == "Active Power Total (kW)")
  |> aggregateWindow(every: 30s, fn: mean, createEmpty: false)
  |> yield(name: "mean")
'''

query_api = client.query_api()
df_results = query_api.query_data_frame(query=query_text)

if len(df_results)==0:
    raise ValueError('There appears to be no data points returned with your query.')

for _field, df_field in df_results.groupby(['_field']):
    #print(df_result.columns) # ['result', 'table', '_start', '_stop', '_time', '_value', '_field', '_measurement', 'eqpt']
    times = df_field['_time'].values
    values = np.array(df_field['_value'])[:,None]
    plt.plot(times,values,'.-',linewidth=0.5,markersize=1,label=_field)
plt.legend()
plt.xlabel('Time')
plt.ylabel('Value')
plt.show()

