import threading
import time
import random
import yaml
import signal
import sys
from lib_loggers import set_logger

shared_data = {}
data_lock = threading.Lock()
thread_status = {}
status_lock = threading.Lock()

# An event is a thread-safe flag that multiple threads can access
# or modify at the same time without causing race conditions
# because it has its own internal lock
# This allows threads to communicate simple signals
shutdown_flag = threading.Event()

# Load configuration
try:
    with open('config.yaml', 'r') as file:
        config = yaml.safe_load(file)
except Exception as e:
    print(f"Error loading config: {e}")

logger = set_logger()

def update_thread_status(sensor_name, status, message=""):
    """Update thread status for real-time monitoring dashboard"""
    with status_lock:
        thread_status[sensor_name] = {
            'status': status,  # 'STARTING', 'RUNNING', 'FAILED', 'COMPLETED'
            'message': message,
            'last_update': time.time(),
            'thread_id': threading.current_thread().ident
        }

def monitor_thread_health():
    """Continuously monitor all threads for failures and performance issues"""
    logger.info("Thread health monitor started - watching for stuck threads")

    while not shutdown_flag.is_set():
        with status_lock:
            current_time = time.time()
            for sensor_name, status_info in thread_status.items():
                # Detect stuck threads (no updates for 10 seconds)
                if current_time - status_info['last_update'] > 10:
                    if status_info['status'] == 'RUNNING':
                        logger.warning(f"ALERT: {sensor_name} appears stuck - no updates for 10+ seconds")
                        status_info['status'] = 'FAILED'
                        status_info['message'] = 'Thread appears stuck - no recent updates'

        time.sleep(2)  # Check every 2 seconds

    logger.info("Thread health monitor stopped")

# We should display the status of all threads in a real-time dashboard, including:
# - Sensor name
# - Thread status (STARTING, RUNNING, FAILED, COMPLETED)
# - Last update time
# - Thread ID
# - Message
def display_status():
    """Continuously display real-time status of all threads"""
    logger.info("Status dashboard started")

    while not shutdown_flag.is_set():
        with status_lock:
            logger.info("=== LIVE SENSOR STATUS DASHBOARD ===")
            for sensor_name, status_info in thread_status.items():
                logger.info(f"Last update time: {status_info['last_update']} {sensor_name}: {status_info['status']} (Thread-{status_info['thread_id']}) - {status_info['message']}")

        shutdown_flag.wait(5)  # Wait 5 seconds or until shutdown

    logger.info("Status dashboard stopped")


def signal_handler(sig, frame):
    """Handles Ctrl+C gracefully"""
    logger.info("===GRACEFUL SHUTDOWN REQUESTED")
    logger.info("Signaling all sensors to complete current readings...")
    shutdown_flag.set()  # Tell all threads to stop
    logger.info("Please wait while system shuts down cleanly...")

def simulate_sensor_reading(min_temp, max_temp):
    """Simulate reading from a sensor"""
    # Generate a random temperature within sensor range
    temperature = random.uniform(min_temp, max_temp)
    return round(temperature, 1)

def check_temperature_status(temp, min_temp, max_temp):
    """Check if temperature is in acceptable range (reuse from Task 1)"""
    if temp < min_temp:
        return "COLD"
    elif temp > max_temp:
        return "HOT"
    return "NORMAL"

def monitor_single_sensor(sensor_name, sensor_config, duration, env_config):
    """Monitor one sensor for specified duration"""
    update_thread_status(sensor_name, 'STARTING', 'Initializing sensor monitoring')
    logger.info(f"Starting monitoring for {sensor_name}")
    
    # Extract sensor configuration
    min_env_temp = env_config['monitoring']['min_temp']
    max_env_temp = env_config['monitoring']['max_temp']
    min_temp = sensor_config['min_temp']
    max_temp = sensor_config['max_temp']
    response_time = sensor_config['response_time']
    failure_rate = sensor_config['failure_rate']
    
    start_time = time.time()
    elapsed_time = 0
    reading_count = 0

    logger.debug(f"Entering monitoring loop for {sensor_name}")
    update_thread_status(sensor_name, 'RUNNING', 'Monitoring active - collecting data')
    while (not shutdown_flag.is_set()) and (elapsed_time < duration):
        logger.debug(f"Checking for failure for {sensor_name}\tElapsed time: {elapsed_time}")
        has_failed = (random.random() < failure_rate)
        logger.debug(f"Has failed: {has_failed}")

        if has_failed:
            update_thread_status(sensor_name, 'FAILED', 'Sensor has failed')
            logger.critical(f"Sensor {sensor_name} failed")
            update_sensor_data(sensor_name, None, 'FAILED')
        else:
            update_thread_status(sensor_name, 'RUNNING', 'Collecting data')
            temp = simulate_sensor_reading(min_env_temp, max_env_temp)
            status = check_temperature_status(temp, min_temp, max_temp)
            logger.debug(f"Sensor {sensor_name} reading: {temp} ({status})")
            update_sensor_data(sensor_name, temp, status)
            reading_count += 1

        time.sleep(response_time)
        elapsed_time = time.time() - start_time

        # Not strictly necessary since the while loop condition will also catch it,
        # but it's a good practice to check for shutdown at the end of each loop
        if shutdown_flag.is_set():
            logger.info(f"{sensor_name}: Received shutdown signal, stopping gracefully")
            break
    
    update_thread_status(sensor_name, 'COMPLETED', f'Monitoring finished - {reading_count} readings collected')
    logger.info(f"Completed monitoring for {sensor_name}: {reading_count} readings")
    return reading_count

def update_sensor_data(sensor_name, reading, status):
    """Thread-safe function to store sensor readings in central database"""
    with data_lock:
        if sensor_name not in shared_data:
            shared_data[sensor_name] = []
            logger.debug(f"Created new entry for {sensor_name} in shared_data")
        shared_data[sensor_name].append({
            'timestamp': time.time(),
            'temperature': reading,
            'status': status
        })

def generate_report():
    """Generate comprehensive cross-sensor analysis report"""
    # In the comprehensive report, we want average, min, max, and normal count for each sensor
    # We also want to know the total number of readings and failures for each sensor

    # Finally, we want to know the system-wide average, min, max, and normal count
    # And the system-wide failure rate
    with data_lock:
        logger.info("\n=== COMPREHENSIVE SENSOR ANALYSIS REPORT ===")

        total_readings = 0
        total_failures = 0
        total_temperatures = 0
        total_normals = 0
        system_min = 1000
        system_max = 0

        for sensor_name, sensor_data in shared_data.items():
            min = 1000
            max = 0
            normal_count = 0
            failure_count = 0

            for reading in sensor_data:
                if reading['status'] == 'FAILED':
                    failure_count += 1
                    total_failures += 1
                else:
                    if reading['temperature'] < min:
                        min = reading['temperature']
                    if reading['temperature'] > max:
                        max = reading['temperature']
                    if reading['status'] == 'NORMAL':
                        normal_count += 1
                    total_temperatures += reading['temperature']

            total_readings += len(sensor_data)
            total_normals += normal_count
            logger.info(f"{sensor_name}: {len(sensor_data)} readings, min: {min}, max: {max}, normal: {normal_count}, failures: {failure_count}")
        
        if min < system_min:
            system_min = min
        if max > system_max:
            system_max = max

        system_avg = total_temperatures / total_readings if total_readings > 0 else 0.0  # Avoid division by zero error
        logger.info(f"System-wide average temperature: {system_avg:.1f}")
        logger.info(f"System temperature range: {system_min:.1f} to {system_max:.1f}")
        logger.info(f"Total readings: {total_readings}, total failures: {total_failures}")
        logger.info(f"System-wide min: {system_min}, max: {system_max}")
        logger.info(f"Total normal readings: {total_normals}")

        system_failure_rate = (total_failures / total_readings) * 100 if total_readings > 0 else 0.0
        logger.info(f"System-wide failure rate: {system_failure_rate:.1f}%")
        logger.info("=== END OF REPORT ===\n")


def main():
    # Register a signal handler (A handler is a function that is called in response to an event)
    # The signal module allows us to catch operating system signals
    # signal.SIGINT is the signal sent when the user presses Ctrl+C
    # signal.signal() is a function that registers a handler for a given signal
    # Overall, this line means that when the program receives a Ctrl + C,
    # run the function signal_handler() instead of the default behaviour
    signal.signal(signal.SIGINT, signal_handler)  # Handle Ctrl+C

    env_choice = 'development'  # Try changing to 'production'
    config_env = config[env_choice]
    
    logger.info("Starting multi-sensor monitoring system...")
    
    # Get monitoring configuration
    duration = config_env['monitoring']['duration_seconds']
    sensors = config_env['sensors']
    
    logger.info(f"Monitoring {len(sensors)} sensors for {duration} seconds")
    
    threads = {}
    monitoring_threads = {}

    health_thread = threading.Thread(target=monitor_thread_health, name="HealthMonitor")
    monitoring_threads['health'] = health_thread
    health_thread.start()

    status_thread = threading.Thread(target=display_status, name="StatusDisplay")
    monitoring_threads['status'] = status_thread
    status_thread.start()
    
    logger.info("Management threads started - system is now self-monitoring")

    # sensor_config becomes a dictionary containing the sensor's configuration
    for sensor_name, sensor_config in sensors.items():
        thread = threading.Thread(target=monitor_single_sensor, args=(sensor_name, sensor_config, duration, config_env))
        # Stores thread in dictionary with sensor_name as key and thread as value

        # A thread is a complex data structure that contains:
        # target: the function to be executed by the thread
        # args: the arguments to be passed to the function
        # daemon: whether the thread is a daemon thread or not
        # internal state: the state of the thread (running, waiting, etc.)
        # thread_id: the unique identifier of the thread
        # methods: start(), join(), etc.
        # attributes: name, ident, etc.
        # Each thread object is a separate entity living in the computer memory
        # and the dictionary just stores a reference to the thread object

        threads[sensor_name] = thread
        thread.start()

    try:
        for sensor_name, thread in threads.items():
            logger.info(f"Waiting for {sensor_name} to complete...")
            # If timeout, log a warning and move on
            # The thread could still be running in the background
            thread.join(timeout=10.0)  # Wait max 10 seconds per thread
            if thread.is_alive():
                logger.warning(f"{sensor_name} did not stop gracefully (may be stuck)")
            else:
                logger.info(f"{sensor_name} completed")

        logger.info("Stopping management threads...")
        for name, thread in monitoring_threads.items():
            logger.info(f"Waiting for {name} thread to complete...")
            thread.join(timeout=10.0)  # Wait max 10 seconds per thread
            if thread.is_alive():
                logger.warning(f"{name} did not stop gracefully (may be stuck)")

    except KeyboardInterrupt:
        logger.error("Force shutdown requested - some data may be lost")
    
    logger.info("Multi-sensor monitoring completed")

    # There is no guarantee whether the threads will terminate first upon seeing the shutdown flag 
    # or the main thread will reach this line first. Report may be incomplete.
    generate_report()

if __name__ == "__main__":
    main()
