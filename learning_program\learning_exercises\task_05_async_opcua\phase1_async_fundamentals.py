"""
Phase 1: Asynchronous Programming Fundamentals

Learning Goals:
- Master async/await syntax and concepts
- Understand event loops and concurrency
- Practice task management and coordination
- Learn async context managers and resource handling

This phase builds the foundation for industrial async programming.
"""

import asyncio
from asyncio import FIRST_COMPLETED
import time
import random
from datetime import datetime, timezone
import yaml

class AsyncFundamentals:
    def __init__(self):
        """
        Initialize async learning environment.
        
        TODO: Set up async learning infrastructure
        Learning Focus: Understanding async system setup
        """
        print("=== ASYNC FUNDAMENTALS LEARNING ENVIRONMENT ===")
        print("Goal: Master async/await patterns for industrial systems\n")
        
    async def demonstrate_sync_vs_async(self):
        """
        Show the dramatic difference between sync and async approaches.
        
        Learning Goal: Understand why async matters for industrial systems
        Manufacturing Context: Multiple machines need concurrent monitoring
        """
        print("=== SYNC VS ASYNC DEMONSTRATION ===")
        
        # Simulate machine operations with different response times
        machines = {
            "EOS_Printer": 2.0,      # 2 second response time
            "CNC_Machine": 1.5,      # 1.5 second response time  
            "Power_Meter": 3.0,      # 3 second response time
            "Air_Quality": 1.0       # 1 second response time
        }
        
        print("Simulating machine monitoring with different response times:")
        for machine, response_time in machines.items():
            print(f"  {machine}: {response_time}s response time")
        
        # TODO: Implement synchronous version
        print("\n1. Synchronous (blocking) approach:")
        sync_start = time.time()
        
        # TODO: Call each machine sequentially
        for machine_name, response_time in machines.items():
            result = self.simulate_machine_read_sync(machine_name, response_time)
            print(f"  {machine_name}: {result}")
        
        sync_total = time.time() - sync_start
        print(f"Synchronous total time: {sync_total:.2f} seconds")
        
        # TODO: Implement asynchronous version
        print("\n2. Asynchronous (concurrent) approach:")
        async_start = time.time()
        
        # TODO: Call all machines concurrently
        tasks = [self.simulate_machine_read_async(name, time) for name, time in machines.items()]
        # returns a list of the return values of each coroutine passed to ite
        results = await asyncio.gather(*tasks)
        
        async_total = time.time() - async_start
        print(f"Asynchronous total time: {async_total:.2f} seconds")
        
        # TODO: Calculate and display performance improvement
        if sync_total > 0:
            improvement = (sync_total - async_total) / sync_total * 100
            print(f"\nPerformance improvement: {improvement:.1f}%")
            print(f"Efficiency gain: {sync_total/async_total:.1f}x faster")
        
    def simulate_machine_read_sync(self, machine_name, response_time):
        """
        Simulate synchronous machine reading (blocking).
        
        TODO: Implement blocking machine simulation
        Args:
            machine_name (str): Name of the machine
            response_time (float): Simulated response time in seconds
            
        Returns:
            dict: Simulated machine data
        """
        print(f"  Starting read from {machine_name}...")
        
        # TODO: Use time.sleep() to simulate blocking operation
        # TODO: Generate realistic machine data
        # TODO: Return structured data with timestamp
        
        time.sleep(response_time)

        return f"Data from {machine_name}"
        
    async def simulate_machine_read_async(self, machine_name, response_time):
        """
        Simulate asynchronous machine reading (non-blocking).
        
        TODO: Implement non-blocking machine simulation
        Args:
            machine_name (str): Name of the machine
            response_time (float): Simulated response time in seconds
            
        Returns:
            dict: Simulated machine data
        """
        print(f"  Starting async read from {machine_name}...")
        
        # TODO: Use await asyncio.sleep() to simulate non-blocking operation
        # TODO: Generate realistic machine data
        # TODO: Return structured data with timestamp
        
        await asyncio.sleep(response_time)

        return f"Data from {machine_name}"

    async def simple_background_task(self):
        print("Running simple background task!")
        await asyncio.sleep(2)
        print("Simple background task complete!")

    async def practice_task_management(self):
        """
        Learn different patterns for managing async tasks.
        
        Learning Goal: Master task creation, coordination, and cleanup
        Industrial Context: Managing multiple machine monitoring tasks
        """
        print("\n=== TASK MANAGEMENT PATTERNS ===")
        
        # TODO: Pattern 1 - Fire and forget tasks
        print("1. Fire and forget pattern:")
        print("   Use case: Background monitoring that doesn't need immediate results")
        asyncio.create_task(self.simple_background_task())
        print("\tMain code did not wait!")
        await asyncio.sleep(2)
        
        # TODO: Pattern 2 - Gather results from multiple tasks
        print("\n2. Gather pattern (wait for all):")
        print("   Use case: Collecting data from multiple machines simultaneously")
        # TODO: Start multiple tasks and wait for all to complete
        sample_tasks = {
            "task1": 1,
            "task2": 2,
            "task3": 3
        }
        async_tasks_objects = []
        for task_no, response_time in sample_tasks.items():
            async_tasks_objects.append(self.simulate_machine_read_async(task_no, response_time))
        results = await asyncio.gather(*async_tasks_objects)
        for entry in results:
            print(entry)
        
        # TODO: Pattern 3 - Race condition (first to complete)
        print("\n3. Race pattern (first wins):")
        print("   Use case: Getting data from redundant sensors, use fastest response")
        # TODO: Start multiple tasks, return when first completes
        async def slow_task():
            await asyncio.sleep(3)
            return "slow task completed!"
        async def fast_task():
            await asyncio.sleep(1)
            return "fast task completed!"
        async_fastslow_objects = [asyncio.create_task(fast_task()), asyncio.create_task(slow_task())]
        done, pending = await asyncio.wait(async_fastslow_objects, return_when=FIRST_COMPLETED)
        for item in done:
            print(item.result())
        for task in pending:
            task.cancel()
        
        # TODO: Pattern 4 - Timeout handling
        print("\n4. Timeout pattern:")
        print("   Use case: Preventing slow machines from blocking the system")
        # TODO: Implement operations with timeout limits
        async def long_running_task():
            await asyncio.sleep(10)
        try:
            attempt = await asyncio.wait_for(long_running_task(), timeout=2.0)
        except asyncio.TimeoutError as e:
            print(f"Exception type: {type(e)}\tException message: {e}")
        
    async def learn_async_context_managers(self):
        """
        Master async context managers for resource management.
        
        Learning Goal: Understand automatic resource cleanup in async code
        Critical for industrial systems that must handle connection failures
        """
        print("\n=== ASYNC CONTEXT MANAGERS ===")
        
        print("Learning automatic resource management for industrial connections...")
        
        # TODO: Demonstrate async context manager usage
        print("1. Basic async context manager:")
        # TODO: Use AsyncMachineSimulator with async context manager
        async with AsyncMachineSimulator("Test_Machine", 1.0) as machine:
            data = await machine.read_data()
            print(f"Data: {data}")

        # TODO: Show error handling with context managers
        print("\n2. Error handling with context managers:")
        try:
            async with AsyncMachineSimulator("Test_Machine_2", 1.0) as machine:
                raise ValueError("Something went wrong!")  
        except ValueError as e:
            print("valueerror")
        
    async def handle_async_errors(self):
        """
        Learn error handling patterns in async code.
        
        Manufacturing Reality: Networks fail, machines disconnect, timeouts occur
        """
        print("\n=== ASYNC ERROR HANDLING ===")
        
        # TODO: Demonstrate different error scenarios
        print("1. Timeout errors:")
        async with AsyncMachineSimulator("timeout_sim", 10.0) as machine:
            try:
                timeout_attempt = await asyncio.wait_for(machine.read_data(), timeout=2)
            except asyncio.TimeoutError as error:
                print("Machine timed out!")
        
        print("\n2. Connection errors:")
        try: 
            async with AsyncMachineSimulator("connection_sim", response_time=1, connected=False) as machine:
                connection_attempt = await asyncio.wait_for(machine.read_data(), timeout=2)
        except ConnectionRefusedError as error:
            print(f"Error type: {type(error)}\nConnection refused!")
    
        print("\n3. Retry logic with exponential backoff:")
        for i in range(1,5):
            try: 
                async with AsyncMachineSimulator("connection_sim", response_time=1, connected=(i == 4)) as machine:
                    connection_attempt = await asyncio.wait_for(machine.read_data(), timeout=3)
                    print("Succesfully connected!")
                    break
            except ConnectionRefusedError as error:
                exponential_delay = i * i
                print(f"Error type: {type(error)}\nConnection attempt no. {i} refused!\n Attempting to reconnect in {exponential_delay}s")
                await asyncio.sleep(0.3)            
        
        print("\n4. Graceful degradation:")
        # TODO: Show how to continue operating when some machines fail
        graceful_degradation_tasks, machines = [], []
        for i in range(0,10):
            try:
                if i == 5:
                    machines.append(AsyncMachineSimulator(f"machine{i}", response_time=0.2, connected=False))
                else:
                    machines.append(AsyncMachineSimulator(f"machine{i}", response_time=0.2, connected=True))
            except Exception as error:
                print(f"Exception type: {type(error)}")

        async def monitor_one_machine(machine_simulator):
            # This function handles the full lifecycle for one machine
            async with machine_simulator as machine:
                data = await machine.read_data()
                return data
            
        for machine in machines:
            graceful_degradation_tasks.append(monitor_one_machine(machine))
        try:
            graceful_results = await asyncio.gather(*graceful_degradation_tasks, return_exceptions=True)
        except Exception as error:
            print(f"Exception type: {type(error)}")

        for item in graceful_results:
            if isinstance(item, Exception):
                print("Failed to initialize!")
            else:
                # print(f"Successfully initialized {item}")    
                print(f"Successfully initialized ", item)    

        await asyncio.sleep(3)

class AsyncMachineSimulator:
    """
    Simulate async machine behavior for learning.
    
    TODO: Implement realistic async machine simulation
    Learning Goal: Understand async resource management patterns
    """
    
    def __init__(self, name, response_time, connected=True, failure_rate=0.05):
        """
        Initialize async machine simulator.
        
        Args:
            name (str): Machine name
            response_time (float): Simulated response time
            failure_rate (float): Probability of failure (0.0 to 1.0)
        """
        self.name = name
        self.response_time = response_time
        self.failure_rate = failure_rate
        self.connected = connected
        
    async def __aenter__(self):
        """
        Async context manager entry.
        
        TODO: Simulate connection establishment
        """
        print(f"  Connecting to {self.name}...")
        # TODO: Simulate connection delay
        await asyncio.sleep(0.1)
        if self.connected:
            print(f"  ✓ Connected to {self.name}")
        else:
            raise ConnectionRefusedError
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """
        Async context manager exit.
        
        TODO: Simulate connection cleanup
        """
        print(f"  Disconnecting from {self.name}")
        self.connected = False
        # TODO: Handle cleanup even if errors occurred
        if exc_type:
            print(f"  ⚠ Disconnected due to error: {exc_type.__name__}")
        else:
            print(f"  ✓ Clean disconnection from {self.name}")
        
    async def read_data(self):
        """
        Simulate reading data from machine.
        
        TODO: Implement realistic data reading with potential failures
        Returns:
            dict: Simulated machine data or raises exception
        """
        if not self.connected:
            raise ConnectionError(f"Not connected to {self.name}")
            
        # TODO: Simulate network delay
        await asyncio.sleep(self.response_time)
        
        # TODO: Simulate occasional failures
        if random.random() < self.failure_rate:
            raise ConnectionError(f"Communication failure with {self.name}")
            
        # TODO: Return realistic machine data
        return {
            'machine': self.name,
            'timestamp': datetime.now(timezone.utc),
            'temperature': round(random.uniform(20.0, 80.0), 1),
            'status': 'running',
            'data_quality': 'good'
        }

async def main():
    """Phase 1: Master async fundamentals before building complex systems"""
    print("=== PHASE 1: ASYNCHRONOUS PROGRAMMING FUNDAMENTALS ===")
    print("Building foundation for industrial async programming\n")
    
    fundamentals = AsyncFundamentals()
    
    # TODO: Complete each learning exercise
    # Uncomment and implement each section as you work through them
    
    # await fundamentals.demonstrate_sync_vs_async()
    # await fundamentals.practice_task_management()
    # await fundamentals.learn_async_context_managers()
    await fundamentals.handle_async_errors()
    
    print("\n=== PHASE 1 COMPLETE ===")
    print("Key concepts mastered:")
    print("✓ Async/await syntax and event loops")
    print("✓ Concurrent task management patterns")
    print("✓ Async context managers for resource cleanup")
    print("✓ Error handling in asynchronous code")
    print("\nReady for Phase 2: OPC UA Client Development")

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
