"""
Phase 1: Machine Discovery & Analysis

Learning Goals:
- Master network discovery and protocol identification
- Analyze machine capabilities systematically
- Build comprehensive documentation and mapping
- Develop systematic integration approaches

This phase teaches real-world skills for unknown machine integration.
"""

import asyncio
import socket
import subprocess
import yaml
import json
import csv
from datetime import datetime, timezone
from pathlib import Path
import ipaddress
import logging
from typing import Dict, List, Any, Optional

# Import protocol testing libraries
try:
    from asyncua import Client as OPCUAClient
    OPCUA_AVAILABLE = True
except ImportError:
    OPCUA_AVAILABLE = False
    print("Warning: asyncua not available. OPC UA testing disabled.")

try:
    from pyModbusTCP.client import ModbusClient
    MODBUS_AVAILABLE = True
except ImportError:
    MODBUS_AVAILABLE = False
    print("Warning: pyModbusTCP not available. Modbus testing disabled.")

try:
    import requests
    HTTP_AVAILABLE = True
except ImportError:
    HTTP_AVAILABLE = False
    print("Warning: requests not available. HTTP testing disabled.")

class MachineDiscoverySystem:
    """
    Comprehensive machine discovery and analysis system.
    
    Learning Goal: Master systematic approach to unknown machine integration
    Real Project Pattern: Skills needed when adding new machines to production
    """
    
    def __init__(self, config_path="config/discovery_config.yaml"):
        """
        Initialize machine discovery system.
        
        TODO: Set up discovery infrastructure
        """
        self.config = self.load_config(config_path)
        self.discovered_devices = []
        self.analyzed_machines = []
        
        # Set up logging
        logging.basicConfig(
            level=getattr(logging, self.config.get('logging', {}).get('level', 'INFO')),
            format=self.config.get('logging', {}).get('format', '%(asctime)s - %(levelname)s - %(message)s'),
            filename=self.config.get('logging', {}).get('file', 'logs/discovery.log')
        )
        self.logger = logging.getLogger(__name__)
        
        print("=== MACHINE DISCOVERY SYSTEM ===")
        print("Goal: Systematically discover and analyze industrial machines\n")
        
    def load_config(self, config_path):
        """
        Load discovery configuration.
        
        TODO: Implement configuration loading with validation
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            print(f"Warning: Config file {config_path} not found. Using defaults.")
            return self.get_default_config()
            
    def get_default_config(self):
        """
        Get default configuration if config file is missing.
        
        TODO: Implement sensible defaults
        """
        return {
            'network_discovery': {
                'ip_ranges': ['***********/24'],
                'industrial_ports': {
                    4840: 'OPC UA',
                    502: 'Modbus TCP',
                    80: 'HTTP'
                },
                'scan_settings': {
                    'ping_timeout_seconds': 2,
                    'port_scan_timeout_seconds': 3,
                    'max_concurrent_scans': 10
                }
            }
        }
        
    async def discover_network_devices(self, ip_ranges=None):
        """
        Discover devices on the network using multiple techniques.
        
        TODO: Implement comprehensive network discovery
        Learning Goal: Understand how to find unknown machines
        
        Args:
            ip_ranges (list): IP ranges to scan (CIDR notation)
            
        Returns:
            list: List of discovered devices with basic info
        """
        print(f"=== NETWORK DEVICE DISCOVERY ===")
        
        if ip_ranges is None:
            ip_ranges = self.config['network_discovery']['ip_ranges']
            
        print(f"Scanning network ranges: {ip_ranges}")
        
        discovered_devices = []
        
        for ip_range in ip_ranges:
            print(f"\nScanning range: {ip_range}")
            
            # TODO: Method 1 - Ping sweep to find active devices
            active_ips = await self.ping_sweep(ip_range)
            print(f"Found {len(active_ips)} active devices")
            
            # TODO: Method 2 - Port scanning for industrial protocols
            for ip in active_ips:
                device_info = await self.scan_device_ports(ip)
                if device_info['open_ports']:
                    discovered_devices.append(device_info)
                    print(f"  {ip}: {list(device_info['open_ports'].keys())}")
                    
        self.discovered_devices = discovered_devices
        print(f"\nDiscovery complete: {len(discovered_devices)} devices with industrial protocols")
        return discovered_devices
    
    async def ping_sweep(self, ip_range):
        """
        Perform ping sweep to find active devices.
        
        TODO: Implement efficient ping sweep
        """
        active_ips = []
        
        try:
            # TODO: Generate IP addresses from CIDR range
            network = ipaddress.ip_network(ip_range, strict=False)
            
            # TODO: Create ping tasks for concurrent execution
            ping_tasks = []
            for ip in network.hosts():
                if len(ping_tasks) < self.config['network_discovery']['scan_settings']['max_concurrent_scans']:
                    ping_tasks.append(self.ping_host(str(ip)))
                else:
                    # Process batch of pings
                    results = await asyncio.gather(*ping_tasks, return_exceptions=True)
                    for i, result in enumerate(results):
                        if result and not isinstance(result, Exception):
                            active_ips.append(result)
                    ping_tasks = []
                    
            # Process remaining pings
            if ping_tasks:
                results = await asyncio.gather(*ping_tasks, return_exceptions=True)
                for result in results:
                    if result and not isinstance(result, Exception):
                        active_ips.append(result)
                        
        except Exception as e:
            self.logger.error(f"Ping sweep error for {ip_range}: {e}")
            
        return active_ips
    
    async def ping_host(self, ip):
        """
        Ping a single host to check if it's active.
        
        TODO: Implement cross-platform ping
        """
        try:
            # TODO: Use subprocess to ping host
            # Consider: Different ping commands for different OS
            import platform
            
            if platform.system().lower() == 'windows':
                cmd = ['ping', '-n', '1', '-w', '2000', ip]
            else:
                cmd = ['ping', '-c', '1', '-W', '2', ip]
                
            # TODO: Execute ping command with timeout
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=self.config['network_discovery']['scan_settings']['ping_timeout_seconds']
            )
            
            # TODO: Check if ping was successful
            if process.returncode == 0:
                return ip
            else:
                return None
                
        except Exception as e:
            self.logger.debug(f"Ping failed for {ip}: {e}")
            return None
    
    async def scan_device_ports(self, ip):
        """
        Scan device for industrial protocol ports.
        
        TODO: Implement port scanning for industrial protocols
        """
        device_info = {
            'ip': ip,
            'hostname': None,
            'open_ports': {},
            'scan_timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        # TODO: Try to resolve hostname
        try:
            hostname = socket.gethostbyaddr(ip)[0]
            device_info['hostname'] = hostname
        except:
            pass
            
        # TODO: Scan industrial ports
        industrial_ports = self.config['network_discovery']['industrial_ports']
        
        for port, protocol in industrial_ports.items():
            if await self.test_port_open(ip, port):
                device_info['open_ports'][protocol] = port
                
        return device_info
    
    async def test_port_open(self, ip, port):
        """
        Test if a specific port is open on a device.
        
        TODO: Implement efficient port testing
        """
        try:
            # TODO: Create socket connection with timeout
            future = asyncio.open_connection(ip, port)
            reader, writer = await asyncio.wait_for(
                future,
                timeout=self.config['network_discovery']['scan_settings']['port_scan_timeout_seconds']
            )
            
            # TODO: Close connection
            writer.close()
            await writer.wait_closed()
            
            return True
            
        except Exception:
            return False
    
    async def analyze_machine_capabilities(self, device_info):
        """
        Analyze what a discovered machine can do.
        
        TODO: Implement comprehensive capability analysis
        Learning Goal: Understand how to explore unknown machine features
        
        Args:
            device_info (dict): Basic device information from discovery
            
        Returns:
            dict: Detailed capability analysis
        """
        print(f"\n=== ANALYZING MACHINE: {device_info['ip']} ===")
        
        capabilities = {
            'ip': device_info['ip'],
            'hostname': device_info.get('hostname'),
            'protocols': {},
            'data_points': {},
            'analysis_timestamp': datetime.now(timezone.utc).isoformat(),
            'recommendations': [],
            'integration_complexity': 'unknown'
        }
        
        # TODO: Test each potential protocol
        for protocol, port in device_info.get('open_ports', {}).items():
            print(f"Testing {protocol} on port {port}...")
            
            if protocol == 'OPC UA' and OPCUA_AVAILABLE:
                # TODO: Analyze OPC UA capabilities
                opcua_info = await self.analyze_opcua_machine(device_info['ip'], port)
                capabilities['protocols']['opcua'] = opcua_info
                
            elif protocol == 'Modbus TCP' and MODBUS_AVAILABLE:
                # TODO: Analyze Modbus capabilities
                modbus_info = await self.analyze_modbus_device(device_info['ip'], port)
                capabilities['protocols']['modbus'] = modbus_info
                
            elif protocol == 'HTTP' and HTTP_AVAILABLE:
                # TODO: Analyze HTTP API capabilities
                http_info = await self.analyze_http_device(device_info['ip'], port)
                capabilities['protocols']['http'] = http_info
                
        # TODO: Generate integration recommendations
        capabilities['recommendations'] = self.generate_integration_recommendations(capabilities)
        capabilities['integration_complexity'] = self.calculate_integration_complexity(capabilities)
        
        return capabilities
    
    async def analyze_opcua_machine(self, ip, port):
        """
        Analyze OPC UA machine capabilities in detail.
        
        TODO: Implement comprehensive OPC UA analysis
        Learning Goal: Master OPC UA machine exploration
        """
        opcua_info = {
            'connection_status': 'unknown',
            'server_info': {},
            'node_structure': {},
            'data_types': {},
            'security_requirements': {},
            'performance_metrics': {}
        }
        
        if not OPCUA_AVAILABLE:
            opcua_info['connection_error'] = 'asyncua library not available'
            return opcua_info
            
        try:
            # TODO: Test basic connection
            url = f"opc.tcp://{ip}:{port}/"
            
            # TODO: Try connection without authentication first
            async with OPCUAClient(url=url) as client:
                # TODO: Get server information
                opcua_info['connection_status'] = 'connected'
                
                # TODO: Get server endpoints and security info
                endpoints = await client.get_endpoints()
                opcua_info['security_requirements'] = {
                    'endpoints_count': len(endpoints),
                    'security_modes': [ep.SecurityMode for ep in endpoints],
                    'security_policies': [ep.SecurityPolicyUri for ep in endpoints]
                }
                
                # TODO: Browse node structure (limited depth for performance)
                root = client.nodes.objects
                opcua_info['node_structure'] = await self.browse_opcua_nodes(root, max_depth=3)
                
                # TODO: Test read operations and measure performance
                opcua_info['performance_metrics'] = await self.test_opcua_performance(client)
                
        except Exception as e:
            opcua_info['connection_error'] = str(e)
            opcua_info['connection_status'] = 'failed'
            
        return opcua_info
    
    async def browse_opcua_nodes(self, node, max_depth=3, current_depth=0):
        """
        Browse OPC UA node structure.
        
        TODO: Implement efficient node browsing
        """
        if current_depth >= max_depth:
            return {}
            
        node_info = {}
        
        try:
            # TODO: Get node information
            browse_name = await node.read_browse_name()
            node_info['browse_name'] = browse_name.Name
            
            # TODO: Get children (limited to prevent infinite browsing)
            children = await node.get_children()
            
            if len(children) > 0 and current_depth < max_depth:
                node_info['children'] = {}
                
                # TODO: Limit number of children to browse
                for child in children[:10]:  # Limit to first 10 children
                    child_name = await child.read_browse_name()
                    node_info['children'][child_name.Name] = await self.browse_opcua_nodes(
                        child, max_depth, current_depth + 1
                    )
                    
        except Exception as e:
            node_info['error'] = str(e)
            
        return node_info
    
    async def test_opcua_performance(self, client):
        """
        Test OPC UA performance characteristics.
        
        TODO: Implement performance testing
        """
        performance = {
            'read_latency_ms': None,
            'subscription_support': False,
            'batch_read_support': False
        }
        
        try:
            # TODO: Test read latency
            start_time = datetime.now()
            server_time = await client.nodes.server_time.read_value()
            end_time = datetime.now()
            
            latency = (end_time - start_time).total_seconds() * 1000
            performance['read_latency_ms'] = round(latency, 2)
            
            # TODO: Test subscription capabilities
            try:
                subscription = await client.create_subscription(1000, None)
                performance['subscription_support'] = True
                await subscription.delete()
            except:
                performance['subscription_support'] = False
                
        except Exception as e:
            performance['error'] = str(e)
            
        return performance
    
    async def analyze_modbus_device(self, ip, port):
        """
        Analyze Modbus device capabilities.
        
        TODO: Implement comprehensive Modbus analysis
        Learning Goal: Master Modbus device exploration
        """
        modbus_info = {
            'connection_status': 'unknown',
            'device_info': {},
            'register_map': {},
            'data_types': {},
            'performance_metrics': {}
        }
        
        if not MODBUS_AVAILABLE:
            modbus_info['connection_error'] = 'pyModbusTCP library not available'
            return modbus_info
            
        try:
            # TODO: Test Modbus connection
            client = ModbusClient(host=ip, port=port, auto_open=True, auto_close=True, timeout=5)
            
            if client.open():
                modbus_info['connection_status'] = 'connected'
                
                # TODO: Test different unit IDs
                unit_ids = self.config.get('protocol_testing', {}).get('modbus', {}).get('unit_ids_to_test', [1])
                
                for unit_id in unit_ids:
                    client.unit_id = unit_id
                    
                    # TODO: Test register ranges
                    register_ranges = self.config.get('protocol_testing', {}).get('modbus', {}).get('register_ranges', {})
                    
                    for register_type, ranges in register_ranges.items():
                        for range_info in ranges:
                            # TODO: Try to read registers
                            success = await self.test_modbus_registers(client, register_type, range_info)
                            if success:
                                if unit_id not in modbus_info['register_map']:
                                    modbus_info['register_map'][unit_id] = {}
                                modbus_info['register_map'][unit_id][register_type] = range_info
                                
                client.close()
            else:
                modbus_info['connection_status'] = 'failed'
                modbus_info['connection_error'] = 'Could not open connection'
                
        except Exception as e:
            modbus_info['connection_error'] = str(e)
            modbus_info['connection_status'] = 'failed'
            
        return modbus_info
    
    async def test_modbus_registers(self, client, register_type, range_info):
        """
        Test reading from Modbus registers.
        
        TODO: Implement register testing
        """
        try:
            start_addr = range_info['start']
            count = range_info['count']
            
            if register_type == 'holding_registers':
                result = client.read_holding_registers(start_addr, count)
            elif register_type == 'input_registers':
                result = client.read_input_registers(start_addr, count)
            else:
                return False
                
            return result is not None and len(result) > 0
            
        except Exception:
            return False
    
    async def analyze_http_device(self, ip, port):
        """
        Analyze HTTP API capabilities.
        
        TODO: Implement HTTP API analysis
        """
        http_info = {
            'connection_status': 'unknown',
            'endpoints': {},
            'api_info': {},
            'authentication': {}
        }
        
        if not HTTP_AVAILABLE:
            http_info['connection_error'] = 'requests library not available'
            return http_info
            
        try:
            # TODO: Test HTTP connection
            base_url = f"http://{ip}:{port}"
            
            # TODO: Test common endpoints
            common_endpoints = self.config.get('protocol_testing', {}).get('http', {}).get('common_endpoints', ['/'])
            
            for endpoint in common_endpoints:
                url = base_url + endpoint
                try:
                    response = requests.get(url, timeout=5)
                    http_info['endpoints'][endpoint] = {
                        'status_code': response.status_code,
                        'content_type': response.headers.get('content-type', 'unknown'),
                        'content_length': len(response.content)
                    }
                    
                    if response.status_code == 200:
                        http_info['connection_status'] = 'connected'
                        
                except Exception as e:
                    http_info['endpoints'][endpoint] = {'error': str(e)}
                    
        except Exception as e:
            http_info['connection_error'] = str(e)
            http_info['connection_status'] = 'failed'
            
        return http_info

    def generate_integration_recommendations(self, capabilities):
        """
        Generate recommendations for integrating this machine.
        
        TODO: Implement intelligent recommendation system
        Learning Goal: Develop systematic integration planning
        """
        recommendations = []
        
        # TODO: Analyze available protocols and recommend best approach
        protocols = capabilities.get('protocols', {})
        
        if 'opcua' in protocols and protocols['opcua'].get('connection_status') == 'connected':
            recommendations.append({
                'priority': 'high',
                'protocol': 'opcua',
                'reason': 'OPC UA provides rich data model and real-time subscriptions',
                'implementation': 'Use async OPC UA client with subscriptions for real-time monitoring'
            })
            
        if 'modbus' in protocols and protocols['modbus'].get('connection_status') == 'connected':
            recommendations.append({
                'priority': 'medium',
                'protocol': 'modbus',
                'reason': 'Modbus provides reliable data access for power and basic sensors',
                'implementation': 'Use Modbus TCP client with polling for regular data collection'
            })
            
        if 'http' in protocols and protocols['http'].get('connection_status') == 'connected':
            recommendations.append({
                'priority': 'low',
                'protocol': 'http',
                'reason': 'HTTP API provides flexible access but may lack real-time capabilities',
                'implementation': 'Use REST API client with polling for configuration and status'
            })
            
        # TODO: Add specific recommendations based on analysis
        if not recommendations:
            recommendations.append({
                'priority': 'manual',
                'protocol': 'unknown',
                'reason': 'No standard industrial protocols detected',
                'implementation': 'Manual investigation required - check device documentation'
            })
            
        return recommendations
    
    def calculate_integration_complexity(self, capabilities):
        """
        Calculate integration complexity score.
        
        TODO: Implement complexity scoring
        """
        # TODO: Implement scoring algorithm based on:
        # - Number of protocols available
        # - Authentication requirements
        # - Data structure complexity
        # - Error handling requirements
        
        protocols = capabilities.get('protocols', {})
        connected_protocols = [p for p in protocols.values() if p.get('connection_status') == 'connected']
        
        if len(connected_protocols) == 0:
            return 'high'
        elif len(connected_protocols) == 1:
            return 'medium'
        else:
            return 'low'

async def main():
    """Phase 1: Master machine discovery and analysis"""
    print("=== PHASE 1: MACHINE DISCOVERY & ANALYSIS ===")
    print("Building skills for real-world machine integration\n")
    
    # Create discovery system
    discovery_system = MachineDiscoverySystem()
    
    try:
        # TODO: Complete discovery workflow
        print("Step 1: Discovering network devices...")
        devices = await discovery_system.discover_network_devices()
        
        print(f"\nStep 2: Analyzing {len(devices)} discovered machines...")
        for device in devices:
            analysis = await discovery_system.analyze_machine_capabilities(device)
            discovery_system.analyzed_machines.append(analysis)
            
            # TODO: Generate documentation for each machine
            # await discovery_system.generate_machine_documentation(analysis)
            
        print(f"\nStep 3: Analysis complete!")
        print(f"Discovered {len(devices)} devices")
        print(f"Analyzed {len(discovery_system.analyzed_machines)} machines")
        
        # TODO: Generate summary report
        print("\n=== DISCOVERY SUMMARY ===")
        for machine in discovery_system.analyzed_machines:
            protocols = list(machine.get('protocols', {}).keys())
            complexity = machine.get('integration_complexity', 'unknown')
            print(f"  {machine['ip']}: {protocols} (complexity: {complexity})")
            
    except KeyboardInterrupt:
        print("\nDiscovery interrupted by user")
    except Exception as e:
        print(f"Discovery error: {e}")
        
    print("\n=== PHASE 1 COMPLETE ===")
    print("Key skills mastered:")
    print("✓ Network discovery and device identification")
    print("✓ Protocol testing and capability analysis")
    print("✓ Machine documentation and mapping")
    print("✓ Integration planning and recommendations")
    print("\nReady for Phase 2: System Integration & Architecture")

if __name__ == "__main__":
    asyncio.run(main())
