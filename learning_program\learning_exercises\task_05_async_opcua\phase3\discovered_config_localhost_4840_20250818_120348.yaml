development:
  event_loop:
    debug_mode: true
    slow_callback_duration: 0.1
  influxdb:
    batch_size: 50
    bucket: async_manufacturing
    connection_timeout_seconds: 10
    flush_interval_seconds: 5
    org: your-org
    token: your-development-token
    url: http://localhost:8086
  machines:
    CNC_Machine_B:
      monitoring:
        connection_timeout_seconds: 5
        retry_attempts: 5
        retry_delay_seconds: 3
        subscription_interval_ms: 1000
      nodes:
        spindle_speed: ns=2;s=CNC.Spindle.Speed
        status: ns=2;s=CNC.Status.Current
        tool_wear: ns=2;s=CNC.Tools.WearLevel
        vibration: ns=2;s=CNC.Sensors.Vibration
      opcua:
        authentication:
          enabled: false
        namespace: 2
        url: opc.tcp://localhost:4841
      type: cnc_machine
    EOS_Printer_A:
      monitoring:
        connection_timeout_seconds: 10
        retry_attempts: 3
        retry_delay_seconds: 5
        subscription_interval_ms: 500
      nodes:
        build_progress: ns=4;s=EOS.Machine.Process.BuildProgress
        humidity: ns=4;s=EOS.Machine.Environment.Humidity
        pressure: ns=4;s=EOS.Machine.ProcessChamber.Pressure
        status: ns=4;s=EOS.Machine.Status.Current
        temperature: ns=4;s=EOS.Machine.Environment.Temperature
      opcua:
        authentication:
          enabled: false
          password: null
          username: null
        namespace: 4
        url: opc.tcp://localhost:4840
      type: 3d_printer
    Machine_localhost_4840:
      monitoring:
        connection_timeout_seconds: 10
        retry_attempts: 3
        retry_delay_seconds: 5
        subscription_interval_ms: 1000
      nodes:
        buildprogress: ns=2;i=9
        current: ns=2;i=7
        humidity: ns=2;i=5
        pressure: ns=2;i=4
        temperature: ns=2;i=3
      opcua:
        authentication:
          enabled: false
          password: null
          username: null
        namespace: 2
        url: opc.tcp://localhost:4840
      type: unknown
    Power_Monitor_C:
      monitoring:
        connection_timeout_seconds: 15
        retry_attempts: 3
        retry_delay_seconds: 10
        subscription_interval_ms: 2000
      nodes:
        current_total: ns=3;s=Power.Current.Total
        power_active: ns=3;s=Power.Active.Total
        voltage_l1: ns=3;s=Power.Voltage.L1
        voltage_l2: ns=3;s=Power.Voltage.L2
        voltage_l3: ns=3;s=Power.Voltage.L3
      opcua:
        authentication:
          enabled: false
        namespace: 3
        url: opc.tcp://localhost:4842
      type: power_meter
  opcua_simulation:
    enable_server: true
    servers:
    - machine_type: 3d_printer
      node_count: 25
      port: 4840
      update_interval_ms: 1000
    - machine_type: cnc_machine
      node_count: 15
      port: 4841
      update_interval_ms: 500
    - machine_type: power_meter
      node_count: 10
      port: 4842
      update_interval_ms: 2000
  performance:
    async_timeout_seconds: 30
    connection_pool_size: 5
    data_buffer_size: 1000
    max_concurrent_connections: 10
  system_health:
    check_interval_seconds: 30
    connection_timeout_threshold_seconds: 60
    data_rate_threshold_per_minute: 10
    error_rate_threshold_percent: 5
production:
  influxdb:
    batch_size: 100
    bucket: ami_eqpt
    flush_interval_seconds: 10
    org: AMI
    token: your-production-token
    url: http://influxdb.ami.modelfactory.sg:8086
  machines:
    EOS_SI3654:
      monitoring:
        connection_timeout_seconds: 30
        retry_attempts: 10
        retry_delay_seconds: 15
        subscription_interval_ms: 1000
      nodes:
        pressure: ns=4;s=EOS.Machine.ProcessChamber.Pressure
        temperature: ns=4;s=EOS.Machine.Environment.Temperature
      opcua:
        authentication:
          enabled: true
          password: your-production-password
          username: client1
        namespace: 4
        url: opc.tcp://**************:4843
      type: 3d_printer
