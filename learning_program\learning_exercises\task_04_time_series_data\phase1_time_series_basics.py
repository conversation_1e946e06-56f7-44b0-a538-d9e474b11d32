"""
Phase 1: Time Series Fundamentals

Learning Goals:
- Master InfluxDB data model (measurements, tags, fields, timestamps)
- Practice creating well-structured data points
- Understand data modeling decisions for manufacturing scenarios
- Learn basic data writing operations

This phase focuses on understanding concepts rather than building complex systems.
"""

import yaml
import time
import random
import lib_loggers
from datetime import datetime, timezone, timedelta
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS

logger = lib_loggers.set_logger()

class TimeSeriesBasics:
    def __init__(self):
        """
        Task 1: Initialize InfluxDB client using configuration.
        
        TODO: Load config and create client connection
        Learning Focus: Understanding client setup and connection management
        """
        env_choice = 'development'

        # TODO: Load configuration from config.yaml
        try:
            with open("config.yaml") as f:
                config = yaml.safe_load(f)
                self.env_config = config[env_choice]
                logger.debug(f"Config loaded: {config}")
        except Exception as e:
            print(f"Error type: {type(e)}\tError message: {e}")
        
        # TODO: Extract InfluxDB connection details
        if config:
            influxdb_config = config[env_choice]['influxdb']

        # TODO: Create InfluxDBClient instance
        try:
            self.client = InfluxDBClient(
                url=influxdb_config['url'],
                token=influxdb_config['token'],
                org=influxdb_config['org']
            )
            logger.debug(f"Client created: {self.client}")
        except Exception as e:
            print(f"Error type: {type(e)}\tError message: {e}")

        # TODO: Create write_api for data operations
        try:
            self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
            logger.debug(f"Write API created: {self.write_api}")
        except Exception as e:
            print(f"Error type: {type(e)}\tError message: {e}")
        
        print("TimeSeriesBasics initialized")
        
    def create_manufacturing_data_point(self, measurement, tags, fields, timestamp=None):
        """
        Task 2: Create a properly structured data point for manufacturing data.
        
        This is the core skill - understanding how to structure time series data.
        
        Args:
            measurement (str): The measurement name (like "machine_sensors")
            tags (dict): Metadata for filtering {"machine": "EOS_01", "type": "temperature"}
            fields (dict): Actual values {"temperature": 245.6, "humidity": 45.2}
            timestamp (datetime): When measurement was taken (optional)
            
        Returns:
            Point: InfluxDB Point object ready for writing
            
        TODO: Implement data point creation
        Learning Questions:
        1. How do you create a Point object with a measurement name?
        2. How do you add multiple tags efficiently?
        3. How do you add multiple fields?
        4. How do you handle timestamp formatting?
        5. What happens if timestamp is None?
        """
        # TODO: Create Point object with measurement name
        try:
            point = Point(measurement)
            logger.debug(f"Point created: {point}")
        except Exception as e:
            print(f"Error type: {type(e)}\tError message: {e}")

        # TODO: Add all tags from the tags dictionary
        for tag_key, tag_value in tags.items():
            point.tag(tag_key, tag_value)
        logger.debug(f"Tags added: {tags}")

        # TODO: Add all fields from the fields dictionary  
        for field_key, field_value in fields.items():
            point.field(field_key, field_value)
        logger.debug(f"Fields added: {fields}")

        # TODO: Set timestamp if provided, otherwise use current time
        if timestamp:
            point.time(timestamp)
        else:
            point.time(datetime.now(timezone.utc))
        logger.debug(f"Timestamp added: {timestamp}")

        # TODO: Return the completed Point object
        return point
        
    
    def demonstrate_data_modeling_decisions(self):
        """
        Task 3: Show different approaches to organizing the same data.
        
        Learning Goal: Understand how data modeling affects query performance
        This is critical thinking - there's no single "right" answer!
        """
        print("=== DATA MODELING DEMONSTRATION ===")
        
        # Scenario: Temperature sensor reading from Machine A
        temperature_reading = 245.6
        machine_id = "EOS_SI3654"
        sensor_location = "print_chamber"
        timestamp = datetime.now(timezone.utc)
        
        print(f"Scenario: Temperature reading of {temperature_reading}°C from {machine_id}")
        print("Exploring different data modeling approaches...\n")
        
        # TODO: Create the same data using different modeling approaches
        
        # Approach 1: Separate measurement per sensor type
        print("Approach 1: Separate measurement per sensor type")
        # TODO: Create point with measurement="temperature_sensors"
        point = self.create_manufacturing_data_point(
            measurement="temperature_sensors",
            tags={"machine_id": machine_id, "location": sensor_location},
            fields={"temperature_celsius": temperature_reading},
            timestamp=timestamp
        )
        # Think: What are the pros/cons of this approach?
        # pros: Each measurement contains only one type of data, easy to query
        # cons: Harder to manage as number of sensor types increases
        
        # Approach 2: Single measurement with sensor type as tag
        print("Approach 2: Single measurement with sensor type as tag")
        # TODO: Create point with measurement="machine_sensors" and sensor_type tag
        point = self.create_manufacturing_data_point(
            measurement="machine_sensors",
            tags={"machine_id": machine_id, "location": sensor_location, "sensor_type": "temperature"},
            fields={"value": temperature_reading},
            timestamp=timestamp
        )
        # Think: How does this affect querying?
        # pros: Easier to manage as number of sensor types increases
        # cons: More complex queries for specific sensor types
        
        # Approach 3: Machine-centric measurement
        print("Approach 3: Machine-centric measurement")
        # TODO: Create point with measurement="eos_si3654_data"
        point = self.create_manufacturing_data_point(
            measurement="eos_si3654_data",
            tags={"location": sensor_location, "sensor_type": "temperature"},
            fields={"value": temperature_reading},
            timestamp=timestamp
        )
        # Think: When would this approach be useful?
        # pros: Easy to query all data for a single machine
        # cons: Harder to query across multiple machines
        
        print("\nReflection Questions:")
        print("\n- Which approach would be best for 'show me all temperature data'?")
        print("\n Approach 1 would be the best for 'show me all temperature data'.")
        print("\n- Which approach would be best for 'show me all data from one machine'?")
        print("\n Approach 3 would be the best for 'show me all data from one machine'.")
        print("\n- How would each approach scale with 100+ machines?")
        print("\n Approach 2 would scale the best with 100+ machines, and approach 3 would scale the worst.\n")
        
    def practice_data_validation(self):
        """
        Learn to validate data before storing it.
        
        Manufacturing Reality: Sensors sometimes return invalid data
        This teaches defensive programming and data quality awareness.
        """
        print("\n=== DATA VALIDATION PRACTICE ===")
        
        # Test cases representing real manufacturing scenarios
        test_cases = [
            {"value": 245.6, "sensor": "temperature", "description": "Normal reading"},
            {"value": -999.0, "sensor": "temperature", "description": "Sensor error code"},
            {"value": 500.0, "sensor": "temperature", "description": "Impossible high value"},
            {"value": None, "sensor": "temperature", "description": "Missing data"},
            {"value": "ERROR", "sensor": "temperature", "description": "Non-numeric data"},
        ]
        
        for test_case in test_cases:
            print(f"\nTesting: {test_case['description']}")
            print(f"Value: {test_case['value']}")
            
            # TODO: Implement validation logic
            # Questions to consider:
            # 1. What makes sensor data "invalid"?
            # 2. What are realistic ranges for different sensor types?
            # 3. How do you handle missing or null values?
            # 4. Should you store invalid data with a quality flag, or reject it?
            
            is_valid = self.validate_sensor_reading(
                test_case['value'], 
                test_case['sensor']
            )
            
            print(f"Validation result: {'VALID' if is_valid else 'INVALID'}")
    
    def validate_sensor_reading(self, value, sensor_type):
        """
        Task 4: Validate a sensor reading for quality and realism.
        
        TODO: Implement validation logic
        Consider:
        - Type checking (is it a number?)
        - Range checking (realistic values for sensor type?)
        - Special values (error codes like -999?)
        - Missing data handling
        
        Args:
            value: The sensor reading to validate
            sensor_type: Type of sensor (temperature, pressure, power, etc.)
            
        Returns:
            bool: True if valid, False if invalid
        """
        # TODO: Implement validation rules
        if value is None:
            logger.debug(f"Value is None")
            return False
        if not isinstance(value, (int, float)):
            logger.debug(f"Value is not a number")
            return False
        if value == -999.0:
            logger.debug(f"Value is -999.0. Check against list of error codes.")
            return False

        for sensor_params in self.env_config['sensors'].values():
            if sensor_params['tags']['sensor_type'] == sensor_type:
                logger.debug(f"Validating {value} for {sensor_type}")
                min_value = sensor_params['min_value']
                max_value = sensor_params['max_value']
                normal_min_value, normal_max_value = sensor_params['normal_range']

                if min_value <= value <= max_value:
                    if value < normal_min_value or value > normal_max_value:
                        logger.warning(f"Value ({value}) is valid, but out of normal range for {sensor_type}")
                    logger.debug(f"Value ({value}) is valid and within normal range for {sensor_type}")
                    return True
                else:
                    logger.critical(f"Value ({value}) out of range for {sensor_type}")
                    return False
                
        logger.debug(f"Sensor type {sensor_type} not found in config")
        return False
    
    # create_manufacturing_data_point(self, measurement, tags, fields, timestamp=None)
    def write_sample_data(self):
        """
        Task 5: Write some sample data to test your InfluxDB connection.
        
        TODO: Create and write sample manufacturing data points
        Learning Focus: Basic write operations and error handling
        """
        print("\n=== WRITING SAMPLE DATA ===")
        
        # TODO: Create several sample data points representing different sensors
        # TODO: Write them to InfluxDB
        # TODO: Handle any connection or write errors
        # TODO: Confirm successful writes
        
        sample_data = [
            {
                "measurement": "machine_sensors",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "sensor_type": "temperature", "location": "print_chamber"},
                "fields": {"temperature_celsius": 250.0},
                "timestamp": datetime.now(timezone.utc)
            },
            {
                "measurement": "machine_sensors",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "sensor_type": "pressure", "location": "build_chamber"},
                "fields": {"pressure_bar": 1.00},
                "timestamp": datetime.now(timezone.utc)
            },
            {
                "measurement": "power_consumption",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "source": "power_meter"},
                "fields": {"power_kw": 15.0},
                "timestamp": datetime.now(timezone.utc)
            },
            {
                "measurement": "machine_sensors",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "sensor_type": "temperature", "location": "print_chamber"},
                "fields": {"temperature_celsius": 260.0},
                "timestamp": datetime.now(timezone.utc) - timedelta(minutes=10)
            },
            {
                "measurement": "machine_sensors",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "sensor_type": "pressure", "location": "build_chamber"},
                "fields": {"pressure_bar": 0.85},
                "timestamp": datetime.now(timezone.utc) - timedelta(minutes=10)
            },
            {
                "measurement": "machine_sensors",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "sensor_type": "temperature", "location": "print_chamber"},
                "fields": {"temperature_celsius": 250.0},
                "timestamp": datetime.now(timezone.utc) - timedelta(minutes=10)
            },
            {
                "measurement": "machine_sensors",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "sensor_type": "pressure", "location": "build_chamber"},
                "fields": {"pressure_bar": 1.00},
                "timestamp": datetime.now(timezone.utc) - timedelta(minutes=10)
            },
            {
                "measurement": "power_consumption",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "source": "power_meter"},
                "fields": {"power_kw": 15.0},
                "timestamp": datetime.now(timezone.utc)
            },
            {
                "measurement": "machine_sensors",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "sensor_type": "temperature", "location": "print_chamber"},
                "fields": {"temperature_celsius": 260.0},
                "timestamp": datetime.now(timezone.utc) - timedelta(minutes=10)
            },
            {
                "measurement": "machine_sensors",
                "tags": {"machine_id": "EOS_SI3654", "machine_type": "3d_printer", "sensor_type": "pressure", "location": "build_chamber"},
                "fields": {"pressure_bar": 0.85},
                "timestamp": datetime.now(timezone.utc) - timedelta(minutes=10)
            }
        ]


        points = []
        for data_point in sample_data:
            try:
                # Get the field value (assumes only one field per sample)
                # list() converts dictionary view into a list so that we can access the index
                value = list(data_point['fields'].values())[0]
                sensor_type = data_point['tags'].get('sensor_type')
                
                # Handle power consumption case (no sensor_type tag)
                if sensor_type is None and data_point['measurement'] == 'power_consumption':
                    sensor_type = 'power'
                
                if sensor_type is None:
                    logger.warning(f"No sensor_type found for data point: {data_point}")
                    continue

                if self.validate_sensor_reading(value, sensor_type):
                    point = self.create_manufacturing_data_point(
                        data_point['measurement'],
                        data_point['tags'],
                        data_point['fields'],
                        data_point['timestamp']
                    )
                    points.append(point)
                    # self.write_api.write(bucket=self.env_config['influxdb']['bucket'], record=point)
                    logger.debug(f"Data point written: {point}")
                else:
                    logger.warning(f"Invalid data point: {data_point}")
            except Exception as e:
                logger.error(f"Error writing data point: {e}")

        try:
            if points:
                self.write_api.write(bucket=self.env_config['influxdb']['bucket'], record=points)
            else:
                logger.info("No valid data points to write")
        except Exception as e:
            logger.error(f"Error writing data points: {e}")

def main():
    """Phase 1: Master the fundamentals before building complex systems"""
    print("=== PHASE 1: TIME SERIES FUNDAMENTALS ===")
    print("Goal: Understand data modeling and basic operations\n")
    
    basics = TimeSeriesBasics()
    
    # Learning exercises - complete each one to build understanding
    basics.demonstrate_data_modeling_decisions()
    basics.practice_data_validation()
    basics.write_sample_data()
    
    print("\n=== PHASE 1 COMPLETE ===")
    print("Key concepts mastered:")
    print("✓ InfluxDB data model (measurements, tags, fields)")
    print("✓ Data point creation and structuring")
    print("✓ Data validation and quality control")
    print("✓ Basic write operations")
    print("\nReady for Phase 2: Data Collection & Storage")

if __name__ == "__main__":
    main()
