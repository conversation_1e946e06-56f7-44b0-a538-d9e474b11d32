# Machine discovery configuration
# Used for Phase 1: Machine Discovery & Analysis

# Network discovery settings
network_discovery:
  # IP ranges to scan for machines
  ip_ranges:
    - "***********/24"      # Local network
    - "*************/24"    # Production network (from real project)
    - "************/24"     # Machine network (from real project)
    
  # Ports to scan for industrial protocols
  industrial_ports:
    4840: "OPC UA"
    4843: "OPC UA (EOS)"
    502: "Modbus TCP"
    8899: "Modbus TCP (Custom)"
    1883: "MQTT"
    8883: "MQTT over SSL"
    80: "HTTP"
    443: "HTTPS"
    8080: "HTTP Alternative"
    9999: "Unknown Protocol"
    
  # Scan settings
  scan_settings:
    ping_timeout_seconds: 2
    port_scan_timeout_seconds: 3
    max_concurrent_scans: 50
    scan_delay_ms: 10

# Protocol testing configuration
protocol_testing:
  opcua:
    connection_timeout_seconds: 10
    authentication_attempts:
      - {username: null, password: null}           # Anonymous
      - {username: "client1", password: ""}        # Username only
      - {username: "admin", password: "admin"}     # Common defaults
      - {username: "operator", password: "operator"}
      - {username: "guest", password: "guest"}
    
    # Common OPC UA endpoints to try
    endpoint_variations:
      - "/"
      - "/OPCUA/SimulationServer"
      - "/UA/SampleServer"
      - "/opcua"
      
    # Node browsing settings
    browsing:
      max_depth: 5
      max_nodes_per_level: 100
      timeout_per_node_seconds: 1
      
  modbus:
    connection_timeout_seconds: 5
    unit_ids_to_test: [1, 2, 3, 247]  # Common unit IDs
    
    # Common register ranges to test
    register_ranges:
      holding_registers:
        - {start: 0, count: 10}      # Common starting registers
        - {start: 40000, count: 10}  # Modicon addressing
        - {start: 100, count: 10}    # Custom ranges
      input_registers:
        - {start: 0, count: 10}
        - {start: 30000, count: 10}
        
    # Data type detection
    data_type_detection:
      test_float32: true
      test_int16: true
      test_int32: true
      test_string: true
      
  http:
    connection_timeout_seconds: 10
    common_endpoints:
      - "/"
      - "/api"
      - "/api/v1"
      - "/status"
      - "/health"
      - "/info"
      - "/data"
    
    # HTTP methods to try
    methods: ["GET", "POST"]
    
    # Common authentication
    authentication:
      - {type: "none"}
      - {type: "basic", username: "admin", password: "admin"}
      - {type: "basic", username: "api", password: "api"}

# Machine identification patterns
machine_identification:
  # Patterns to identify machine types from network responses
  identification_patterns:
    eos_3d_printer:
      opcua_server_name_patterns:
        - "EOS"
        - "M290"
        - "3D Printer"
      opcua_namespace_patterns:
        - "EOS.Machine"
        - "Additive Manufacturing"
      http_response_patterns:
        - "EOS GmbH"
        - "Electro Optical Systems"
        
    power_meter:
      modbus_device_id_patterns:
        - "SDM630"
        - "Eastron"
        - "Power Meter"
      modbus_register_patterns:
        - {address: 0, expected_range: [200, 250]}  # Voltage
        - {address: 52, expected_range: [0, 1000]}  # Power
        
    cnc_machine:
      opcua_server_name_patterns:
        - "CNC"
        - "Machining Center"
        - "Mill"
        - "Lathe"
      opcua_namespace_patterns:
        - "CNC.Machine"
        - "Machining"
        
    environmental_sensor:
      modbus_register_patterns:
        - {address: 100, expected_range: [0, 50]}   # Temperature
        - {address: 101, expected_range: [0, 100]}  # Humidity
      http_endpoint_patterns:
        - "/temperature"
        - "/humidity"
        - "/environmental"

# Documentation generation settings
documentation:
  output_directory: "discovered_machines/"
  
  # File formats to generate
  formats:
    yaml: true      # Machine configuration files
    json: true      # API-friendly format
    markdown: true  # Human-readable documentation
    csv: true       # Spreadsheet format
    
  # Documentation sections to include
  sections:
    connection_details: true
    data_point_mapping: true
    protocol_capabilities: true
    integration_recommendations: true
    configuration_templates: true
    troubleshooting_notes: true
    
  # Template settings
  templates:
    machine_config_template: "templates/machine_config.yaml.j2"
    documentation_template: "templates/machine_docs.md.j2"
    integration_guide_template: "templates/integration_guide.md.j2"

# Analysis and recommendation settings
analysis:
  # Criteria for integration recommendations
  recommendation_criteria:
    protocol_preference_order: ["opcua", "modbus", "http", "mqtt"]
    data_quality_thresholds:
      response_time_ms: 1000
      success_rate_percent: 95
      data_consistency_percent: 90
      
    # Performance considerations
    performance_factors:
      max_concurrent_connections: 10
      preferred_update_interval_ms: 1000
      batch_size_recommendations: 50
      
  # Integration complexity scoring
  complexity_scoring:
    factors:
      protocol_complexity: 0.3
      authentication_complexity: 0.2
      data_structure_complexity: 0.2
      error_handling_complexity: 0.15
      maintenance_complexity: 0.15
      
    # Scoring ranges (1-10, 10 being most complex)
    protocol_scores:
      opcua: 7
      modbus: 4
      http: 3
      mqtt: 5
      proprietary: 9

# Logging and debugging
logging:
  level: "INFO"
  file: "logs/discovery.log"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Discovery-specific logging
  log_successful_connections: true
  log_failed_connections: false  # Too noisy for production
  log_data_samples: true
  log_performance_metrics: true
