"""
Realistic Manufacturing Sensor Simulator

This module simulates how real manufacturing sensors behave:
- Gradual value changes (not random jumps)
- Occasional failures and error conditions
- Different response times for different sensor types
- Realistic value ranges for manufacturing equipment

Learning Goal: Understand real-world sensor behavior patterns
"""

import random
import time
import math
from datetime import datetime, timezone

class ManufacturingSensorSimulator:
    """
    Simulate realistic manufacturing sensor behavior.
    
    Real sensors don't just return random values - they have patterns:
    - Temperature changes gradually
    - Pressure fluctuates around set points
    - Power consumption varies with machine activity
    - Sensors occasionally fail or return error values
    """
    
    def __init__(self, sensor_name, sensor_config):
        """
        Initialize sensor simulator with configuration.
        
        Args:
            sensor_config (dict): Configuration from config.yaml for this sensor
        """
        self.config = sensor_config
        self.sensor_name = sensor_name

        self.measurement = sensor_config['measurement']
        self.tags = sensor_config['tags']
        self.field_name = sensor_config['field_name']
        self.min_value = sensor_config['min_value']
        self.max_value = sensor_config['max_value']
        self.normal_range = sensor_config['normal_range']
        self.response_time_seconds = sensor_config['response_time_seconds']
        self.failure_rate = sensor_config['failure_rate']
        
        # Initialize sensor state
        self.last_value = None
        self.last_read_time = None
        self.failure_count = 0
        self.consecutive_failures = 0
        
        # Calculate initial value in normal range, rounded to 2 decimal places
        self.current_value = round(random.uniform(self.normal_range[0], self.normal_range[1]), 2)
        
        print(f"Initialized sensor: {self.sensor_name}")
        
    def read_sensor_value(self):
        """
        Simulate reading from a real sensor with realistic behavior.
        
        Manufacturing Reality: Sensors don't always work perfectly
        - Sometimes they fail temporarily (return None or error values)
        - Values change gradually, not randomly
        - Occasional outliers occur due to process variations
        - Response time varies by sensor type
        
        Returns:
            float or None: Sensor reading, or None if sensor failed
            
        TODO: Implement realistic sensor simulation
        Consider:
        1. How do real sensor values change over time?
        2. What types of failures occur in manufacturing?
        3. How do you simulate gradual changes vs sudden spikes?
        4. When should you return None (sensor failure)?
        5. How do you simulate different sensor response characteristics?
        """
        current_time = datetime.now(timezone.utc)
        
        # TODO: Simulate sensor response time delay
        response_delay= self.response_time_seconds
        
        # TODO: Simulate sensor failures
        failure_rate = self.failure_rate
        spike_rate = 0.1

        # Simulate sensor failure
        if random.random() < failure_rate:
            # Do not update self.current_value, just return None to indicate failure
            self.last_read_time = current_time
            return None
        elif random.random() < spike_rate:
            # Simulate a spike
            new_value = random.uniform(self.min_value, self.max_value)
            self.current_value = new_value
        else:
            # Normal gradual change
            self.current_value = self._simulate_gradual_change()
        
        # TODO: Update sensor state for next reading
        self.last_value = self.current_value
        self.last_read_time = current_time
        
        # TODO: Return the simulated sensor reading
        return self.current_value
    
    def _simulate_gradual_change(self):
        """
        Simulate how sensor values change gradually over time.
        
        TODO: Implement realistic value evolution
        Different sensor types change differently:
        - Temperature: Gradual changes, thermal inertia
        - Pressure: Quick responses to process changes
        - Power: Varies with machine activity cycles
        
        Consider: How would you model each type of sensor behavior?
        """
        # TODO: Get current normal range and limits
        normal_min, normal_max = self.normal_range
        min_value, max_value = self.min_value, self.max_value

        # TODO: Calculate realistic change amount based on sensor type
        growth_factor = 1.04

        # TODO: Apply change while staying within realistic bounds
        new_value = self.current_value * growth_factor
        if new_value > max_value:
            lower_half = (normal_min, (normal_max - normal_min) / 2)
            new_value = random.uniform(*lower_half)
        
        new_value = max(min_value, min(max_value, new_value))
        return round(new_value, 2)
    
    def _should_sensor_fail(self):
        """
        Determine if sensor should fail this reading.
        
        TODO: Implement failure simulation
        Consider:
        - Random failures based on failure_rate
        - Consecutive failure patterns (sensors don't always recover immediately)
        - Different types of failures (temporary vs persistent)
        
        Returns:
            bool: True if sensor should fail this reading
        """
        # TODO: Implement failure logic
        pass
    
    def _generate_error_value(self):
        """
        Generate realistic sensor error values.
        
        Manufacturing sensors often return specific error codes:
        - -999.0 for communication errors
        - Very high/low values for sensor malfunctions
        - None for complete sensor failure
        
        TODO: Implement error value generation
        """
        # TODO: Return realistic error values based on sensor type
        pass
    
    def get_sensor_info(self):
        """
        Get information about current sensor state.
        
        Useful for debugging and monitoring sensor simulation.
        
        Returns:
            dict: Current sensor state information
        """
        return {
            'sensor_name': self.sensor_name,
            'current_value': self.current_value,
            'last_read_time': self.last_read_time,
            'failure_count': self.failure_count,
            'consecutive_failures': self.consecutive_failures,
            'config': self.config
        }

def test_sensor_simulation():
    """
    Test the sensor simulator with different configurations.
    
    This helps you understand how different sensor types behave.
    """
    print("=== TESTING SENSOR SIMULATION ===")
    
    # Test configuration for a temperature sensor
    temp_sensor_config = {
        'field_name': 'temperature_celsius',
        'min_value': 200.0,
        'max_value': 280.0,
        'normal_range': [240.0, 260.0],
        'response_time_seconds': 1.5,
        'failure_rate': 0.02
    }
    
    # TODO: Create sensor simulator
    # TODO: Read values multiple times to see behavior patterns
    # TODO: Print results to understand sensor behavior
    
    print("Temperature sensor test:")
    temp_sensor = ManufacturingSensorSimulator(temp_sensor_config)
    
    for i in range(10):
        value = temp_sensor.read_sensor_value()
        print(f"Reading {i+1}: {value}")
        time.sleep(0.5)  # Simulate time between readings

if __name__ == "__main__":
    test_sensor_simulation()
