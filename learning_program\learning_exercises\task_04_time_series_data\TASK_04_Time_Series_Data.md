# 📊 Task 4: Time Series Data & InfluxDB Integration

## 🎯 **The Manufacturing Data Challenge**

### **Why Time Series Databases Matter in Manufacturing**

Imagine you're a production engineer monitoring a 3D printing facility. Every second, your machines generate data:
- **Temperature sensors**: 245.6°C, 246.1°C, 245.8°C...
- **Power meters**: 15.2kW, 15.7kW, 15.1kW...
- **Pressure readings**: 2.3 bar, 2.4 bar, 2.2 bar...

**The Challenge**: How do you store millions of data points efficiently and answer questions like:
- "What was the average temperature during the last print job?"
- "Which machine consumed the most power yesterday?"
- "Show me the pressure trend when the quality issue occurred."

**Traditional databases** (MySQL, PostgreSQL) struggle with this because:
- They're designed for transactions, not continuous sensor data
- Queries become slow with millions of time-stamped records
- Storage becomes inefficient for repetitive time-based data

**Time Series Databases** (like InfluxDB) excel because:
- Optimized for time-stamped data with automatic compression
- Built-in functions for time-based aggregations and analysis
- Efficient storage of sensor data patterns

---

## 🧠 **Core Concepts You Must Master**

### **1. The InfluxDB Data Model**

Think of InfluxDB like a specialized filing system for time-based data:

```
📊 MEASUREMENT (like a database table)
├── 🏷️  TAGS (indexed metadata for fast filtering)
│   ├── machine_id: "EOS_SI3654"
│   ├── location: "Building_A"
│   └── sensor_type: "temperature"
├── 📈 FIELDS (actual measured values)
│   ├── temperature: 245.6
│   ├── humidity: 45.2
│   └── pressure: 2.3
└── ⏰ TIMESTAMP (when the measurement was taken)
    └── 2025-01-15T14:30:25Z
```

**Key Insight**: Tags are for **grouping and filtering**, Fields are for **actual sensor values**.

### **2. Real Project Data Patterns**

From the actual power meter project:
```python
# This is how the real project stores power data:
point = influxdb_client.Point("power_consumption")
point = point.tag("eqpt", "EOS_SI3654")           # Which machine
point = point.tag("source", "bridged_SDM630")     # Data source type
point = point.field("Active Power Total (kW)", 15.2)  # Actual power reading
point = point.field("Voltage A-N (V)", 231.5)     # Voltage reading
```

From the OPC UA project:
```python
# This is how machine sensor data is stored:
data = Point("printer stats")
data = data.tag("eqpt", "SI3654")                 # Machine identifier
data = data.field("EOS.Machine.Environment.Temperature", 245.6)  # Sensor reading
```

### **3. Why This Data Model Works**

**Tags enable fast queries**:
- "Show me all data from machine EOS_SI3654" → Filter by tag
- "Compare power consumption across all machines" → Group by machine tag

**Fields store the actual measurements**:
- Temperature, pressure, power, voltage readings
- Can have multiple fields per data point for efficiency

**Timestamps enable time-based analysis**:
- "Show me the last hour of data"
- "Calculate daily averages"
- "Find when the temperature spike occurred"

---

## 📚 **Essential InfluxDB Knowledge**

### **InfluxDB Python Client Library**

**Key Classes and Methods You'll Use**:

```python
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS

# Client connection
client = InfluxDBClient(url="http://localhost:8086", token="your-token", org="your-org")

# Writing data
write_api = client.write_api(write_options=SYNCHRONOUS)
point = Point("measurement_name").tag("device", "sensor1").field("temperature", 25.3)
write_api.write(bucket="your-bucket", record=point)

# Reading data  
query_api = client.query_api()
result = query_api.query('from(bucket:"your-bucket") |> range(start: -1h)')
```

**Important Methods**:
- `Point(measurement_name)` - Creates a new data point
- `.tag(key, value)` - Adds metadata tags (chainable)
- `.field(key, value)` - Adds measured values (chainable)
- `.time(timestamp)` - Sets custom timestamp (optional)
- `write_api.write()` - Stores data points
- `query_api.query()` - Retrieves data using Flux queries

### **Flux Query Language Fundamentals**

**Flux** is InfluxDB's query language. Think of it as a pipeline of data transformations:

```flux
from(bucket: "sensors")           // Start with data from bucket
  |> range(start: -1h)           // Filter to last hour
  |> filter(fn: (r) => r["_measurement"] == "temperature")  // Only temperature data
  |> filter(fn: (r) => r["device"] == "sensor1")           // Only sensor1
  |> mean()                      // Calculate average
```

**Essential Flux Functions**:
- `from(bucket: "name")` - Select data source
- `range(start: time, stop: time)` - Time window filtering
- `filter(fn: (r) => condition)` - Row-based filtering
- `aggregateWindow(every: duration, fn: function)` - Time-based grouping
- `mean()`, `max()`, `min()`, `count()` - Aggregation functions
- `yield(name: "result")` - Return results

---

## 🔧 **Phase 1: Time Series Fundamentals**

### **Learning Objectives**
- Understand the InfluxDB data model deeply
- Practice creating well-structured data points
- Learn data modeling decisions for manufacturing scenarios
- Master basic data writing operations

### **Setup Requirements**

Create `requirements.txt`:
```
influxdb-client>=1.36.0
PyYAML>=6.0
matplotlib>=3.5.0
numpy>=1.21.0
```

### **Configuration Design Challenge**

Create `config.yaml` - but first, **think critically** about the data model:

**Questions to Consider**:
1. What tags would help you filter and group manufacturing data effectively?
2. What fields represent the actual sensor measurements?
3. How should you organize different types of sensors (temperature, pressure, power)?
4. What measurement names would make sense for different data types?

```yaml
development:
  influxdb:
    url: "http://localhost:8086"
    token: "your-development-token"
    org: "your-org"
    bucket: "manufacturing_sensors"
    
  # TODO: Design your sensor configuration
  # Think about: What tags and fields make sense for each sensor type?
  # Consider: How would you group similar sensors? How would you filter data?
  
  sensors:
    # Design challenge: Create a logical sensor configuration
    # that demonstrates good time series data modeling
    
  simulation:
    duration_minutes: 5
    collection_interval_seconds: 2
    data_quality_issues: true  # Simulate realistic data problems
```

### **Phase 1 Implementation: `phase1_time_series_basics.py`**

```python
import yaml
import time
import random
from datetime import datetime, timezone
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS

# Load configuration
with open("config.yaml") as f:
    config = yaml.safe_load(f)

class TimeSeriesBasics:
    def __init__(self):
        # TODO: Initialize InfluxDB client using config
        # Consider: How do you handle connection errors?
        pass
    
    def create_manufacturing_data_point(self, measurement, tags, fields, timestamp=None):
        """
        Create a properly structured data point for manufacturing data.
        
        Learning Challenge: Understand the difference between tags and fields
        - Tags: Use for filtering and grouping (machine_id, location, sensor_type)
        - Fields: Use for actual measurements (temperature, pressure, power)
        
        Args:
            measurement (str): The measurement name (like "machine_sensors")
            tags (dict): Metadata for filtering {"machine": "EOS_01", "type": "temperature"}
            fields (dict): Actual values {"temperature": 245.6, "humidity": 45.2}
            timestamp (datetime): When measurement was taken (optional)
        """
        # TODO: Implement data point creation
        # Questions to think about:
        # 1. How do you create a Point object?
        # 2. How do you add multiple tags efficiently?
        # 3. How do you add multiple fields?
        # 4. How do you handle timestamp formatting?
        pass
    
    def demonstrate_data_modeling_decisions(self):
        """
        Show different approaches to organizing the same data.
        
        Learning Goal: Understand how data modeling affects query performance
        """
        print("=== DATA MODELING DEMONSTRATION ===")
        
        # Scenario: Temperature sensor on Machine A
        temperature_reading = 245.6
        machine_id = "EOS_SI3654"
        sensor_location = "print_chamber"
        
        # TODO: Create the same data using different modeling approaches
        # Approach 1: Separate measurement per sensor type
        # Approach 2: Single measurement with sensor type as tag
        # Approach 3: Machine-centric measurement
        
        # Think about: Which approach would be better for different query patterns?
        pass
    
    def practice_data_validation(self):
        """
        Learn to validate data before storing it.
        
        Manufacturing Reality: Sensors sometimes return invalid data
        """
        # TODO: Implement data validation
        # Consider: What makes sensor data "invalid"?
        # - Out of realistic range values
        # - Sudden impossible changes
        # - Missing or null values
        # - Timestamp issues
        pass

def main():
    """Phase 1: Master the fundamentals before building complex systems"""
    basics = TimeSeriesBasics()
    
    print("Phase 1: Time Series Fundamentals")
    print("Goal: Understand data modeling and basic operations")
    
    # TODO: Implement these learning exercises
    basics.demonstrate_data_modeling_decisions()
    basics.practice_data_validation()
    
    print("Phase 1 complete! Ready for Phase 2 (Data Collection)")

if __name__ == "__main__":
    main()
```

---

## 🎯 **Critical Thinking Challenges for Phase 1**

### **Challenge 1: Data Model Design**
You need to store data from these manufacturing sensors:
- 3 EOS 3D printers, each with temperature, pressure, and power sensors
- 2 CNC machines with spindle temperature and vibration sensors  
- 1 environmental monitor with room temperature and humidity

**Your Task**: Design a data model that allows efficient queries for:
- "Show me all temperature data from EOS printers"
- "Compare power consumption across all machines"
- "Find when any sensor exceeded normal ranges"

**Think About**: What tags and measurement names would you use? Why?

### **Challenge 2: Data Quality Strategy**
Real sensors sometimes provide bad data. How would you detect and handle:
- Temperature readings of -999°C (sensor error)
- Power readings that jump from 15kW to 150kW instantly
- Missing data for 30-second periods
- Timestamps that are in the future

**Your Task**: Develop validation rules and error handling strategies.

---

## 🔄 **Phase 2: Data Collection & Storage**

### **Learning Objectives**
- Apply threading concepts from Task 2 to continuous data collection
- Implement robust data storage patterns from the real project
- Handle network failures and data quality issues
- Build a complete sensor monitoring system

### **Real-World Connection**
This phase mirrors the actual project patterns:
- `For power meter/main.py` uses threading to monitor multiple power meters
- `For EOS M290 OPCUA/main.py` continuously collects machine sensor data
- Both handle network failures and implement retry logic

### **Phase 2 Implementation: `phase2_data_collection.py`**

```python
import threading
import time
import random
import yaml
from datetime import datetime, timezone
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS

class SensorSimulator:
    """
    Simulate realistic manufacturing sensor behavior.

    Learning Goal: Understand how real sensors behave in manufacturing
    """

    def __init__(self, sensor_config):
        self.config = sensor_config
        self.last_value = None
        self.failure_count = 0

    def read_sensor_value(self):
        """
        Simulate reading from a real sensor with realistic behavior.

        Manufacturing Reality: Sensors don't always work perfectly
        - Sometimes they fail temporarily
        - Values change gradually, not randomly
        - Occasional outliers occur
        """
        # TODO: Implement realistic sensor simulation
        # Consider:
        # 1. How do real sensor values change over time?
        # 2. What types of failures occur?
        # 3. How do you simulate gradual changes vs sudden spikes?
        # 4. When should you return None (sensor failure)?
        pass

class ManufacturingDataCollector:
    """
    Collect data from multiple sensors using threading patterns from Task 2.

    Learning Goal: Apply threading to real-world data collection
    """

    def __init__(self, config):
        self.config = config
        self.sensors = {}
        self.threads = {}
        self.running = False
        self.data_stats = {}

        # TODO: Initialize InfluxDB client
        # TODO: Create sensor simulators from config

    def monitor_single_sensor(self, sensor_name, sensor_config):
        """
        Monitor one sensor continuously (runs in its own thread).

        Threading Pattern: Similar to Task 2's worker threads
        Data Storage Pattern: Similar to real project's data collection
        """
        print(f"Starting monitoring for {sensor_name}")

        # TODO: Implement continuous sensor monitoring
        # Pattern from real project:
        # 1. Read sensor value
        # 2. Validate data quality
        # 3. Create InfluxDB point
        # 4. Store in database
        # 5. Handle errors gracefully
        # 6. Sleep until next reading

        while self.running:
            try:
                # TODO: Your implementation here
                pass
            except Exception as e:
                # TODO: How should you handle sensor failures?
                # Consider: Logging, retry logic, graceful degradation
                pass

    def start_monitoring(self):
        """
        Start monitoring all sensors using threading.

        Threading Pattern: Apply concepts from Task 2
        """
        print("=== STARTING MANUFACTURING DATA COLLECTION ===")
        self.running = True

        # TODO: Create and start threads for each sensor
        # Pattern from Task 2:
        # 1. Create thread for each sensor
        # 2. Store thread references
        # 3. Start all threads
        # 4. Handle thread management

    def stop_monitoring(self):
        """
        Gracefully stop all sensor monitoring.

        Threading Pattern: Clean shutdown from Task 2
        """
        print("Stopping data collection...")
        self.running = False

        # TODO: Implement graceful shutdown
        # 1. Set running flag to False
        # 2. Wait for all threads to finish
        # 3. Close database connections
        # 4. Generate final statistics

    def generate_collection_report(self):
        """
        Generate statistics about data collection performance.

        Learning Goal: Understand system monitoring and performance
        """
        # TODO: Implement reporting
        # Consider: What metrics matter for data collection systems?
        # - Data points collected per sensor
        # - Error rates and failure patterns
        # - Collection timing and performance
        # - Data quality statistics
        pass

def main():
    """Phase 2: Build a complete data collection system"""

    # Load configuration
    with open("config.yaml") as f:
        config = yaml.safe_load(f)

    collector = ManufacturingDataCollector(config)

    try:
        # TODO: Start data collection
        # TODO: Run for configured duration
        # TODO: Handle keyboard interrupt gracefully
        pass
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    finally:
        # TODO: Ensure clean shutdown
        pass

if __name__ == "__main__":
    main()
```

### **Phase 2 Critical Thinking Challenges**

#### **Challenge 1: Error Handling Strategy**
Real manufacturing environments have network issues, sensor failures, and database outages.

**Your Task**: Design error handling for these scenarios:
- Sensor returns invalid data (how do you detect and handle?)
- InfluxDB is temporarily unavailable (how do you buffer data?)
- Network timeout during data write (retry strategy?)
- One sensor fails completely (how do you continue monitoring others?)

**Think About**: What's the difference between recoverable and non-recoverable errors?

#### **Challenge 2: Performance Optimization**
You're monitoring 50 sensors, each generating data every 2 seconds.

**Your Task**: Optimize for:
- Minimize database write operations (batch writes?)
- Reduce memory usage (data buffering strategy?)
- Handle varying sensor response times (threading implications?)
- Maintain data quality while maximizing throughput

**Think About**: How do the real project files handle these challenges?

---

## 📈 **Phase 3: Analysis & Visualization**

### **Learning Objectives**
- Master Flux query language for data analysis
- Implement data retrieval patterns from the real project
- Create meaningful visualizations of manufacturing data
- Develop analytical thinking for manufacturing insights

### **Flux Query Language Deep Dive**

**Essential Patterns You'll Use**:

```flux
// Pattern 1: Basic time-range filtering
from(bucket: "manufacturing")
  |> range(start: -1h, stop: now())
  |> filter(fn: (r) => r["_measurement"] == "machine_sensors")

// Pattern 2: Multi-condition filtering
from(bucket: "manufacturing")
  |> range(start: -24h)
  |> filter(fn: (r) => r["machine"] == "EOS_SI3654")
  |> filter(fn: (r) => r["_field"] == "temperature")

// Pattern 3: Time-based aggregation
from(bucket: "manufacturing")
  |> range(start: -1d)
  |> aggregateWindow(every: 1h, fn: mean)
  |> yield(name: "hourly_averages")

// Pattern 4: Cross-measurement analysis
temp_data = from(bucket: "manufacturing")
  |> range(start: -1h)
  |> filter(fn: (r) => r["_field"] == "temperature")

power_data = from(bucket: "manufacturing")
  |> range(start: -1h)
  |> filter(fn: (r) => r["_field"] == "power")

// Join and analyze correlation
```

### **Phase 3 Implementation: `phase3_analysis_visualization.py`**

```python
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime, timedelta
from influxdb_client import InfluxDBClient

class ManufacturingDataAnalyzer:
    """
    Analyze manufacturing data using patterns from the real project.

    Learning Goal: Transform raw sensor data into actionable insights
    """

    def __init__(self, config):
        # TODO: Initialize InfluxDB client for reading
        pass

    def analyze_machine_performance(self, machine_id, time_range_hours=24):
        """
        Analyze overall machine performance over time.

        Real Project Pattern: Similar to analysis in InfluxDB/pull_data.py
        """
        # TODO: Write Flux query to get machine data
        # Consider: What metrics indicate good machine performance?
        # - Temperature stability
        # - Power consumption patterns
        # - Sensor data quality
        # - Operating time vs downtime

        query = f"""
        // TODO: Write your Flux query here
        // Goal: Get comprehensive machine data for analysis
        """

        # TODO: Execute query and process results
        # TODO: Calculate performance metrics
        # TODO: Return analysis results
        pass

    def detect_anomalies(self, sensor_type, threshold_std_dev=2):
        """
        Detect unusual sensor readings that might indicate problems.

        Manufacturing Value: Early detection of equipment issues
        """
        # TODO: Implement anomaly detection
        # Approach: Statistical analysis of sensor data
        # 1. Calculate rolling averages and standard deviations
        # 2. Identify readings outside normal ranges
        # 3. Flag potential equipment issues
        # 4. Generate alerts for investigation
        pass

    def create_production_dashboard(self):
        """
        Create visualizations similar to what you'd see in Grafana.

        Learning Goal: Present data in meaningful ways for operators
        """
        # TODO: Create multiple subplot dashboard
        # Panel 1: Temperature trends across all machines
        # Panel 2: Power consumption comparison
        # Panel 3: Data quality metrics
        # Panel 4: Anomaly detection results

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Manufacturing Monitoring Dashboard', fontsize=16)

        # TODO: Implement each dashboard panel
        # Consider: What information is most valuable for operators?

        plt.tight_layout()
        plt.show()

def main():
    """Phase 3: Turn data into insights"""

    with open("config.yaml") as f:
        config = yaml.safe_load(f)

    analyzer = ManufacturingDataAnalyzer(config)

    print("=== MANUFACTURING DATA ANALYSIS ===")

    # TODO: Implement analysis workflow
    # 1. Analyze machine performance
    # 2. Detect anomalies
    # 3. Create visualizations
    # 4. Generate insights report

if __name__ == "__main__":
    main()
```

---

## 🎯 **Final Integration Challenge**

### **The Complete Manufacturing Monitoring System**

**Your Mission**: Combine all three phases into a complete system that:

1. **Collects** data from multiple simulated manufacturing sensors
2. **Stores** data efficiently in InfluxDB with proper data modeling
3. **Analyzes** data to detect trends and anomalies
4. **Visualizes** results in a meaningful dashboard

**Success Criteria**:
- System runs continuously for 30+ minutes without errors
- Handles simulated sensor failures gracefully
- Generates meaningful insights about "machine performance"
- Creates professional-quality visualizations
- Demonstrates understanding of time series data concepts

**Bonus Challenges**:
- Implement data retention policies (delete old data)
- Add real-time alerting for anomalies
- Create automated daily/weekly reports
- Optimize query performance for large datasets

---

## 📚 **Additional Learning Resources**

### **InfluxDB Documentation**
- [InfluxDB Python Client](https://influxdb-client.readthedocs.io/)
- [Flux Query Language Guide](https://docs.influxdata.com/influxdb/v2.0/query-data/flux/)
- [Time Series Best Practices](https://docs.influxdata.com/influxdb/v2.0/write-data/best-practices/)

### **Manufacturing Data Analysis**
- [Industrial IoT Data Patterns](https://www.influxdata.com/solutions/industrial-iot/)
- [Time Series Analysis for Manufacturing](https://towardsdatascience.com/time-series-analysis-in-manufacturing)

### **Python Libraries for Time Series**
- [Pandas Time Series](https://pandas.pydata.org/docs/user_guide/timeseries.html)
- [Matplotlib for Industrial Dashboards](https://matplotlib.org/stable/gallery/index.html)

---

## 🚀 **Ready to Build Your Manufacturing Data System?**

This task will challenge you to think like a manufacturing engineer while building real technical skills. Focus on understanding the **why** behind each decision, not just the **how** to implement it.

**Remember**: The goal is learning, not just completing the code. Take time to understand each concept deeply before moving to the next phase.

Good luck! 🎯
