2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	=== PHASE 2: DATA CONVERSION PRACTICE ===
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	=== MODBUS DATA TYPES EXPLANATION ===
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Common Modbus data types:
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	- 16-bit Integer: 1 register (range: -32,768 to 32,767)
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	- 32-bit Integer: 2 registers (range: -2,147,483,648 to 2,147,483,647)
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	- IEEE 754 Float: 2 registers (what we're using for power meter data)
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	- Boolean: 1 bit (for status flags)
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Why IEEE 754 for power meters?
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	- Voltage: 230.5V (needs decimal precision)
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	- Current: 12.34A (needs decimal precision)
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	- Power: 8.456kW (needs decimal precision)
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	- Integers can't represent decimal values accurately
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	=== IEEE 754 FLOAT FORMAT DEMONSTRATION ===
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Value: 1.0
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Value: -1.0
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Value: 0.0
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Value: 3.14159
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Value: 230.5
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	=== TESTING DATA CONVERSION ACCURACY ===
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Testing value: 230.5
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting float 230.5 to registers
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting registers [17254, 32768] to float
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Testing value: 12.3
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting float 12.3 to registers
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting registers [16708, 52429] to float
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Testing value: 8.45
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting float 8.45 to registers
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting registers [16647, 13107] to float
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Testing value: 0.0
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting float 0.0 to registers
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting registers [0, 0] to float
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Testing value: -15.7
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting float -15.7 to registers
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting registers [49531, 13107] to float
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Testing value: 999.99
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting float 999.99 to registers
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting registers [17529, 65372] to float
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Testing value: 1.23456789
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting float 1.23456789 to registers
2025-08-04 14:53:37	phase2_data_conversion	26228	DEBUG	Converting registers [16286, 1618] to float
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	=== CONVERSION TEST SUMMARY ===
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Successful conversions: 7/7 (100.0%)
2025-08-04 14:53:37	phase2_data_conversion	26228	INFO	Next step: Run 'python phase2_modbus_client.py'
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	=== PHASE 2: DATA CONVERSION PRACTICE ===
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	=== MODBUS DATA TYPES EXPLANATION ===
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Common Modbus data types:
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	- 16-bit Integer: 1 register (range: -32,768 to 32,767)
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	- 32-bit Integer: 2 registers (range: -2,147,483,648 to 2,147,483,647)
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	- IEEE 754 Float: 2 registers (what we're using for power meter data)
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	- Boolean: 1 bit (for status flags)
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Why IEEE 754 for power meters?
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	- Voltage: 230.5V (needs decimal precision)
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	- Current: 12.34A (needs decimal precision)
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	- Power: 8.456kW (needs decimal precision)
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	- Integers can't represent decimal values accurately
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	=== IEEE 754 FLOAT FORMAT DEMONSTRATION ===
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: 1.0 -> Hex: 3f800000
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: 1.0
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: -1.0 -> Hex: bf800000
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: -1.0
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: 0.0 -> Hex: 00000000
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: 0.0
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: 3.14159 -> Hex: 40490fd0
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: 3.14159
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: 230.5 -> Hex: 43668000
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Value: 230.5
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	=== TESTING DATA CONVERSION ACCURACY ===
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Testing value: 230.5
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting float 230.5 to registers
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting registers [17254, 32768] to float
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Testing value: 12.3
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting float 12.3 to registers
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting registers [16708, 52429] to float
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Testing value: 8.45
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting float 8.45 to registers
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting registers [16647, 13107] to float
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Testing value: 0.0
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting float 0.0 to registers
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting registers [0, 0] to float
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Testing value: -15.7
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting float -15.7 to registers
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting registers [49531, 13107] to float
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Testing value: 999.99
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting float 999.99 to registers
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting registers [17529, 65372] to float
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Testing value: 1.23456789
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting float 1.23456789 to registers
2025-08-04 14:56:58	phase2_data_conversion	27596	DEBUG	Converting registers [16286, 1618] to float
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	=== CONVERSION TEST SUMMARY ===
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Successful conversions: 7/7 (100.0%)
2025-08-04 14:56:58	phase2_data_conversion	27596	INFO	Next step: Run 'python phase2_modbus_client.py'
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	=== PHASE 2: DATA CONVERSION PRACTICE ===
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	=== MODBUS DATA TYPES EXPLANATION ===
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Common Modbus data types:
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	- 16-bit Integer: 1 register (range: -32,768 to 32,767)
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	- 32-bit Integer: 2 registers (range: -2,147,483,648 to 2,147,483,647)
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	- IEEE 754 Float: 2 registers (what we're using for power meter data)
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	- Boolean: 1 bit (for status flags)
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Why IEEE 754 for power meters?
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	- Voltage: 230.5V (needs decimal precision)
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	- Current: 12.34A (needs decimal precision)
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	- Power: 8.456kW (needs decimal precision)
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	- Integers can't represent decimal values accurately
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	=== IEEE 754 FLOAT FORMAT DEMONSTRATION ===
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: 1.0 -> Hex: 3f800000
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: 1.0
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: -1.0 -> Hex: bf800000
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: -1.0
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: 0.0 -> Hex: 00000000
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: 0.0
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: 3.14159 -> Hex: 40490fd0
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: 3.14159
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: 230.5 -> Hex: 43668000
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Value: 230.5
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	=== TESTING DATA CONVERSION ACCURACY ===
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Testing value: 230.5
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting float 230.5 to registers
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting registers [17254, 32768] to float
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	 Test passed: 230.5 -> [17254, 32768] -> 230.5
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Testing value: 12.3
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting float 12.3 to registers
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting registers [16708, 52429] to float
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	 Test passed: 12.3 -> [16708, 52429] -> 12.300000190734863
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Testing value: 8.45
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting float 8.45 to registers
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting registers [16647, 13107] to float
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	 Test passed: 8.45 -> [16647, 13107] -> 8.449999809265137
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Testing value: 0.0
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting float 0.0 to registers
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting registers [0, 0] to float
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	 Test passed: 0.0 -> [0, 0] -> 0.0
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Testing value: -15.7
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting float -15.7 to registers
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting registers [49531, 13107] to float
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	 Test passed: -15.7 -> [49531, 13107] -> -15.699999809265137
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Testing value: 999.99
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting float 999.99 to registers
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting registers [17529, 65372] to float
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	 Test passed: 999.99 -> [17529, 65372] -> 999.989990234375
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Testing value: 1.23456789
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting float 1.23456789 to registers
2025-08-04 14:57:21	phase2_data_conversion	11948	DEBUG	Converting registers [16286, 1618] to float
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	 Test passed: 1.23456789 -> [16286, 1618] -> 1.2345678806304932
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	=== CONVERSION TEST SUMMARY ===
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Successful conversions: 7/7 (100.0%)
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	 All conversion tests passed!
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	 Phase 2 complete! Ready for Phase 3 (Modbus Communication)
2025-08-04 14:57:21	phase2_data_conversion	11948	INFO	Next step: Run 'python phase2_modbus_client.py'
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	=== PHASE 2: DATA CONVERSION PRACTICE ===
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	=== MODBUS DATA TYPES EXPLANATION ===
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Common Modbus data types:
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	- 16-bit Integer: 1 register (range: -32,768 to 32,767)
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	- 32-bit Integer: 2 registers (range: -2,147,483,648 to 2,147,483,647)
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	- IEEE 754 Float: 2 registers (what we're using for power meter data)
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	- Boolean: 1 bit (for status flags)
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Why IEEE 754 for power meters?
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	- Voltage: 230.5V (needs decimal precision)
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	- Current: 12.34A (needs decimal precision)
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	- Power: 8.456kW (needs decimal precision)
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	- Integers can't represent decimal values accurately
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	=== IEEE 754 FLOAT FORMAT DEMONSTRATION ===
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Value: 1.0 -> Hex: 3f800000
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	msb: 0x3f, lsb: 0x80
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Value: -1.0 -> Hex: bf800000
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	msb: 0xbf, lsb: 0x80
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Value: 0.0 -> Hex: 00000000
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	msb: 0x0, lsb: 0x0
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Value: 3.14159 -> Hex: 40490fd0
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	msb: 0x40, lsb: 0x49
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Value: 230.5 -> Hex: 43668000
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	msb: 0x43, lsb: 0x66
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	=== TESTING DATA CONVERSION ACCURACY ===
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Testing value: 230.5
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting float 230.5 to registers
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting registers [17254, 32768] to float
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	 Test passed: 230.5 -> [17254, 32768] -> 230.5
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Testing value: 12.3
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting float 12.3 to registers
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting registers [16708, 52429] to float
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	 Test passed: 12.3 -> [16708, 52429] -> 12.300000190734863
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Testing value: 8.45
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting float 8.45 to registers
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting registers [16647, 13107] to float
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	 Test passed: 8.45 -> [16647, 13107] -> 8.449999809265137
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Testing value: 0.0
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting float 0.0 to registers
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting registers [0, 0] to float
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	 Test passed: 0.0 -> [0, 0] -> 0.0
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Testing value: -15.7
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting float -15.7 to registers
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting registers [49531, 13107] to float
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	 Test passed: -15.7 -> [49531, 13107] -> -15.699999809265137
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Testing value: 999.99
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting float 999.99 to registers
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting registers [17529, 65372] to float
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	 Test passed: 999.99 -> [17529, 65372] -> 999.989990234375
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Testing value: 1.23456789
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting float 1.23456789 to registers
2025-08-04 14:58:35	phase2_data_conversion	13692	DEBUG	Converting registers [16286, 1618] to float
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	 Test passed: 1.23456789 -> [16286, 1618] -> 1.2345678806304932
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	=== CONVERSION TEST SUMMARY ===
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Successful conversions: 7/7 (100.0%)
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	 All conversion tests passed!
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	 Phase 2 complete! Ready for Phase 3 (Modbus Communication)
2025-08-04 14:58:35	phase2_data_conversion	13692	INFO	Next step: Run 'python phase2_modbus_client.py'
