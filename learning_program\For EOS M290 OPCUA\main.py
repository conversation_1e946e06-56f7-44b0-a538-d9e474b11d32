import os
import pytz
import time
import socket
import urllib3
import asyncio
import logging
import datetime
import multiprocessing
import urllib.parse
from contextlib import closing

from asyncua import Client, Node, ua

from influxdb_client import InfluxDBClient, Point, WriteOptions

import pickle

EOS_MACHINE_SI = os.getenv('EOS_MACHINE_SI')
if EOS_MACHINE_SI is None:
    raise ValueError('Env variable EOS_MACHINE_SI is not set.')
if not EOS_MACHINE_SI.startswith('SI'):
    raise ValueError('Env variable EOS_MACHINE_SI is in an improper format.')
    

# Loads different settings based on the machine SI chosen
if EOS_MACHINE_SI=='SI3654_old_before_Mar_2024':
    EOS_OPCUA_IP = "*************"
    EOS_OPCUA_PORT = 4843
    EOS_OPCUA_username = None
    EOS_OPCUA_token = None
    update_criteria = {'EOS.Machine.CurrentJob.LayerCountCurrent':{'max_diff':None,'min_time':1,'max_time':None},
                        'EOS.Machine.CurrentJob.LayerCountTarget':{'max_diff':None,'min_time':1,'max_time':None},
                        'EOS.Machine.CurrentJob.LayerThickness':{'max_diff':None,'min_time':1,'max_time':None},
                        'EOS.Machine.CurrentJob.MaterialName':{'max_diff':None,'min_time':1,'max_time':None},
                        'EOS.Machine.CurrentJob.OrderId':{'max_diff':None,'min_time':1,'max_time':None},
                        'EOS.Machine.CurrentJob.Starter':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.CurrentJob.Times.BuildStart':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.CurrentJob.Times.CoolDownStart':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.CurrentJob.Times.JobEnd':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.CurrentJob.Times.JobStart':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Environment.HumidityRelative':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Environment.Temperature':{'max_diff':2,'min_time':30,'max_time':None},
                        'EOS.Machine.Info.Brand':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Info.HostName':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Info.Id':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Info.Model':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Info.ProcessState':{'max_diff':None,'min_time':1,'max_time':None},
                        'EOS.Machine.Info.ProcessStateEOS':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Info.ProcessStateSemiE10':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Info.Time':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Info.Type':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.BuildingPlatform.Position':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.BuildingPlatform.Speed':{'max_diff':0.001,'min_time':0.1,'max_time':None},
                        'EOS.Machine.Sensors.BuildingPlatform.Temperature':{'max_diff':2,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.CollectorPlatform.Position':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.Dispenser.FillLevel':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.Dispenser.LastDosingCount':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.Dispenser.Position':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.Dispenser.Speed':{'max_diff':0.001,'min_time':0.1,'max_time':None},
                        'EOS.Machine.Sensors.ExposureUnits.ExposureUnit1.Temperature':{'max_diff':2,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.ExposureUnits.ExposureUnit2.Temperature':{'max_diff':2,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.ExposureUnits.ExposureUnit3.Temperature':{'max_diff':2,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.ExposureUnits.ExposureUnit4.Temperature':{'max_diff':2,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.ProcessChamber.InertGasType':{'max_diff':None,'min_time':1,'max_time':None},
                        'EOS.Machine.Sensors.ProcessChamber.O2ConcentrationBottom':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.ProcessChamber.O2ConcentrationTop':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.ProcessChamber.RelativePressure':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.ProcessChamber.Temperature':{'max_diff':2,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.RecirculatingFilterSystem.FilterPressure':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.RecirculatingFilterSystem.O2Concentration':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.Sensors.Recoater.Position':{'max_diff':0.01,'min_time':0,'max_time':60},
                        'EOS.Machine.Sensors.Recoater.Speed':{'max_diff':0.001,'min_time':0.1,'max_time':None},
                        'EOS.Machine.Sensors.Turbine.Pressure':{'max_diff':None,'min_time':30,'max_time':None},
                        'EOS.Machine.CurrentJob.Id':{'max_diff':None,'min_time':1,'max_time':None},
                        'UserMessages':{'max_diff':None,'min_time':30,'max_time':None},                       
                        }
elif EOS_MACHINE_SI=='SI3654':
    EOS_OPCUA_IP = "*************"
    EOS_OPCUA_PORT = 4843
    EOS_OPCUA_username = 'client1'
    EOS_OPCUA_token = 'xxx'
    update_criteria = {}
elif EOS_MACHINE_SI=='SI2373':
    EOS_OPCUA_IP = "*************"
    EOS_OPCUA_PORT = 4843
    EOS_OPCUA_username = 'client2'
    EOS_OPCUA_token = 'xxx'
    update_criteria = {}   
elif EOS_MACHINE_SI=='SI2461':
    EOS_OPCUA_IP = "*************"
    EOS_OPCUA_PORT = 4843
    EOS_OPCUA_username = 'client2'
    EOS_OPCUA_token = 'xxx'
    update_criteria = {}     
elif EOS_MACHINE_SI=='SI4490':
    EOS_OPCUA_IP = "**************"
    EOS_OPCUA_PORT = 4843
    EOS_OPCUA_username = 'client1'
    EOS_OPCUA_token = 'xxx'
    update_criteria = {}
else:
    raise ValueError(f'The machine SI {EOS_MACHINE_SI} is not in a recognised format.')



INFLUXDB_BUCKET = "eos_opcua"
INFLUXDB_URL = "http://influxdb.ami.modelfactory.sg:8086"
INFLUXDB_TOKEN = "xxx"
INFLUXDB_ORG = "266e2e2e067fbe5b" # Organisation "AMI"
retries = urllib3.Retry(connect=5, read=2, redirect=5)


# Configures the logger
#typical_log_level = logging.ERROR
typical_log_level = logging.DEBUG
logging.basicConfig(format='%(asctime)s\t%(name)s\t%(levelname)s\t%(message)s',
                    datefmt="%Y-%m-%d %H:%M:%S",
                    level=logging.ERROR)
logger = logging.getLogger(__name__)
logger.propagate = False
formatter = logging.Formatter('%(asctime)s\t%(name)s\t%(levelname)s\t%(message)s',"%Y-%m-%d %H:%M:%S")
fh = logging.FileHandler(os.path.join(os.path.basename(__file__)+'.log'))
fh.setLevel(typical_log_level)
fh.setFormatter(formatter)
logger.addHandler(fh)
ch = logging.StreamHandler()
ch.setFormatter(formatter)
logger.addHandler(ch)
logger.setLevel(typical_log_level)
logger.debug('Started script')



def datetime_str():
    return datetime.datetime.now(tz=pytz.timezone('Asia/Singapore')).strftime('%H:%M:%S.%f')

def check_socket(host, port, timeout_ms=500,type='ipv4'):
    assert type in ['ipv4','ipv6']
    with closing(socket.socket(socket.AF_INET if type=='ipv4' else socket.AF_INET6, socket.SOCK_STREAM)) as sock:
        sock.settimeout(timeout_ms/1000)
        if sock.connect_ex((host, port)) == 0:
            #print("Port {}:{} is open.".format(host,port))
            return True
        else:
            #print("Port {}:{} is closed.".format(host,port))
            return False


def upload_single_measurement(ident,value,UTC_datetime,show_debug=True):
    # Examples for uploading single measurement with multiple fields and tags
    #data = Point("particle count").tag("eqpt","Solair3100").tag('loc',EOS_data['location']).tag('sample time',EOS_data['sample time (s)']).time(EOS_data['datetime_iso']).field("size1",1).field("size2",2)
    data = Point("printer stats").tag("eqpt",EOS_MACHINE_SI).time(UTC_datetime).field(ident,value)
    with InfluxDBClient(url=INFLUXDB_URL, token=INFLUXDB_TOKEN, org=INFLUXDB_ORG, retries=retries) as _client:
        with _client.write_api() as write_api:
            write_api.write(bucket=INFLUXDB_BUCKET, org=INFLUXDB_ORG, record=data) # This is async and will not raise any error in the foreground
    if show_debug:
        logger.debug(f'[{datetime.datetime.now().strftime("%H:%M:%S")}] Uploaded data to InfluxDB server: {ident}={value}')

class SubscriptionHandler:
    initiation_time = 0
    last_data_time = 0
    last_debugprint_time = 0
    stats_count = 0
    
    def __init__(self,shared_dict,current_server_time_offset):
        self.shared_dict = shared_dict
        self.initiation_time = time.time()
        self.current_server_time_offset = current_server_time_offset
        
        
    def datachange_notification(self, node: Node, val, data):
        # Since the serial number is critical and has to be pre-declared, we check it when we get a chance
        if str(node.nodeid.Identifier) == 'EOS.Machine.Info.SerialNumber':
            assert val == EOS_MACHINE_SI, f'If reusing this script for another EOS machine, please remember to set the EOS_MACHINE_SI. Currently EOS_MACHINE_SI={EOS_MACHINE_SI} but the OPCUA says that it is {val}.'
            logger.info(f'Machine serial number readback is correct.')

        # Updates internal statistics
        self.last_data_time = time.time()
        self.stats_count += 1
        is_starting_up = time.time() - self.initiation_time < 300
        
        # Dumps the data for later debugging in a cyclic buffer pattern
        max_buffer_size = 1000
        proposed_filename = os.path.join('Debugging dump',f'Dump {self.stats_count % max_buffer_size:04}.pck')
        try:
            os.makedirs(os.path.dirname(proposed_filename))
        except FileExistsError:
            pass
        with open(proposed_filename,'wb') as f:
            pickle.dump({'monitored_item':data.monitored_item,
                         'stats_count':self.stats_count,
                         'last_data_time':self.last_data_time,
                         'Identifier':node.nodeid.Identifier,
                         },f)



        # Flags out empty data
        has_information = True
        if val is None:
            has_information = False
        elif (data.monitored_item.Value.Value.VariantType==ua.VariantType.Null) and (data.monitored_item.Value.StatusCode.value==10813440):
            has_information = False # We have a GoodNoData status, but a blank value.
        
        # Determines whether to update the data
        do_update = True
        if has_information:
            # Then we have valid data
            source_timestamp = data.monitored_item.Value.SourceTimestamp.replace(tzinfo=pytz.utc)
            data_time = (source_timestamp + datetime.timedelta(seconds=self.current_server_time_offset))
            
            # Looks at the value to determine whether the value needs changing
            if node.nodeid.Identifier in self.shared_dict:
                if self.shared_dict.get(node.nodeid.Identifier).get('data_value') == val:
                    do_update = False # Then the value has not changed
                    #logger.debug(f'Not updating {node.nodeid.Identifier} because there is zero change in value.')
                elif update_criteria.get(node.nodeid.Identifier,{}).get('max_diff',None) is not None:
                    if abs(val - self.shared_dict.get(node.nodeid.Identifier).get('data_value')) <= update_criteria.get(node.nodeid.Identifier).get('max_diff'):
                        do_update = False
                        #logger.debug(f'Not updating {node.nodeid.Identifier} because there is only a small change in value.')

            
            # Example: update_criteria = {'EOS.Machine.CurrentJob.LayerCountCurrent':{'max_diff':None,'min_time':30,'max_time':None}
            
            # Updates data if exceeded max_time of update_criteria
            if (update_criteria.get(node.nodeid.Identifier,{}).get('max_time',None) is not None) and (node.nodeid.Identifier in self.shared_dict):
                if (data_time - self.shared_dict.get(node.nodeid.Identifier,{}).get('data_time',None)).total_seconds() > update_criteria.get(node.nodeid.Identifier,{}).get('max_time'):
                    do_update = True
                    logger.debug(f'Forcing updating {node.nodeid.Identifier} because max time is exceeded')
            # Denies update if min_time is not satisfied
            if (update_criteria.get(node.nodeid.Identifier,{}).get('min_time',None) is not None) and (node.nodeid.Identifier in self.shared_dict):
                if (data_time - self.shared_dict.get(node.nodeid.Identifier,{}).get('data_time',None)).total_seconds() < update_criteria.get(node.nodeid.Identifier,{}).get('min_time'):
                    do_update = False
                    #logger.debug(f'Not updating {node.nodeid.Identifier} because min time is not satisfied')
            
        else:
            do_update = False
        
        # Updates the date if required
        if do_update:
            self.shared_dict[node.nodeid.Identifier] = {'data_value':val,'data_time':data_time}
            if isinstance(val, datetime.datetime):
                upload_single_measurement(ident=node.nodeid.Identifier,value=val.isoformat(),UTC_datetime=data_time,show_debug=is_starting_up)
            else:
                upload_single_measurement(ident=node.nodeid.Identifier,value=val,UTC_datetime=data_time,show_debug=is_starting_up)
        
        
        
        # Prints debugging information
        if is_starting_up:
            logger.debug(f'Datachange: {node.nodeid.Identifier}={val}')
        elif time.time() - self.last_debugprint_time > 300: # to throttle debugging to avoid log files bloating up
            self.last_debugprint_time = time.time()
            logger.debug(f'Cumulatively handled {self.stats_count} statistics. Last datachange: {node.nodeid.Identifier}={val}')

async def get_children(node,level=0,drop_commands=True):
    if drop_commands:
        if 'command' in node.__str__().lower():
            if node.__str__() in ['ns=4;s=EOS.Machine.Software.CommandEvents']:
                pass # These are acceptable non-commands, so we don't stop these.
            else:
                return []
    children_list = await node.get_children()
    if len(children_list)==0:
        all_children = [node]
    else:
        all_children = []
        for i in children_list:
            all_children += [c for c in await get_children(i,level=level+1) if c.__str__() not in map(str,all_children)]
    
    return all_children

async def main(shared_dict):
    # Added to EOS M300 authorisation page:
    # Client ID: client1
    # Client secret: xxx
    #client = Client(url='opc.tcp://**************:4843/')
    #client = Client(url='opc.tcp://client1:xxx@**************:4843/')
    # The client ought to be disconnected with disconnect() to avoid BadTooManySessions, or else I need to wait for the timeout 
    # of at least 6 minutes, but this has been difficult to implement as Windows does not handle signals properly, and that
    # it is tricky to catch signals to disconnect the client in an async routine too.
    
    
    if EOS_OPCUA_username is None and EOS_OPCUA_token is None:
        client = Client(url=f'opc.tcp://{EOS_OPCUA_IP}:{EOS_OPCUA_PORT}/')
    else:
        escaped_EOS_OPCUA_token = urllib.parse.quote_plus(EOS_OPCUA_token) # Since there might be an / char in the base64 char
        client = Client(url=f'opc.tcp://{EOS_OPCUA_username}:{escaped_EOS_OPCUA_token}@{EOS_OPCUA_IP}:{EOS_OPCUA_PORT}/')


    async with client:
        current_server_time_offset = ((await client.get_node(ua.ObjectIds.Server_ServerStatus_CurrentTime).get_value()).replace(tzinfo=pytz.utc) - datetime.datetime.now(tz=pytz.timezone('Asia/Singapore'))).total_seconds()
        if EOS_MACHINE_SI in ['SI3654','SI2373','SI2461']:
            all_nodes = await get_children(node=await client.nodes.objects.get_child(['2:EOS']))
        elif EOS_MACHINE_SI=='SI4490':
            all_nodes = await get_children(node=await client.nodes.objects.get_child(["4:V2",'4:EOS']))    
        all_nodes = all_nodes
        #all_nodes = all_nodes[:50]
        
        handler = SubscriptionHandler(shared_dict,current_server_time_offset)
        subscription = await client.create_subscription(500, handler)
        
        await subscription.subscribe_data_change(all_nodes)
        if False:
            await asyncio.sleep(10) # Let the subscription run for X seconds
        elif True:
            while True:
                await asyncio.sleep(10)
                if not check_socket(host=EOS_OPCUA_IP, port=EOS_OPCUA_PORT, timeout_ms=500,type='ipv4'):
                    raise ConnectionError(f'Machine {EOS_MACHINE_SI} appears to be turned off.')
                
                # Sometimes, asyncua.client.client has silent error "Error while renewing session" that is not caught since the renewal happens in the background. Thus, we have to resort to other means to catch the inactive connection, for example by checking for whether the last incoming data was too long ago.
                server_state_val = await client.nodes.server_state.read_value() # https://github.com/FreeOpcUa/python-opcua/blob/master/opcua/ua/uaprotocol_auto.py#L1050
                if (time.time() - handler.initiation_time > 120) and (server_state_val!=0): # Avoids throwing error when client is just starting up
                    logger.error(f"Server state is not 0 but {server_state_val}, indicating unhealthy state.")
                    raise TimeoutError(f'Server state ({server_state_val}) indicates unhealthy state. Raising exception...')
                if time.time() - handler.last_data_time > 900:
                    logger.debug(f"Server state at apparent death {server_state_val}.")
                    raise TimeoutError('No data was observed in the last 900 sceonds. Assuming that OPCUA session renewal has failed silently. Raising exception to reset connection...')

                # If debugging, kills the client after 10s so that we don't get BadTooManySessions for running this script repeatly.
                if False: # Activate only when debugging so that client is disconnected after a certain time
                    if (time.time() - handler.initiation_time > 8):
                        await client.disconnect()
                        logger.debug('Client disconnected in 10s deliberately for testing mode.')
                        while True:
                            logger.debug('Client has been disconnected. You can safely terminate the script...')
                            await asyncio.sleep(2)

                    
        else:
            await asyncio.Event().wait() # Let the subscription run forever
            
        await subscription.delete()
        logger.error('Finished')
        await asyncio.sleep(1) # Closes the client context manager and close the connection


def start_asyncio(shared_dict):
    while True:
        try:
            asyncio.run(main(shared_dict))
            break
        except TimeoutError:
            logger.error('Restarting connection because a long time has passed since last data was observed.')
        except (ConnectionError, asyncio.exceptions.TimeoutError):
            # Most likely the M300 is not turned on. To simulate the M300 being turned off,
            # set up a firewall rule that blocks connections to **************:4843
            logger.error(f'Machine {EOS_MACHINE_SI} does not appear to be turned on. Waiting out 30s...')
            time.sleep(30)
        except ua.uaerrors._auto.BadTooManySessions:
            logger.error('Received error BadTooManySessions. Waiting for previous sessions to auto-close...')
            time.sleep(10)


if __name__ == "__main__":
    manager = multiprocessing.Manager()
    shared_dict = manager.dict()
    
    print('Starting asyncio run')
    p1 = multiprocessing.Process(target=start_asyncio, args=(shared_dict,))
    p1.start()
    p1.join()

    #asyncio.run(main(shared_dict))
    #print('Finished asynio run with dict:{}'.format(str(shared_dict)))
