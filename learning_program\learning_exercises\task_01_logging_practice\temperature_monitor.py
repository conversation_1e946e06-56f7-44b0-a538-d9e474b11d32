# Import the logger from the lib_loggers file
from lib_loggers import set_logger
import time
import random
import yaml

try:
    with open("config.yaml") as config_file:
        # yaml.safe_load converts the yaml file into a python dictionary
        # The results are stored in the config variable, of type dictionary
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The config.yaml file should be present. Just copy the config.yaml.template file if no production token is required.')

# Set up the logger
logger = set_logger()

def simulate_temperature_reading():
    """Simulate reading temperature from a sensor"""
    # Generate a random temperature between 18-30°C
    temperature = random.uniform(18.0, 30.0)
    return round(temperature, 1)

def check_temperature_status(temp, min_temp, max_temp):
    """Check if temperature is in acceptable range"""
    if temp < min_temp:
        return "COLD"
    elif temp > max_temp:
        return "HOT" 
    else:
        return "NORMAL"

def main():
    env_choice = 'development'
    config_env = config[env_choice]
    min_normal = config_env['temperature']['min_normal']
    max_normal = config_env['temperature']['max_normal']
    no_of_simulations = config_env['monitoring']['readings_count']
    simulation_delay = config_env['monitoring']['delay_seconds']
    sensor_failure_rate = config_env['temperature']['sensor_failure_rate']

    logger.info("Starting temperature monitoring system...")
    
    normal_count = 0
    cold_count = 0
    hot_count = 0

    for i in range(no_of_simulations):
        sensor_failure = (random.random() <= sensor_failure_rate)
        if sensor_failure:
            logger.critical("Hardware error in the sensor!")
        else:
            simulated_temperature = simulate_temperature_reading()

            temperature_status = check_temperature_status(simulated_temperature, min_normal, max_normal)

            if temperature_status == "COLD":
                logger.warning(f"Temperature: {simulated_temperature}\tStatus: {temperature_status}")
                cold_count += 1
            elif temperature_status == "HOT":
                hot_count += 1
                logger.error(f"Temperature: {simulated_temperature}\tStatus: {temperature_status}")
            else:
                normal_count += 1
                logger.debug(f"Temperature: {simulated_temperature}\tStatus: {temperature_status}")

        time.sleep(simulation_delay)
       
    logger.info(f"Normal: {normal_count}\tCold: {cold_count}\tHot: {hot_count}")
    logger.info("Temperature monitoring completed")

if __name__ == "__main__":
    main()
