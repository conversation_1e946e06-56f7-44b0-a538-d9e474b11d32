# Asynchronous programming framework
asyncio>=3.4.3

# OPC UA client library for industrial communication
asyncua>=1.0.0

# Modbus TCP client library for industrial communication
pyModbusTCP>=0.2.1

# InfluxDB client for time series data storage (from Task 4)
influxdb-client>=1.36.0

# Configuration file handling
PyYAML>=6.0

# Data analysis and visualization
matplotlib>=3.5.0
numpy>=1.21.0
pandas>=1.3.0

# Async file operations (optional)
aiofiles>=0.8.0

# HTTP client for async web requests (optional)
aiohttp>=3.8.0

# Logging and monitoring
structlog>=22.0.0

# For datetime handling
python-dateutil>=2.8.0

# Cryptography for OPC UA security (optional)
cryptography>=3.4.0
