# Quick Start Deployment Guide

## Prerequisites Checklist

### Software Requirements
- [ ] **Python 3.8+** installed
- [ ] **InfluxDB 2.x** running and accessible
- [ ] **Network access** to target machines
- [ ] **Git** for version control

### Hardware Requirements
- [ ] **Dedicated computer** for 24/7 monitoring (can be Raspberry Pi 4+)
- [ ] **Network connectivity** to factory floor
- [ ] **Sufficient storage** for data collection (10GB+ recommended)

### Network Access Requirements
- [ ] **OPC UA access** to EOS machines (typically port 4840)
- [ ] **Modbus TCP access** to power meters (typically port 502)
- [ ] **InfluxDB access** (typically port 8086)
- [ ] **Internet access** for software updates (optional)

## 15-Minute Quick Setup

### Step 1: Clone and Prepare Environment (3 minutes)
```powershell
# Clone the repository
git clone <your-repo-url>
cd MicroSLMMonitoring

# Create Python virtual environment
python -m venv monitoring_env
monitoring_env\Scripts\activate

# Install base requirements
pip install asyncua influxdb-client pyModbusTCP pyyaml
```

### Step 2: Configure Database Connection (2 minutes)
```powershell
# Copy environment template
cd "For power meter"
copy env.yaml.template env.yaml

# Edit env.yaml with your InfluxDB details
notepad env.yaml
```

**env.yaml configuration:**
```yaml
production:
  influxdb_token: "your-influxdb-token-here"
  influxdb_bucket_name: "your-bucket-name"

development:
  influxdb_token: "dev-token"
  influxdb_bucket_name: "dev-bucket"
```

### Step 3: Test OPC UA Connection (5 minutes)
```powershell
# Navigate to OPC UA folder
cd "..\For EOS M290 OPCUA"

# Test connection to your machine
python opcua_test.py
```

**Edit opcua_test.py for your machine:**
```python
# Change this line to your machine's IP
url = 'opc.tcp://YOUR_MACHINE_IP:4840/'

# If authentication required:
url = 'opc.tcp://username:password@YOUR_MACHINE_IP:4840/'
```

### Step 4: Test Power Meter Connection (3 minutes)
```powershell
# Navigate to power meter folder
cd "..\For power meter"

# Edit main.py with your power meter IPs
notepad main.py
```

**Update equipment list in main.py:**
```python
# Around line 580, update with your equipment:
eqpts = {}
eqpts['YOUR_MACHINE_1'] = ('*************', 8899)
eqpts['YOUR_MACHINE_2'] = ('*************', 8899)
# Remove or comment out machines you don't have
```

### Step 5: Start Monitoring (2 minutes)
```powershell
# Test power monitoring first (easier to debug)
python main.py

# In a new terminal, test OPC UA monitoring
cd "..\For EOS M290 OPCUA"
python main.py
```

## Production Deployment

### Step 1: Create Windows Service (Recommended)
```powershell
# Install NSSM (Non-Sucking Service Manager)
# Download from: https://nssm.cc/download

# Create service for power monitoring
nssm install "EOS_PowerMonitoring" "C:\path\to\python.exe"
nssm set "EOS_PowerMonitoring" AppDirectory "C:\path\to\MicroSLMMonitoring\For power meter"
nssm set "EOS_PowerMonitoring" AppParameters "main.py"
nssm start "EOS_PowerMonitoring"

# Create service for OPC UA monitoring  
nssm install "EOS_OPCUAMonitoring" "C:\path\to\python.exe"
nssm set "EOS_OPCUAMonitoring" AppDirectory "C:\path\to\MicroSLMMonitoring\For EOS M290 OPCUA"
nssm set "EOS_OPCUAMonitoring" AppParameters "main.py"
nssm start "EOS_OPCUAMonitoring"
```

### Step 2: Set Up Monitoring & Logging
```powershell
# Create logs directory
mkdir C:\EOS_Monitoring_Logs

# Configure log rotation in lib_loggers.py
# Logs will automatically rotate and prevent disk space issues
```

### Step 3: Configure Firewall (If needed)
```powershell
# Allow outbound connections to InfluxDB
netsh advfirewall firewall add rule name="InfluxDB_Out" dir=out action=allow protocol=TCP localport=8086

# Allow connections to OPC UA servers
netsh advfirewall firewall add rule name="OPCUA_Out" dir=out action=allow protocol=TCP localport=4840

# Allow connections to Modbus devices
netsh advfirewall firewall add rule name="Modbus_Out" dir=out action=allow protocol=TCP localport=502,8899
```

## Verification & Testing

### Health Check Script
```python
# Create health_check.py
import requests
import time
from influxdb_client import InfluxDBClient

def check_influxdb_connection():
    try:
        with InfluxDBClient(url="http://your-influxdb:8086", token="your-token") as client:
            # Try to query recent data
            query = 'from(bucket:"your-bucket") |> range(start: -5m) |> limit(n:1)'
            result = client.query_api().query(query)
            return len(list(result)) > 0
    except:
        return False

def check_recent_data():
    # Check if data was received in last 5 minutes
    with InfluxDBClient(url="http://your-influxdb:8086", token="your-token") as client:
        query = '''
        from(bucket: "your-bucket")
        |> range(start: -5m)
        |> group(columns: ["eqpt"])
        |> count()
        '''
        result = client.query_api().query(query)
        return result

if __name__ == "__main__":
    print("🔍 Checking InfluxDB connection...")
    if check_influxdb_connection():
        print("✅ InfluxDB connection OK")
    else:
        print("❌ InfluxDB connection failed")
    
    print("🔍 Checking recent data...")
    recent_data = check_recent_data()
    for table in recent_data:
        for record in table.records:
            print(f"✅ {record.values['eqpt']}: {record.values['_value']} data points in last 5 minutes")
```

### Common Issues & Solutions

#### Issue 1: "Connection Refused" to OPC UA Server
```python
# Solutions to try:
1. Check if machine OPC UA server is enabled
2. Verify IP address and port (usually 4840)
3. Check if authentication is required
4. Test with UaExpert client software first
5. Check firewall settings on both ends
```

#### Issue 2: Power Meter Not Responding
```python
# Debug steps:
1. Ping the power meter: ping *************
2. Check port availability: telnet ************* 8899
3. Verify Modbus settings on power meter
4. Check if PW11 bridge device needs reboot
5. Use QModMaster to test Modbus communication
```

#### Issue 3: InfluxDB Write Errors
```python
# Common causes:
1. Wrong bucket name or organization
2. Expired or invalid token
3. Network connectivity issues
4. Data format problems
5. Bucket permissions

# Debug with:
curl -X POST "http://your-influxdb:8086/api/v2/write?org=AMI&bucket=testbucket" \
  -H "Authorization: Token your-token" \
  -d "test_measurement value=1.0"
```

#### Issue 4: High Memory Usage
```python
# Memory optimization:
1. Reduce subscription frequency
2. Implement data batching
3. Add garbage collection hints
4. Monitor for memory leaks
5. Restart services periodically

# Add to your code:
import gc
gc.collect()  # Force garbage collection periodically
```

## Monitoring Script Health

### Create Watchdog Script
```python
# watchdog.py - Monitors the monitoring scripts!
import psutil
import time
import subprocess
import logging

def is_process_running(process_name):
    for process in psutil.process_iter(['pid', 'name', 'cmdline']):
        if process_name in ' '.join(process.info['cmdline'] or []):
            return True
    return False

def restart_service(service_name):
    try:
        subprocess.run(['net', 'stop', service_name], check=True)
        time.sleep(5)
        subprocess.run(['net', 'start', service_name], check=True)
        logging.info(f"Restarted service: {service_name}")
        return True
    except subprocess.CalledProcessError:
        logging.error(f"Failed to restart service: {service_name}")
        return False

def main():
    services_to_monitor = [
        'EOS_PowerMonitoring',
        'EOS_OPCUAMonitoring'
    ]
    
    while True:
        for service in services_to_monitor:
            # Check if service is running and healthy
            # Implement your health checks here
            pass
        
        time.sleep(60)  # Check every minute

if __name__ == "__main__":
    main()
```

### Set Up Email Alerts (Optional)
```python
# email_alerts.py
import smtplib
from email.mime.text import MIMEText

def send_alert(subject, body):
    msg = MIMEText(body)
    msg['Subject'] = f"EOS Monitoring Alert: {subject}"
    msg['From'] = "<EMAIL>"
    msg['To'] = "<EMAIL>"
    
    with smtplib.SMTP('smtp.yourcompany.com') as server:
        server.send_message(msg)

# Use in your monitoring scripts:
if time_since_last_data > 300:  # 5 minutes
    send_alert("Data Collection Stopped", 
               f"No data received from {machine_name} for 5 minutes")
```

## Performance Tuning

### Optimize Data Collection Frequency
```python
# High-frequency sensors (every 1-5 seconds):
- Machine status
- Critical temperatures
- Alarm states

# Medium-frequency sensors (every 30-60 seconds):
- Power consumption
- Non-critical temperatures
- Position data

# Low-frequency sensors (every 5-15 minutes):
- Cumulative counters
- Diagnostic data
- Environmental conditions
```

### Database Optimization
```python
# InfluxDB retention policies:
- Real-time data: 7 days (full resolution)
- Historical data: 90 days (5-minute averages)
- Archive data: 2 years (1-hour averages)

# Create downsampling tasks in InfluxDB:
option task = {name: "downsample-5min", every: 5m}

from(bucket: "realtime")
  |> range(start: -10m)
  |> aggregateWindow(every: 5m, fn: mean)
  |> to(bucket: "historical")
```

This deployment guide should get you up and running quickly, with production-ready configurations for 24/7 operation!
