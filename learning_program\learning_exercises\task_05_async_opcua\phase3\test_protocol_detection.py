#!/usr/bin/env python3
"""
Test script to demonstrate the protocol differentiation capability
of the manufacturing monitor system without requiring InfluxDB.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from config import load_config
from logger import setup_logger

# Set up basic logging
logger = setup_logger(log_level=logging.INFO)
log = logging.getLogger("protocol_test")

def test_protocol_detection():
    """Test that we can properly detect and differentiate protocols."""
    
    log.info("="*60)
    log.info("Protocol Detection Test")
    log.info("="*60)
    
    try:
        # Load configuration
        config = load_config("config.yaml")
        machines = config['development']['machines']
        
        log.info(f"Found {len(machines)} machines in configuration:")
        log.info("")
        
        opcua_count = 0
        modbus_count = 0
        
        for machine_name, machine_config in machines.items():
            protocol = machine_config.get('protocol', 'opcua')  # Default to OPC UA
            machine_type = machine_config.get('type', 'unknown')
            
            log.info(f"Machine: {machine_name}")
            log.info(f"  Type: {machine_type}")
            log.info(f"  Protocol: {protocol.upper()}")
            
            if protocol == 'opcua':
                opcua_config = machine_config.get('opcua', {})
                url = opcua_config.get('url', 'Not specified')
                log.info(f"  OPC UA URL: {url}")
                opcua_count += 1
                
            elif protocol == 'modbus':
                modbus_config = machine_config.get('modbus', {})
                host = modbus_config.get('host', 'Not specified')
                port = modbus_config.get('port', 502)
                log.info(f"  Modbus Host: {host}:{port}")
                
                # Show some register info
                registers = machine_config.get('registers', {})
                log.info(f"  Registers configured: {len(registers)}")
                if registers:
                    # Show first few registers as examples
                    for i, (reg_name, reg_config) in enumerate(list(registers.items())[:3]):
                        addr = reg_config.get('address', 0)
                        log.info(f"    {reg_name}: 0x{addr:04X}")
                    if len(registers) > 3:
                        log.info(f"    ... and {len(registers) - 3} more registers")
                
                modbus_count += 1
            
            log.info("")
        
        log.info("="*60)
        log.info("Summary:")
        log.info(f"  OPC UA machines: {opcua_count}")
        log.info(f"  Modbus machines: {modbus_count}")
        log.info(f"  Total machines: {len(machines)}")
        log.info("="*60)
        
        return True
        
    except Exception as e:
        log.error(f"Error during protocol detection test: {e}")
        return False

async def test_monitor_creation():
    """Test that we can create the appropriate monitor types."""
    
    log.info("Testing monitor creation (without actual connections)...")
    
    try:
        config = load_config("config.yaml")
        machines = config['development']['machines']
        
        # Mock InfluxWriter for testing
        class MockInfluxWriter:
            def write(self, measurement, fields, tags, timestamp):
                log.debug(f"Mock write: {measurement} - {len(fields)} fields")
        
        mock_influx = MockInfluxWriter()
        
        monitor_tasks = []
        
        for machine_name, machine_config in machines.items():
            protocol = machine_config.get('protocol', 'opcua')
            
            log.info(f"Would create {protocol.upper()} monitor for: {machine_name}")
            
            # We're not actually creating the tasks to avoid connection errors
            # but this shows the differentiation logic works
            if protocol == 'opcua':
                log.info(f"  -> Using OPC UA monitor")
            elif protocol == 'modbus':
                log.info(f"  -> Using Modbus monitor")
        
        log.info(f"Successfully identified protocols for all {len(machines)} machines")
        return True
        
    except Exception as e:
        log.error(f"Error during monitor creation test: {e}")
        return False

def main():
    """Main test function."""
    
    log.info("Starting Protocol Differentiation Test")
    log.info("")
    
    # Test 1: Protocol Detection
    if not test_protocol_detection():
        log.error("Protocol detection test failed!")
        return False
    
    # Test 2: Monitor Creation Logic
    asyncio.run(test_monitor_creation())
    
    log.info("")
    log.info("All tests completed successfully!")
    log.info("The system can properly differentiate between OPC UA and Modbus protocols.")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log.info("Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        log.error(f"Test failed with error: {e}")
        sys.exit(1)
