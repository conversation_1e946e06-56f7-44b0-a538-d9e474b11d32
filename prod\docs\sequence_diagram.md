```mermaid
sequenceDiagram
    participant User
    participant Discoverer as discover_nodes.py
    participant Main as main.py
    participant Logger as logger.py
    participant Config as config.py
    participant InfluxWriter as influx_writer.py
    participant OpcuaMoni<PERSON> as opcua_monitor.py
    participant Equipment as Industrial Equipment
    participant InfluxDB as InfluxDB Server
    participant ConfigFile as config.yaml

    %% Configuration Discovery Phase (Optional - Run before main application)
    Note over User,ConfigFile: Configuration Discovery Phase (Optional)
    User->>Discoverer: python discover_nodes.py opc.tcp://server:port
    Discoverer->>Equipment: connect to OPC UA server
    Equipment-->>Discoverer: connection established

    loop Node Discovery
        Discoverer->>Equipment: browse node tree recursively
        Equipment-->>Discoverer: return node information
        Discoverer->>Discoverer: filter custom namespace nodes (ns > 0)
        Discoverer->>Discoverer: collect readable data nodes
    end

    Discoverer->>Discoverer: generate_machine_config()
    Discoverer->>ConfigFile: save_config_to_file()
    ConfigFile-->>Discoverer: configuration saved
    Discoverer-->>User: discovery complete, config generated

    %% Application Startup (Main Monitoring Service)
    Note over User,InfluxDB: Main Application Startup
    User->>Main: Start Application

    Note over Main: Application Initialization Phase
    Main->>Logger: setup_logger(log_level, log_file)
    Logger->>Logger: create console & file handlers
    Logger-->>Main: configured logger

    Main->>Config: load_config("config.yaml")
    Config->>ConfigFile: read configuration file
    ConfigFile-->>Config: YAML content
    Config->>Config: parse YAML file
    Config->>Config: validate configuration
    Config-->>Main: configuration dict
    
    Main->>Main: determine environment (dev/prod)
    Main->>Main: validate InfluxDB config
    
    %% InfluxDB Initialization
    Note over Main,InfluxDB: Database Connection Setup
    Main->>InfluxWriter: InfluxWriter(influx_config)
    InfluxWriter->>InfluxDB: ping() - test connection
    InfluxDB-->>InfluxWriter: connection confirmed
    InfluxWriter->>InfluxWriter: create async write API
    InfluxWriter-->>Main: initialized writer
    
    %% Machine Monitoring Setup
    Note over Main,Equipment: Monitoring Tasks Creation
    loop For each configured machine
        Main->>Main: create_task(monitor_opcua_machine)
        Main->>OpcuaMonitor: monitor_opcua_machine(machine_name, config, influx_writer)
        
        %% OPC UA Connection Loop
        loop Connection retry logic
            OpcuaMonitor->>Equipment: connect to OPC UA server
            alt Connection successful
                Equipment-->>OpcuaMonitor: connection established
                OpcuaMonitor->>OpcuaMonitor: create subscription handler
                OpcuaMonitor->>Equipment: subscribe to data changes
                Equipment-->>OpcuaMonitor: subscription confirmed
            else Connection failed
                Equipment-->>OpcuaMonitor: connection error
                OpcuaMonitor->>Logger: log error
                OpcuaMonitor->>OpcuaMonitor: wait retry_delay
            end
        end
    end
    
    %% Normal Operation - Data Flow
    Note over Equipment,InfluxDB: Continuous Data Monitoring
    loop Continuous monitoring
        Equipment->>OpcuaMonitor: data change notification
        OpcuaMonitor->>OpcuaMonitor: datachange_notification()
        OpcuaMonitor->>OpcuaMonitor: format data with tags & timestamp
        OpcuaMonitor->>InfluxWriter: write(measurement, fields, tags, timestamp)
        InfluxWriter->>InfluxWriter: create Point object
        InfluxWriter->>InfluxDB: async write to bucket
        InfluxDB-->>InfluxWriter: write queued
        InfluxWriter->>Logger: log debug info
    end
    
    %% Error Handling Scenarios
    Note over OpcuaMonitor,InfluxDB: Error Handling
    alt Connection lost to equipment
        Equipment-->>OpcuaMonitor: connection timeout
        OpcuaMonitor->>Logger: log connection error
        OpcuaMonitor->>OpcuaMonitor: trigger reconnection logic
        OpcuaMonitor->>Equipment: attempt reconnection
    else InfluxDB write error
        InfluxWriter->>InfluxDB: write attempt
        InfluxDB-->>InfluxWriter: write error
        InfluxWriter->>Logger: log write error
        InfluxWriter->>InfluxWriter: retry or queue for later
    end
    
    %% Graceful Shutdown
    Note over User,InfluxDB: Application Shutdown
    User->>Main: SIGINT/SIGTERM
    Main->>Main: cancel all monitoring tasks
    Main->>OpcuaMonitor: cleanup connections
    OpcuaMonitor->>Equipment: disconnect
    Main->>InfluxWriter: flush pending writes
    InfluxWriter->>InfluxDB: flush write buffer
    Main->>Logger: log shutdown complete
    Main-->>User: application terminated

```