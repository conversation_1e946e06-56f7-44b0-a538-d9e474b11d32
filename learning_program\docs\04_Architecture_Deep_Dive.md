# Project Architecture Deep Dive

## Folder-by-Folder Breakdown

### 📁 `For EOS M290 OPCUA/` - Process Data Collection
**Purpose**: Collects real-time manufacturing process data from EOS 3D printers

**Key Files**:
- **`main.py`**: Production monitoring script (runs 24/7)
- **`opcua_test.py`**: Development/debugging tool

**What it monitors**:
```python
# Example data points collected:
EOS.Machine.Environment.Temperature          → 245.6°C
EOS.Machine.Sensors.BuildingPlatform.PositionZ → 150.2mm  
EOS.Machine.CurrentJob.LayerCountCurrent     → 1847
EOS.Machine.Sensors.ProcessChamber.O2ConcentrationTop → 0.3%
```

**Technology Stack**:
- **asyncua**: For OPC UA communication
- **asyncio**: For handling concurrent operations
- **influxdb-client**: For database uploads
- **Threading**: For health monitoring

### 📁 `For power meter/` - Energy Data Collection  
**Purpose**: Monitors electrical consumption of all equipment using external power meters

**Key Files**:
- **`main.py`**: Multi-threaded power monitoring (runs 24/7)
- **`env.yaml.template`**: Configuration template
- **`lib_loggers.py`**: Logging utilities
- **`requirements.txt`**: Python dependencies

**What it monitors**:
```python
# Power data collected:
Active Power Total (kW)     → 15.2 kW
Current A (A)              → 22.1 A
Voltage A-N (V)            → 230.4 V
Power Factor Total         → 0.89
Total Import (kWh)         → 1547.3 kWh
```

**Technology Stack**:
- **pyModbusTCP**: For Modbus communication with Eastron SDM630 meters
- **threading**: For monitoring multiple meters simultaneously
- **requests**: For PW11 device management (reboot, WiFi status)
- **influxdb-client**: For database uploads

### 📁 `InfluxDB/` - Data Analysis & Reporting
**Purpose**: Advanced analysis and reporting beyond real-time dashboards

**Key Files**:

#### `compare_m290.py` - Process Engineering Analysis
**Purpose**: Analyzes pump-down process consistency
```python
# What it does:
1. Finds all pump-down events (O2 drops from ~15% to <1%)
2. Extracts 20-minute windows around each event
3. Applies quality filters to reject bad cycles
4. Creates overlay plots showing process consistency
5. Identifies process drift and optimization opportunities
```

#### `pull_data.py` - Quick Data Visualization
**Purpose**: Simple time-series plotting for ad-hoc analysis
```python
# Typical use cases:
- Plot last 24 hours of temperature data
- Compare power consumption across different machines
- Quick correlation analysis between process parameters
```

#### `upload_data.py` - Database Testing & Examples
**Purpose**: Learning tool and connection testing
```python
# Contains examples of:
- How to write data to InfluxDB
- How to read data from InfluxDB  
- Different query patterns and data structures
```

### 🗄️ **InfluxDB Database** - Central Data Storage
**Purpose**: High-performance time-series database optimized for sensor data

**Database Structure**:
```
influxdb.ami.modelfactory.sg:8086/
├── Bucket: "eos_opcua"
│   ├── Measurement: "printer stats"
│   │   ├── Tags: eqpt=SI3654, sensor_type=temperature
│   │   └── Fields: EOS.Machine.Environment.Temperature=245.6
│   └── Retention: 30 days
├── Bucket: "power_consumption" 
│   ├── Measurement: "power_consumption"
│   │   ├── Tags: eqpt=EOS_SI3654, source=bridged_SDM630
│   │   └── Fields: Active Power Total (kW)=15.2
│   └── Retention: 90 days
└── Bucket: "long_term_archive"
    └── Retention: 2 years
```

### 🌐 **Grafana Dashboards** (Missing from current setup)
**Purpose**: Real-time visualization and alerting

**Typical Dashboard Structure**:
```
Manufacturing Overview Dashboard:
├── Row 1: Machine Status
│   ├── EOS SI3654 Status Panel
│   ├── EOS SI2373 Status Panel  
│   └── EOS SI2461 Status Panel
├── Row 2: Process Parameters
│   ├── Temperature Trends (24h)
│   ├── Pressure Monitoring
│   └── O2 Concentration
├── Row 3: Power Monitoring
│   ├── Total Power Consumption
│   ├── Power per Machine
│   └── Energy Cost Tracking
└── Row 4: Alerts & Notifications
    ├── Active Alarms
    ├── Maintenance Reminders
    └── Process Deviations
```

## Data Flow Architecture

### Detailed Data Journey

#### 1. **Data Generation** (Physical Layer)
```
EOS 3D Printer Sensors → Internal PLC → OPC UA Server
Power Meter → Modbus Registers → PW11 WiFi Bridge → Network
```

#### 2. **Data Collection** (Application Layer)
```python
# EOS Process Data Flow:
OPC UA Server → asyncua client → Python processing → InfluxDB Point → Database

# Power Data Flow:  
Modbus TCP → pyModbusTCP client → Python processing → InfluxDB Point → Database
```

#### 3. **Data Storage** (Persistence Layer)
```
InfluxDB receives data points:
{
    "measurement": "printer stats",
    "tags": {"eqpt": "SI3654"},
    "fields": {"temperature": 245.6},
    "timestamp": "2025-07-30T14:30:25Z"
}
```

#### 4. **Data Analysis** (Processing Layer)
```python
# Python Analysis Pipeline:
InfluxDB → Flux Query → Pandas DataFrame → NumPy Processing → Matplotlib Plot
```

#### 5. **Data Visualization** (Presentation Layer)
```
Grafana: InfluxDB → Flux Query → Real-time Charts → Web Dashboard
Python: InfluxDB → Analysis Scripts → Static Reports → PNG/PDF Files
```

## Critical Design Patterns

### 1. **Asynchronous Data Collection**
```python
# Why async is essential:
async def monitor_machine():
    async with client:  # Manages connection lifecycle
        while True:
            # Non-blocking operations allow handling multiple machines
            await subscription.process_events()
            await health_check()
            await asyncio.sleep(10)  # Doesn't block other tasks
```

### 2. **Intelligent Data Filtering**
```python
# Prevents database overload:
update_criteria = {
    'temperature': {
        'max_diff': 2,      # Only upload if change > 2°C
        'min_time': 30,     # Wait at least 30s
        'max_time': None    # No forced uploads
    }
}
```

### 3. **Robust Error Handling**
```python
# Industrial networks are unreliable:
while True:
    try:
        await collect_data()
    except ConnectionError:
        logger.error('Connection lost, reconnecting...')
        await reconnect()
    except TimeoutError:
        logger.error('Timeout, checking health...')
        await health_check()
```

### 4. **Multi-threaded Power Monitoring**
```python
# Each power meter gets its own thread:
threads = {}
for equipment, (ip, port) in equipment_list.items():
    threads[equipment] = threading.Thread(
        target=monitor_power_meter, 
        args=(equipment, ip, port)
    )
    threads[equipment].start()
```

## Performance Considerations

### Database Write Optimization
```python
# Batch writes for efficiency:
points = []
for sensor_data in batch:
    points.append(create_influx_point(sensor_data))

# Single write operation for entire batch
write_api.write(bucket=bucket, record=points)
```

### Memory Management
```python
# Prevent memory leaks in long-running processes:
with InfluxDBClient(url=url, token=token) as client:
    with client.write_api() as write_api:
        # Automatic cleanup when context exits
        write_api.write(bucket=bucket, record=data)
```

### Network Resilience
```python
# Automatic reconnection with exponential backoff:
retry_count = 0
while True:
    try:
        await connect()
        break
    except ConnectionError:
        wait_time = min(300, 5 * (2 ** retry_count))  # Max 5 minutes
        await asyncio.sleep(wait_time)
        retry_count += 1
```

## Security Architecture

### Network Security
```
Factory Network (Isolated):
├── EOS Machines: 10.100.113.x
├── Power Meters: 192.168.192.x
└── Data Server: influxdb.ami.modelfactory.sg

Authentication:
├── OPC UA: Username/Token pairs
├── InfluxDB: API tokens with bucket-specific permissions
└── Grafana: User roles (viewer, editor, admin)
```

### Data Security
```python
# Sensitive data handling:
env_config = {
    'production': {
        'influxdb_token': os.environ['INFLUX_TOKEN'],  # Never hardcode
        'opcua_password': os.environ['OPCUA_PASS']
    }
}
```

This architecture is designed for **24/7 industrial operation** with emphasis on reliability, scalability, and maintainability.
