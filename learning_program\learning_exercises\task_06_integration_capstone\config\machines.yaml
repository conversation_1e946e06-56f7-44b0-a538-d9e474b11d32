# Machine configuration - based on real project patterns
# This file defines all machines and devices to monitor

# OPC UA Machines (based on EOS M290 project)
opcua_machines:
  EOS_SI3654:
    type: "3d_printer"
    manufacturer: "EOS"
    model: "M290"
    serial_number: "SI3654"
    location: "Building_A_Floor_2"
    
    # OPC UA connection settings
    opcua:
      url: "opc.tcp://10.100.113.104:4843/"
      namespace: 4
      authentication:
        enabled: true
        username: "client1"
        password: "your-password"  # Store securely
      connection:
        timeout_seconds: 30
        retry_attempts: 5
        retry_delay_seconds: 10
        
    # Data points to monitor (real EOS node structure)
    data_points:
      temperature:
        node_id: "ns=4;s=EOS.Machine.Environment.Temperature"
        data_type: "float"
        unit: "celsius"
        normal_range: [200, 280]
        critical_range: [180, 300]
        collection_interval_ms: 1000
        
      pressure:
        node_id: "ns=4;s=EOS.Machine.ProcessChamber.Pressure"
        data_type: "float"
        unit: "bar"
        normal_range: [0.8, 1.2]
        critical_range: [0.5, 1.5]
        collection_interval_ms: 2000
        
      humidity:
        node_id: "ns=4;s=EOS.Machine.Environment.Humidity"
        data_type: "float"
        unit: "percent"
        normal_range: [30, 70]
        critical_range: [20, 80]
        collection_interval_ms: 5000
        
      status:
        node_id: "ns=4;s=EOS.Machine.Status.Current"
        data_type: "string"
        collection_interval_ms: 1000
        
      build_progress:
        node_id: "ns=4;s=EOS.Machine.Process.BuildProgress"
        data_type: "float"
        unit: "percent"
        collection_interval_ms: 10000
        
    # Power monitoring via separate Modbus connection
    power_monitoring:
      protocol: "modbus"
      ip: "***************"
      port: 8899
      unit_id: 1
      registers:
        voltage_l1: {address: 0, type: "float32", unit: "V"}
        voltage_l2: {address: 2, type: "float32", unit: "V"}
        voltage_l3: {address: 4, type: "float32", unit: "V"}
        current_total: {address: 48, type: "float32", unit: "A"}
        power_active: {address: 52, type: "float32", unit: "kW"}

  # Simulated machines for learning
  EOS_Simulator_A:
    type: "3d_printer"
    manufacturer: "EOS"
    model: "M290_Simulator"
    location: "Simulation_Lab"
    
    opcua:
      url: "opc.tcp://localhost:4840/"
      namespace: 4
      authentication:
        enabled: false
      connection:
        timeout_seconds: 10
        retry_attempts: 3
        retry_delay_seconds: 5
        
    data_points:
      temperature:
        node_id: "ns=4;s=EOS.Machine.Environment.Temperature"
        data_type: "float"
        unit: "celsius"
        normal_range: [240, 260]
        collection_interval_ms: 1000
        
      pressure:
        node_id: "ns=4;s=EOS.Machine.ProcessChamber.Pressure"
        data_type: "float"
        unit: "bar"
        normal_range: [0.95, 1.05]
        collection_interval_ms: 2000

# Modbus Devices (based on power meter project)
modbus_devices:
  PowerMeter_PW11_001:
    type: "power_meter"
    manufacturer: "Eastron"
    model: "SDM630"
    serial_number: "PW11001"
    location: "Electrical_Panel_A"
    
    # Modbus connection settings
    modbus:
      ip: "***************"
      port: 8899
      unit_id: 1
      timeout_seconds: 10
      retry_attempts: 3
      retry_delay_seconds: 2
      
    # Register map (real SDM630 registers)
    registers:
      voltage_l1_n:
        address: 0
        type: "float32"
        unit: "V"
        description: "Voltage L1-N"
        normal_range: [220, 240]
        
      voltage_l2_n:
        address: 2
        type: "float32"
        unit: "V"
        description: "Voltage L2-N"
        normal_range: [220, 240]
        
      voltage_l3_n:
        address: 4
        type: "float32"
        unit: "V"
        description: "Voltage L3-N"
        normal_range: [220, 240]
        
      current_l1:
        address: 6
        type: "float32"
        unit: "A"
        description: "Current L1"
        normal_range: [0, 100]
        
      current_l2:
        address: 8
        type: "float32"
        unit: "A"
        description: "Current L2"
        normal_range: [0, 100]
        
      current_l3:
        address: 10
        type: "float32"
        unit: "A"
        description: "Current L3"
        normal_range: [0, 100]
        
      power_active_total:
        address: 52
        type: "float32"
        unit: "kW"
        description: "Active Power Total"
        normal_range: [0, 50]
        
      power_reactive_total:
        address: 54
        type: "float32"
        unit: "kVAr"
        description: "Reactive Power Total"
        
      power_apparent_total:
        address: 56
        type: "float32"
        unit: "kVA"
        description: "Apparent Power Total"
        
      frequency:
        address: 70
        type: "float32"
        unit: "Hz"
        description: "Frequency"
        normal_range: [49.5, 50.5]
        
    collection:
      interval_seconds: 5
      batch_size: 10
      
  # Environmental monitoring
  Environmental_Sensor_001:
    type: "environmental_sensor"
    manufacturer: "Generic"
    model: "TempHumidity"
    location: "Production_Floor"
    
    modbus:
      ip: "*************"
      port: 502
      unit_id: 1
      timeout_seconds: 5
      
    registers:
      temperature:
        address: 100
        type: "int16"
        unit: "celsius"
        scale_factor: 0.1
        normal_range: [18, 28]
        
      humidity:
        address: 101
        type: "int16"
        unit: "percent"
        scale_factor: 0.1
        normal_range: [30, 70]

# Machine groups for coordinated monitoring
machine_groups:
  production_line_a:
    description: "Main production line machines"
    machines: ["EOS_SI3654", "PowerMeter_PW11_001"]
    priority: "high"
    
  simulation_lab:
    description: "Simulation and testing machines"
    machines: ["EOS_Simulator_A"]
    priority: "medium"
    
  environmental:
    description: "Environmental monitoring devices"
    machines: ["Environmental_Sensor_001"]
    priority: "low"

# Global machine settings
global_settings:
  data_retention:
    raw_data_days: 30
    aggregated_data_months: 12
    
  alerting:
    enable_email_alerts: true
    enable_dashboard_alerts: true
    alert_cooldown_minutes: 15
    
  maintenance:
    scheduled_maintenance_window: "02:00-04:00"
    auto_restart_on_failure: true
    max_restart_attempts: 3
