import struct
from lib_loggers import set_logger

logger = set_logger()

def float_to_registers(float_value):
    """Convert a float to two Modbus registers (like the simulator does)"""
    
    logger.debug(f"Converting float {float_value} to registers")
    
    try:
        # Packs the float into 4 bytes using IEEE 754 format
        packed_bytes = struct.pack('>f', float_value)
        # Unpacks the 4 bytes into two 16-bit UINTs
        msb, lsb = struct.unpack('>HH', packed_bytes)
        return [msb, lsb]
    except Exception as e:
        logger.error(f"Error converting float {float_value} to registers: {e}")
        return None

def registers_to_float(register_pair):
    """Convert two Modbus registers back to float (like your client needs)"""
    # TODO: Implement register to float conversion
    # This is what you'll use to read power meter data
    # Steps:
    # 1. Combine registers: combined = (reg[0] << 16) | reg[1]
    # 2. Pack as unsigned int: struct.pack('>I', combined)
    # 3. Unpack as float: struct.unpack('>f', packed)[0]
    
    if len(register_pair) != 2:
        raise ValueError("Expected exactly 2 registers for float conversion")
    
    logger.debug(f"Converting registers {register_pair} to float")
    
    try:
        # Your code here
        # Hint: combined_value = (register_pair[0] << 16) | register_pair[1]
        # Hint: packed_value = struct.pack('>I', combined_value)
        # Hint: float_value = struct.unpack('>f', packed_value)[0]
        # Hint: return float_value
        # Combines the 2 16-bit registers into a single 32-bit value
        combined_value = (register_pair[0] << 16) | register_pair[1]
        # Convert the 32-bit value into a 4-byte array
        packed_value = struct.pack('>I', combined_value)
        # Unpacks the 4 bytes into a float
        float_value = struct.unpack('>f', packed_value)[0]
        return float_value
    except Exception as e:
        logger.error(f"Error converting registers {register_pair} to float: {e}")
        return None

def test_conversion_accuracy():
    """Test that conversion is accurate (round-trip test)"""
    logger.info("=== TESTING DATA CONVERSION ACCURACY ===")
    
    test_values = [230.5, 12.3, 8.45, 0.0, -15.7, 999.99, 1.23456789]
    tolerance = 0.001  # Allow small floating-point errors
    
    successful_tests = 0
    total_tests = len(test_values)
    
    for original_value in test_values:
        # TODO: Test round-trip conversion
        # 1. Convert float to registers
        # 2. Convert registers back to float  
        # 3. Check if result matches original (within small tolerance)
        # 4. Log results
        
        logger.info(f"Testing value: {original_value}")
        
        converted_registers = float_to_registers(original_value)
        converted_float = registers_to_float(converted_registers)

        if abs(converted_float - original_value) < tolerance:
            logger.info(f" Test passed: {original_value} -> {converted_registers} -> {converted_float}")
            successful_tests += 1
        else:
            logger.error(f" Test failed: {original_value} -> {converted_registers} -> {converted_float}")
        
    # TODO: Generate summary
    success_rate = (successful_tests / total_tests) * 100
    logger.info(f"=== CONVERSION TEST SUMMARY ===")
    logger.info(f"Successful conversions: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if successful_tests == total_tests:
        logger.info(" All conversion tests passed!")
        return True
    else:
        logger.warning(" Some conversion tests failed!")
        return False

def demonstrate_ieee754_concepts():
    """Educational demonstration of IEEE 754 float format"""
    logger.info("=== IEEE 754 FLOAT FORMAT DEMONSTRATION ===")
    
    # Show how different values are represented
    demo_values = [1.0, -1.0, 0.0, 3.14159, 230.5]
    
    for value in demo_values:
        # TODO: Show the binary representation
        # Your code here
        # Hint: Use struct.pack('>f', value) to get bytes
        # Hint: Convert bytes to hex for display
        # Hint: Show both the original value and its hex representation
        packed_bytes = struct.pack('>f', value)
        hex_representation = packed_bytes.hex()
        logger.info(f"Value: {value} -> Hex: {hex_representation}")
        logger.info(f"msb: {hex(packed_bytes[0])}, lsb: {hex(packed_bytes[1])}")
        # Show hex representation
        # Show register breakdown
        
def understand_modbus_data_types():
    """Learn about different Modbus data types"""
    logger.info("=== MODBUS DATA TYPES EXPLANATION ===")
    
    logger.info("Common Modbus data types:")
    logger.info("- 16-bit Integer: 1 register (range: -32,768 to 32,767)")
    logger.info("- 32-bit Integer: 2 registers (range: -2,147,483,648 to 2,147,483,647)")
    logger.info("- IEEE 754 Float: 2 registers (what we're using for power meter data)")
    logger.info("- Boolean: 1 bit (for status flags)")
    
    logger.info("Why IEEE 754 for power meters?")
    logger.info("- Voltage: 230.5V (needs decimal precision)")
    logger.info("- Current: 12.34A (needs decimal precision)")  
    logger.info("- Power: 8.456kW (needs decimal precision)")
    logger.info("- Integers can't represent decimal values accurately")

def main():
    """Phase 2: Data Conversion Practice"""
    logger.info("=== PHASE 2: DATA CONVERSION PRACTICE ===")
    
    # Step 1: Learn about data types
    understand_modbus_data_types()
    
    # Step 2: Demonstrate IEEE 754 format
    demonstrate_ieee754_concepts()
    
    # Step 3: Test conversion functions
    if test_conversion_accuracy():
        logger.info(" Phase 2 complete! Ready for Phase 3 (Modbus Communication)")
        logger.info("Next step: Run 'python phase2_modbus_client.py'")
    else:
        logger.error(" Fix conversion functions before proceeding to Phase 3")

if __name__ == "__main__":
    main()
