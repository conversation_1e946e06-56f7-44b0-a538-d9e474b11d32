"""
Phase 3: Analysis & Visualization

Learning Goals:
- Master Flux query language for data analysis
- Implement data retrieval patterns from the real project
- Create meaningful visualizations of manufacturing data
- Develop analytical thinking for manufacturing insights

This phase transforms raw sensor data into actionable manufacturing insights.
"""

import yaml
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
from influxdb_client import InfluxDBClient
from influxdb_client.client.write_api import SYNCHRONOUS

class ManufacturingDataAnalyzer:
    """
    Analyze manufacturing data using patterns from the real project.
    
    Learning Goal: Transform raw sensor data into actionable insights
    Real Project Pattern: Similar to analysis in InfluxDB/pull_data.py and compare_m290.py
    """
    
    def __init__(self, config):
        """
        Initialize the data analyzer.
        
        Task 1: Set up InfluxDB client for reading data
        """
        self.config = config
        
        # TODO: Initialize InfluxDB client from config
        self.env_choice = 'development'
        self.env_config = config[self.env_choice]
        self.influxdb_config = self.env_config['influxdb']

        self.influxdb_url = self.influxdb_config['url']
        self.influxdb_token = self.influxdb_config['token']
        self.influxdb_org = self.influxdb_config['org']
        self.influxdb_bucket = self.influxdb_config['bucket']

        try:
            self.influxdb_client = InfluxDBClient(
                url=self.influxdb_url,
                token=self.influxdb_token,
                org=self.influxdb_org
            )
            self.influxdb_query_api = self.influxdb_client.query_api()
        except Exception as e:
            print(f"Error type: {type(e)}\tError message: {e}")
        
        print("Manufacturing Data Analyzer initialized")
    
    def analyze_machine_performance(self, machine_id: str, time_range_hours: int=24) -> dict:
        """
        Analyze overall machine performance over time.
        
        Real Project Pattern: Similar to analysis in InfluxDB/pull_data.py
        
        Args:
            machine_id (str): ID of machine to analyze (e.g., "EOS_SI3654")
            time_range_hours (int): How many hours of data to analyze
            
        Returns:
            dict: Analysis results with performance metrics
            
        TODO: Write Flux query to get machine data and calculate performance metrics
        """
        print(f"Analyzing performance for {machine_id} over last {time_range_hours} hours")
        
        # Task 2a: Write Flux query to get comprehensive machine data
        # Consider: What metrics indicate good machine performance?
        # - Temperature stability (low standard deviation)
        # - Power consumption patterns (consistent vs erratic)
        # - Sensor data quality (percentage of valid readings)
        # - Operating time vs downtime
        
        query = f"""
        // Task 2a: Write your Flux query here
        from(bucket: "{self.config['development']['influxdb']['bucket']}")
          |> range(start: -{time_range_hours}h)
          |> filter(fn: (r) => r["machine_id"] == "{machine_id}")
        """
        
        # Task 2b: Execute query and process results
        dataframe = self._execute_flux_query(query)
        if not dataframe or dataframe.empty:
            print("analyze_machine_performance failed because there is no data to analyze.")
            return None

        # Task 2c: Calculate performance metrics
        temperature_df = dataframe[dataframe['_field'] == 'temperature_celsius']
        if not temperature_df.empty:
            temperature_std = temperature_df['_value'].std()

        power_df = dataframe[dataframe['_field'] == 'power_kw']
        if not power_df.empty:
            power_mean = power_df['_value'].mean()

        power_config = None
        for sensor_field in self.env_config['sensors'].values():
            if sensor_field['field_name'] == "power_kw":
                power_config = sensor_field
                break
        if power_config:
            normal_min_power, normal_max_power = power_config['normal_range']
            normal_power_df = power_df['_value'].between(normal_min_power, normal_max_power) # returns boolean series
            total_reading_count = len(power_df)
            valid_reading_count = normal_power_df.sum()
            operating_efficiency = (valid_reading_count / total_reading_count) * 100
        else:
            operating_efficiency = None



        # Task 2d: Return analysis results
        performance_metrics = {
            'machine_id': machine_id,
            'analysis_period_hours': time_range_hours,
            'temperature_stability': temperature_std,  # Task 2c: Calculate temperature std dev
            'average_power_consumption': power_mean,  # Task 2c: Calculate from power data
            'data_quality_score': operating_efficiency,  # Task 2c: Percentage of valid readings
            'anomaly_count': None,  # Task 2c: Number of unusual readings
        }
        
        return performance_metrics
    
    def _plot_sub_axes(self, sub_axes, plot_title, ylabel, field_name):
        sub_axes.set_title(plot_title)
        sub_axes.set_xlabel('Time')
        sub_axes.set_ylabel(ylabel)

        time_range_hours = 24

        query = f"""
            from(bucket: "{self.influxdb_bucket}")
            |> range(start: -{time_range_hours}h)
            |> filter(fn: (r) => r["_field"] == "{field_name}")
            |> aggregateWindow(every: 10s, fn: mean, createEmpty: false)
        """
        dataframe = self._execute_flux_query(query)

        if dataframe is not None and not dataframe.empty:
            # Group the data by machine so we can plot one line per machine.
            grouped_by_machine = dataframe.groupby('machine_id')
            
            # Loop through each machine's data and plot it.
            for machine_name, machine_df in grouped_by_machine:
                sub_axes.plot(machine_df['_time'], machine_df['_value'], label=machine_name)
            
            # Add a legend to identify which line belongs to which machine.
            sub_axes.legend()
            sub_axes.grid(True)
        else:
            # If no data, display a message on the plot.
            sub_axes.text(0.5, 0.5, f'No {field_name} data available', 
                            horizontalalignment='center', 
                            verticalalignment='center', 
                            transform=sub_axes.transAxes)

    def create_production_dashboard(self):   
        # In Summary
        # Prepare the overall picture: plt.subplots()
        # Focus on one section: axes[row, col]
        # Label that section: .set_title(), .set_xlabel(), .set_ylabel()
        # Draw the data: .plot()
        # Explain the lines: .legend()
        # Make it readable: .grid()
        # Show everything nicely: plt.tight_layout() and plt.show()
        """
        Creates and displays a 2x2 dashboard of manufacturing analytics.
        Focus is on the first panel: Temperature Trends.
        """
        print("Creating production monitoring dashboard...")

        # --- Step 1: Create the Dashboard Layout ---
        # We create a figure (the window) and a 2x2 grid of subplots (the axes).
        fig, axes = plt.subplots(2, 2, figsize=(16, 10))
        fig.suptitle('Manufacturing Monitoring Dashboard', fontsize=12)

        self._plot_sub_axes(axes[0, 0], 'Temperature Trends (Last 24h)', 'Temperature (°C)', 'temperature_celsius')
        self._plot_sub_axes(axes[0, 1], 'Power Consumption Trends (Last 24h)', 'Power (KW)', 'power_kw')
        self._plot_sub_axes(axes[1, 0], 'Pressure Trends (Last 24h)', 'Pressure (bar)', 'pressure_bar')

        axes[1, 1].set_title('Anomaly Detection')

        # --- Final Step: Display the Dashboard ---
        plt.tight_layout()  # Adjusts spacing to prevent labels from overlapping
        plt.show()
    
    def generate_insights_report(self):
        """
        Generate a comprehensive analysis report.
        
        Learning Goal: Transform data analysis into actionable insights
        
        TODO: Combine all analysis results into a meaningful report
        """
        print("=== MANUFACTURING INSIGHTS REPORT ===")
        
        # Task 5a: Analyze all machines
        # Task 5b: Detect system-wide patterns
        # Task 5c: Generate recommendations
        # Task 5d: Create executive summary
        
        # Example insights to generate:
        insights = {
            'report_timestamp': datetime.now(timezone.utc),
            'machines_analyzed': [],
            'overall_system_health': None,
            'performance_trends': [],
            'anomalies_detected': [],
            'recommendations': [],
            'data_quality_summary': {}
        }
        
        # Task 5e: Populate insights with actual analysis results
        
        return insights
    
    def _execute_flux_query(self, query: str) -> pd.DataFrame | None:
        """
        Execute a Flux query and return results.
        
        TODO: Implement query execution with error handling
        
        Args:
            query (str): Flux query to execute
            
        Returns:
            pandas.DataFrame or list: Query results
        """
        # Task 6a: Execute query using query_api
        # Task 6b: Handle connection errors
        # Task 6c: Convert results to useful format (DataFrame or list)
        # Task 6d: Return processed results
        try:
            dataframe = self.influxdb_query_api.query_data_frame(query, self.influxdb_org)
            if dataframe.empty:
                print("!!!!! Dataframe is empty !!!!!")
                return None
        except Exception as e:
            print(f"Error type: {type(e)}\tError message: {e}")

        return dataframe
    
    def _calculate_data_quality_score(self, sensor_data):
        """
        Calculate data quality score for sensor readings.
        
        TODO: Implement data quality assessment
        # Task 1a: Initialize InfluxDB client from config
        Args:
            sensor_data: Raw sensor readings
        # Task 1b: Create query_api for data retrieval
        Returns:
            float: Quality score between 0 and 1
        """
        # Task 6e: Calculate quality metrics:
        # - Percentage of non-null readings
        # - Percentage of readings within expected ranges
        # - Consistency of reading intervals
        # - Detection of obvious errors or outliers
        pass

def main():
    """Phase 3: Turn data into insights"""
    print("=== PHASE 3: ANALYSIS & VISUALIZATION ===")
    
    # Load configuration
    try:
        with open("config.yaml") as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print("Error: config.yaml not found. Please run Phase 2 first.")
        return
    
    analyzer = ManufacturingDataAnalyzer(config)
    
    print("Starting manufacturing data analysis...")
    
    # Task 7: Implement analysis workflow
    # 7a. Analyze machine performance for each configured machine
    # 7b. Detect anomalies across all sensors
    # 7c. Create visualizations
    # 7d. Generate insights report
    
    try:
        # Task 7e: Get list of machines from config or database
        machines = []  # Task 7e: Extract from config
        
        # Task 7f: Analyze each machine
        for machine_id in machines:
            performance = analyzer.analyze_machine_performance(machine_id)
            print(f"Performance analysis for {machine_id}: {performance}")
        
        # Task 7h: Create dashboard
        analyzer.create_production_dashboard()
        
        # # Task 7i: Generate comprehensive report
        # insights = analyzer.generate_insights_report()
        # print("Analysis complete!")
        
    except Exception as e:
        print(f"Analysis error: {e}")
        print("Make sure you have data from Phase 2 before running analysis")

if __name__ == "__main__":
    main()
