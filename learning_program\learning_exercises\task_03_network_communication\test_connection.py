#!/usr/bin/env python3
"""
Simple test to verify Modbus connection issue
"""

import time
import threading
from modbus_simulator import PowerMeterSimulator
from pyModbusTCP.client import ModbusClient

def test_simulator_and_client():
    """Test simulator and client together"""
    print("=== TESTING SIMULATOR AND CLIENT ===")
    
    # Start simulator
    print("1. Starting simulator...")
    sim = PowerMeterSimulator(port=5020, device_name="TestMeter")
    
    if sim.start():
        print("✅ Simulator started successfully")
        
        # Wait for data generation
        print("2. Waiting for data generation...")
        time.sleep(2)
        
        # Test client connection
        print("3. Testing client connection...")
        client = ModbusClient(host="127.0.0.1", port=5020, unit_id=1, timeout=5.0)
        
        try:
            if client.open():
                print("✅ Client connected successfully")
                
                # Read some registers
                print("4. Reading registers...")
                response = client.read_holding_registers(0x0000, 2)
                print(f"Register 0x0000-0x0001: {response}")
                
                response = client.read_holding_registers(0x0006, 2)
                print(f"Register 0x0006-0x0007: {response}")
                
                client.close()
                print("✅ Test completed successfully")
            else:
                print("❌ Client failed to connect")
        except Exception as e:
            print(f"❌ Client error: {e}")
        finally:
            if client.is_open:
                client.close()
        
        # Stop simulator
        print("5. Stopping simulator...")
        sim.stop()
        print("✅ Simulator stopped")
    else:
        print("❌ Failed to start simulator")

if __name__ == "__main__":
    test_simulator_and_client()
