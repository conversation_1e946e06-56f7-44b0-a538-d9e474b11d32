# 🌐 Task 3: Network Communication & Modbus Practice

## **Your Mission**
Learn network communication fundamentals and implement a Modbus client to read data from simulated power meters. This task is structured in **3 progressive phases** that build upon each other, making it easier to master both networking basics and industrial protocols.

---

## **What You'll Learn**

### **Phase 1: Network Fundamentals** 🔌
✅ **TCP/IP basics** - IP addresses, ports, and how machines communicate
✅ **Socket programming** - Low-level network connectivity testing
✅ **Network troubleshooting** - Testing reachability and handling timeouts

### **Phase 2: Modbus Protocol** ⚡
✅ **Industrial communication** - Understanding Modbus TCP protocol
✅ **Register addressing** - How industrial devices organize data
✅ **Data conversion** - Converting raw bytes to engineering values

### **Phase 3: Integration & Threading** 🚀
✅ **Multi-device monitoring** - Applying threading from Task 2
✅ **Error handling** - Dealing with network failures gracefully
✅ **Real-world simulation** - Complete industrial monitoring system

---

## **Background: Why Network Communication?**

In manufacturing environments:
- **Machines are networked** - Power meters, sensors, controllers all communicate over Ethernet
- **Modbus is everywhere** - Most power meters, PLCs, and industrial devices speak Modbus
- **Data comes as bytes** - Raw network data needs conversion to useful values
- **Networks fail** - Industrial systems must handle connection issues gracefully

**Real Project Connection:**
```python
# From the actual power meter project:
client = ModbusClient(host="*************", port=502)
raw_data = client.read_input_registers(reg_addr=0x0000, reg_nb=2)
voltage = decode_float(raw_data)  # Convert bytes to actual voltage
```

---

## **📋 Learning Path Overview**

**Recommended Timeline:**
- **Phase 1** (Days 1-2): Master network basics with simple socket programming
- **Phase 2** (Days 3-4): Learn Modbus protocol and data conversion
- **Phase 3** (Days 5-6): Integrate everything with threading and error handling

**Prerequisites:**
- Completed Task 1 (Logging) and Task 2 (Threading)
- Basic understanding of IP addresses and network concepts

---

## **🔌 PHASE 1: Network Fundamentals**

### **Learning Objectives**
- Understand IP addresses and port numbers
- Test network connectivity using Python sockets
- Handle network timeouts and connection errors
- Build foundation for industrial communication

### **Phase 1 Setup**

### **Step 1: Copy Required Files**
1. Copy `lib_loggers.py` from task_01 into this folder
2. This ensures consistent logging across all tasks

### **Step 2: Install Required Dependencies**
Create `requirements.txt`:
```
pyModbusTCP==0.2.1
PyYAML>=6.0
```

Install with: `pip install -r requirements.txt`

### **Step 3: Create Simple Network Test Configuration**
Create `network_config.yaml` for Phase 1 testing:

```yaml
development:
  test_targets:
    local_test:
      host: "127.0.0.1"
      port: 8080
      description: "Local test server"
    google_dns:
      host: "*******"
      port: 53
      description: "Google DNS server"
    local_modbus_sim:
      host: "127.0.0.1"
      port: 5020
      description: "Future Modbus simulator"
  network:
    default_timeout: 3.0
    retry_attempts: 3
```

### **Phase 1 Tasks**

#### **Task 1.1: Basic Socket Programming**
Create `phase1_network_basics.py`:

```python
import socket
import time
import yaml
from lib_loggers import set_logger

# Load configuration
try:
    with open("network_config.yaml") as config_file:
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The network_config.yaml file should be present.')

logger = set_logger()

def test_tcp_connection(host, port, timeout=3):
    """Test if a TCP connection can be established to host:port"""
    # TODO: Implement basic socket connection test
    # Steps:
    # 1. Create a socket using socket.socket()
    # 2. Set timeout using socket.settimeout()
    # 3. Try to connect using socket.connect()
    # 4. Return True if successful, False if failed
    # 5. Handle exceptions gracefully

    logger.info(f"Testing connection to {host}:{port}")

    try:
        # Your code here
        pass
    except socket.timeout:
        logger.warning(f"Connection to {host}:{port} timed out after {timeout}s")
        return False
    except ConnectionRefusedError:
        logger.warning(f"Connection to {host}:{port} refused (service not running)")
        return False
    except Exception as e:
        logger.error(f"Connection to {host}:{port} failed: {e}")
        return False

def scan_network_range(base_ip, start_port, end_port, timeout=1):
    """Scan a range of ports on a single IP address"""
    # TODO: Implement port scanning
    # This teaches how networks and services work
    # Steps:
    # 1. Loop through port range
    # 2. Test each port using test_tcp_connection()
    # 3. Log which ports are open
    # 4. Return list of open ports

    logger.info(f"Scanning {base_ip} ports {start_port}-{end_port}")
    open_ports = []

    # Your code here

    logger.info(f"Scan complete. Found {len(open_ports)} open ports: {open_ports}")
    return open_ports

def test_network_targets():
    """Test connectivity to all configured targets"""
    config_env = config['development']
    targets = config_env['test_targets']
    timeout = config_env['network']['default_timeout']

    logger.info("=== NETWORK CONNECTIVITY TEST ===")

    results = {}
    for target_name, target_config in targets.items():
        host = target_config['host']
        port = target_config['port']
        description = target_config['description']

        logger.info(f"Testing {target_name}: {description}")

        # TODO: Test each target and store results
        # Your code here

    # TODO: Generate summary report
    # Your code here

    return results

def main():
    """Phase 1: Network Fundamentals Practice"""
    logger.info("=== PHASE 1: NETWORK FUNDAMENTALS ===")

    # Test 1: Basic connectivity testing
    logger.info("Test 1: Basic connectivity testing")
    test_network_targets()

    # Test 2: Port scanning (educational)
    logger.info("Test 2: Port scanning demonstration")
    # Scan common ports on localhost
    scan_network_range("127.0.0.1", 20, 100, timeout=0.5)

    logger.info("Phase 1 complete! Ready for Phase 2 (Modbus Protocol)")

if __name__ == "__main__":
    main()
```

#### **Task 1.2: Understanding Network Concepts**

**Your Learning Tasks:**
1. **Complete `test_tcp_connection()`** - Learn socket programming basics
2. **Complete `scan_network_range()`** - Understand how services listen on ports
3. **Complete `test_network_targets()`** - Practice with real network testing
4. **Experiment with different targets** - Add your own test targets to config

**Expected Output:**
```
2025-07-31 16:00:15    phase1_network_basics    12345    INFO     === PHASE 1: NETWORK FUNDAMENTALS ===
2025-07-31 16:00:15    phase1_network_basics    12345    INFO     Testing local_test: Local test server
2025-07-31 16:00:15    phase1_network_basics    12345    INFO     Testing connection to 127.0.0.1:8080
2025-07-31 16:00:18    phase1_network_basics    12345    WARNING  Connection to 127.0.0.1:8080 refused (service not running)
2025-07-31 16:00:18    phase1_network_basics    12345    INFO     Testing google_dns: Google DNS server
2025-07-31 16:00:18    phase1_network_basics    12345    INFO     Testing connection to *******:53
2025-07-31 16:00:18    phase1_network_basics    12345    INFO     ✅ Connection to *******:53 successful
```

---

## **⚡ PHASE 2: Modbus Protocol**

### **Learning Objectives**
- Understand Modbus TCP protocol fundamentals
- Learn register addressing and data organization
- Master IEEE 754 float conversion (critical for real project)
- Read data from simulated industrial devices

### **Phase 2 Setup**

Create `modbus_config.yaml` for Modbus-specific configuration:

```yaml
development:
  power_meters:
    PowerMeter_A:
      ip_address: "127.0.0.1"  # Localhost for simulation
      port: 5020
      device_id: 1
      registers:
        voltage_l1: 0x0000    # Register address for L1 voltage
        voltage_l2: 0x0002    # Register address for L2 voltage
        voltage_l3: 0x0004    # Register address for L3 voltage
        current_l1: 0x0006    # Register address for L1 current
        power_total: 0x0034   # Register address for total power
  modbus:
    timeout: 3.0
    unit_id: 1
    register_count: 2  # IEEE 754 floats use 2 registers

production:
  power_meters:
    PowerMeter_A:
      ip_address: "*************"
      port: 502
      device_id: 1
      registers:
        voltage_l1: 0x0000
        voltage_l2: 0x0002
        voltage_l3: 0x0004
        current_l1: 0x0006
        power_total: 0x0034
  modbus:
    timeout: 5.0
    unit_id: 1
    register_count: 2
```

#### **Task 2.1: Understanding Modbus Basics**

**Before coding, understand these concepts:**

**What is Modbus?**
- **Industrial communication protocol** used by power meters, PLCs, sensors
- **Register-based** - devices organize data in numbered "registers" (like memory addresses)
- **Different data types** - 16-bit integers, 32-bit floats, boolean values
- **Function codes** - different ways to read/write data (we'll use "read input registers")

**Key Modbus Concepts:**
```
Register Address    Data Type       Meaning
0x0000-0x0001      IEEE 754 Float  L1 Voltage (230.5V)
0x0002-0x0003      IEEE 754 Float  L2 Voltage (231.2V)
0x0004-0x0005      IEEE 754 Float  L3 Voltage (229.8V)
0x0006-0x0007      IEEE 754 Float  L1 Current (12.3A)
0x0034-0x0035      IEEE 754 Float  Total Power (8.45kW)
```

**Why 2 registers per value?**
- Each register = 16 bits
- IEEE 754 float = 32 bits
- Therefore: 1 float = 2 registers

#### **Task 2.2: Data Conversion Practice**

Create `phase2_data_conversion.py`:

```python
import struct
from lib_loggers import set_logger

logger = set_logger()

def float_to_registers(float_value):
    """Convert a float to two Modbus registers (like the simulator does)"""
    # TODO: Implement float to register conversion
    # This helps you understand the data format
    # Steps:
    # 1. Use struct.pack('>f', float_value) to get 4 bytes
    # 2. Split into two 16-bit words using struct.unpack('>HH', bytes)
    # 3. Return as list [high_word, low_word]

    logger.debug(f"Converting float {float_value} to registers")

    # Your code here
    pass

def registers_to_float(register_pair):
    """Convert two Modbus registers back to float (like your client needs)"""
    # TODO: Implement register to float conversion
    # This is what you'll use to read power meter data
    # Steps:
    # 1. Combine registers: combined = (reg[0] << 16) | reg[1]
    # 2. Pack as unsigned int: struct.pack('>I', combined)
    # 3. Unpack as float: struct.unpack('>f', packed)[0]

    if len(register_pair) != 2:
        raise ValueError("Expected exactly 2 registers for float conversion")

    logger.debug(f"Converting registers {register_pair} to float")

    # Your code here
    pass

def test_conversion_accuracy():
    """Test that conversion is accurate (round-trip test)"""
    logger.info("=== TESTING DATA CONVERSION ACCURACY ===")

    test_values = [230.5, 12.3, 8.45, 0.0, -15.7, 999.99]

    for original_value in test_values:
        # TODO: Test round-trip conversion
        # 1. Convert float to registers
        # 2. Convert registers back to float
        # 3. Check if result matches original (within small tolerance)
        # 4. Log results

        logger.info(f"Testing value: {original_value}")
        # Your code here

def main():
    """Phase 2: Data Conversion Practice"""
    logger.info("=== PHASE 2: DATA CONVERSION PRACTICE ===")

    # Test conversion functions
    test_conversion_accuracy()

    logger.info("Phase 2 complete! Ready for Phase 3 (Modbus Communication)")

if __name__ == "__main__":
    main()
```

#### **Task 2.3: Simple Modbus Client**

Create `phase2_modbus_client.py`:

```python
import yaml
from pyModbusTCP.client import ModbusClient
from lib_loggers import set_logger

# Load configuration
try:
    with open("modbus_config.yaml") as config_file:
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The modbus_config.yaml file should be present.')

logger = set_logger()

def read_single_register_pair(client, register_address):
    """Read 2 consecutive registers (for one IEEE 754 float)"""
    # TODO: Implement basic Modbus reading
    # Steps:
    # 1. Use client.read_input_registers(register_address, 2)
    # 2. Check if response is valid
    # 3. Return the register values as list
    # 4. Handle errors gracefully

    logger.debug(f"Reading registers starting at 0x{register_address:04X}")

    try:
        # Your code here
        pass
    except Exception as e:
        logger.error(f"Failed to read registers at 0x{register_address:04X}: {e}")
        return None

def test_modbus_connection():
    """Test basic Modbus connectivity (requires simulator running)"""
    logger.info("=== TESTING MODBUS CONNECTION ===")

    config_env = config['development']
    meter_config = config_env['power_meters']['PowerMeter_A']

    # TODO: Create Modbus client and test connection
    # Steps:
    # 1. Create ModbusClient with IP and port from config
    # 2. Test connection using client.open()
    # 3. Try reading one register pair
    # 4. Close connection properly

    ip_address = meter_config['ip_address']
    port = meter_config['port']

    logger.info(f"Connecting to Modbus device at {ip_address}:{port}")

    # Your code here

def main():
    """Phase 2: Basic Modbus Communication"""
    logger.info("=== PHASE 2: BASIC MODBUS COMMUNICATION ===")

    logger.info("Note: Make sure to run 'python modbus_simulator.py' first!")

    # Test basic Modbus connection
    test_modbus_connection()

    logger.info("Phase 2 complete! Ready for Phase 3 (Integration)")

if __name__ == "__main__":
    main()
```

**Expected Phase 2 Output:**
```
2025-07-31 16:30:15    phase2_modbus_client    12345    INFO     === PHASE 2: BASIC MODBUS COMMUNICATION ===
2025-07-31 16:30:15    phase2_modbus_client    12345    INFO     Connecting to Modbus device at 127.0.0.1:5020
2025-07-31 16:30:15    phase2_modbus_client    12345    INFO     ✅ Modbus connection successful
2025-07-31 16:30:15    phase2_modbus_client    12345    DEBUG    Reading registers starting at 0x0000
2025-07-31 16:30:15    phase2_modbus_client    12345    INFO     Raw registers: [17126, 15616] → Voltage: 231.2V
```

---

## **🚀 PHASE 3: Integration & Threading**

### **Learning Objectives**
- Combine network skills with Modbus protocol
- Apply threading patterns from Task 2 to network devices
- Build complete multi-device monitoring system
- Handle real-world network failures and recovery

### **Phase 3 Tasks**

Now you'll combine everything into a complete monitoring system using the provided simulator.

#### **Task 3.1: Complete Power Meter Client**

Use the existing `power_meter_client.py` template and complete all TODO sections:

```python
# This file combines Phase 1 (networking) + Phase 2 (Modbus) + Task 2 (threading)

def check_network_connectivity(host, port, timeout=3):
    """Use your Phase 1 socket programming skills"""
    # TODO: Implement using knowledge from phase1_network_basics.py

def decode_float_from_registers(register_pair):
    """Use your Phase 2 data conversion skills"""
    # TODO: Implement using knowledge from phase2_data_conversion.py

def read_power_meter_data(meter_name, meter_config):
    """Use your Phase 2 Modbus skills"""
    # TODO: Implement using knowledge from phase2_modbus_client.py

def monitor_single_meter(meter_name, meter_config, duration):
    """Continuous monitoring with error handling"""
    # TODO: Combine networking + Modbus + error handling

def main():
    """Use your Task 2 threading skills"""
    # TODO: Create threads for each power meter (like Task 2)
```

#### **Task 3.2: Multi-Device Threading**

Apply threading patterns from Task 2:
- Create one thread per power meter
- Start all threads simultaneously
- Handle graceful shutdown
- Generate comprehensive reports

#### **Task 3.3: Error Handling & Recovery**

Add robust error handling:
- Network connection failures
- Modbus communication errors
- Device timeouts and retries
- Graceful degradation when devices fail

### **Phase 3 Setup - Modbus Simulator**

**To run the simulator:**
```bash
python modbus_simulator.py
```

**Expected simulator output:**
```
✅ PowerMeter_A started successfully
✅ PowerMeter_B started successfully

🔌 Modbus simulators running. Press Ctrl+C to stop.
📊 Connect your client to:
   - PowerMeter_A: 127.0.0.1:5020
   - PowerMeter_B: 127.0.0.1:5021
```

#### **Task 3.4: Final Integration Test**

**Testing Sequence:**
1. **Start simulator**: `python modbus_simulator.py`
2. **Test Phase 1**: `python phase1_network_basics.py`
3. **Test Phase 2**: `python phase2_modbus_client.py`
4. **Run final client**: `python power_meter_client.py`

**Expected Final Output:**
```
2025-07-31 16:45:15    power_meter_client    12345    INFO     Starting power meter monitoring system...
2025-07-31 16:45:15    power_meter_client    12345    INFO     Monitoring 2 power meters
2025-07-31 16:45:15    power_meter_client    12345    INFO     Starting monitoring for PowerMeter_A
2025-07-31 16:45:15    power_meter_client    12345    INFO     Starting monitoring for PowerMeter_B
2025-07-31 16:45:16    power_meter_client    12345    INFO     PowerMeter_A: L1=231.2V, L2=229.8V, L3=232.1V, Power=6.85kW
2025-07-31 16:45:16    power_meter_client    12345    INFO     PowerMeter_B: L1=230.5V, Power=5.42kW
2025-07-31 16:45:18    power_meter_client    12345    INFO     PowerMeter_A: L1=230.9V, L2=231.3V, L3=229.7V, Power=6.91kW
```

---

## **📋 Phase-by-Phase Success Criteria**

### **✅ Phase 1 Success (Network Fundamentals)**
- Successfully test TCP connections to various hosts/ports
- Understand socket programming basics
- Handle network timeouts and connection errors
- Complete port scanning demonstration

### **✅ Phase 2 Success (Modbus Protocol)**
- Convert floats to/from Modbus register format accurately
- Successfully connect to Modbus simulator
- Read register data and convert to engineering units
- Understand IEEE 754 float encoding

### **✅ Phase 3 Success (Integration)**
- Multi-meter monitoring with threading (like Task 2)
- Robust error handling and reconnection logic
- Real-time data logging with proper formatting
- Graceful shutdown and comprehensive reporting

---

## **🎯 Learning Progression Summary**

**Phase 1 → Phase 2 → Phase 3** builds your skills incrementally:

1. **Phase 1**: Master basic networking (sockets, connectivity, timeouts)
2. **Phase 2**: Learn industrial protocols (Modbus, data conversion, registers)
3. **Phase 3**: Integrate everything (threading, error handling, real-world patterns)

Each phase can be completed independently, making it easier to:
- **Debug issues** - isolate problems to specific phases
- **Learn incrementally** - master one concept before moving to next
- **Get help** - ask specific questions about individual phases
- **Track progress** - clear milestones for each phase

---

## **🔗 Real-World Connection**

This phased approach directly mirrors how you'd approach the actual project:

**Phase 1 Skills** → Used for network troubleshooting and connectivity testing
**Phase 2 Skills** → Used for power meter communication and data processing
**Phase 3 Skills** → Used for complete monitoring system implementation

**Actual Project Patterns You'll Recognize:**
```
2025-07-31 15:30:15    power_meter_client    12345    INFO     Starting power meter monitoring system...
2025-07-31 15:30:15    power_meter_client    12345    INFO     Monitoring 2 power meters
2025-07-31 15:30:15    power_meter_client    12345    INFO     Connecting to PowerMeter_A at 127.0.0.1:5020
2025-07-31 15:30:15    power_meter_client    12345    INFO     Connecting to PowerMeter_B at 127.0.0.1:5021
2025-07-31 15:30:16    power_meter_client    12345    INFO     PowerMeter_A: L1=231.2V, L2=229.8V, L3=232.1V, Power=6.85kW
2025-07-31 15:30:16    power_meter_client    12345    INFO     PowerMeter_B: L1=230.5V, Power=5.42kW
2025-07-31 15:30:18    power_meter_client    12345    INFO     PowerMeter_A: L1=230.9V, L2=231.3V, L3=229.7V, Power=6.91kW
2025-07-31 15:30:18    power_meter_client    12345    INFO     PowerMeter_B: L1=231.1V, Power=5.38kW
```

**Testing Steps:**
1. Run `python modbus_simulator.py` in one terminal
2. Run `python power_meter_client.py` in another terminal
3. Observe real-time power meter data being read and logged
4. Test network failure by stopping simulator mid-run
5. Verify graceful error handling and reconnection

---

## **Bonus Challenges** 🏆

### **Challenge 1: Data Validation and Quality Control**

#### **The Business Problem: Bad Data Causes Production Issues**

**Real-World Scenario:**
```
Quality Engineer: "The parts from yesterday's batch failed testing. 
                  Was there a power fluctuation during production?"
Current System: "The power meter showed 245V, but that seems wrong..."
Improved System: "ALERT: PowerMeter_A voltage reading 245V exceeds normal 
                 range (220-240V). Flagged as potentially invalid."
```

**Implementation:**
Add data validation to your monitoring:

```python
def validate_power_data(meter_name, measurement_type, value):
    """Validate power meter readings for realistic ranges"""
    validation_ranges = {
        'voltage': (200, 250),    # Volts
        'current': (0, 100),      # Amps  
        'power': (0, 50),         # kW
    }
    
    # TODO: Implement validation logic
    # 1. Check if value is within expected range
    # 2. Check for sudden large changes (> 20% from previous reading)
    # 3. Flag suspicious readings for review
    # 4. Log validation warnings
    pass

def calculate_power_quality_metrics(readings_history):
    """Calculate power quality indicators"""
    # TODO: Implement power quality calculations
    # 1. Voltage stability (standard deviation)
    # 2. Load factor (average vs peak power)
    # 3. Power factor estimation
    # 4. Frequency of out-of-range readings
    pass
```

### **Challenge 2: Historical Data Storage**

#### **The Business Problem: Need Trend Analysis**

**Real-World Need:**
- "Show me power consumption trends over the last week"
- "Which machine uses the most power during night shift?"
- "Are there any patterns in power failures?"

**Implementation:**
Add simple CSV data logging:

```python
import csv
from datetime import datetime

def log_to_csv(meter_name, readings):
    """Store readings in CSV for historical analysis"""
    filename = f"power_data_{meter_name}_{datetime.now().strftime('%Y%m%d')}.csv"
    
    # TODO: Implement CSV logging
    # 1. Create CSV file with headers if it doesn't exist
    # 2. Append new readings with timestamps
    # 3. Include data quality flags
    # 4. Rotate files daily
    pass

def generate_daily_report(meter_name):
    """Generate summary report from CSV data"""
    # TODO: Implement report generation
    # 1. Read today's CSV file
    # 2. Calculate min/max/average values
    # 3. Identify peak usage periods
    # 4. Count data quality issues
    pass
```

---

## **Real-World Connection**

This exercise directly prepares you for the actual project patterns:

**Power Meter Project Similarities:**
```python
# Your practice code:
client = ModbusClient(host="127.0.0.1", port=5020)
raw_data = client.read_input_registers(reg_addr=0x0000, reg_nb=2)
voltage = decode_float_from_registers(raw_data)

# Actual project code:
client = ModbusClient(host=IP_address, port=8899)
response = client.read_input_registers(reg_addr=reg_addr, reg_nb=read_count)
float_value = decode_for_float(response.registers)
```

**Key Skills You'll Master:**
- Industrial network protocols (Modbus TCP)
- Binary data conversion (IEEE 754 floats)
- Network error handling and recovery
- Multi-device monitoring with threading
- Real-time data validation and logging

---

## **Success Criteria**

✅ **Basic Implementation:**
- Successfully connect to simulated power meters
- Read and decode voltage/current/power values
- Handle network timeouts and errors gracefully
- Log data in engineering units (V, A, kW)

✅ **Advanced Implementation:**
- Multi-meter monitoring with threading
- Data validation and quality control
- Historical data storage (CSV)
- Comprehensive error handling and recovery

✅ **Professional Implementation:**
- Production-ready configuration management
- Robust reconnection logic
- Performance monitoring and statistics
- Clean separation of concerns (networking, data processing, logging)

---

## **Getting Help**

**Common Issues:**
- **"Connection refused"**: Make sure simulator is running first
- **"Invalid register data"**: Check your float conversion functions
- **"Timeout errors"**: Verify network connectivity and timeouts
- **"Threading issues"**: Review Task 2 patterns for thread management

**Testing Tips:**
- Start with single meter before adding threading
- Test conversion functions with known values
- Use network tools to verify simulator is listening
- Add debug logging to trace data flow

**Ready to master industrial networking? Good luck! 🚀**
