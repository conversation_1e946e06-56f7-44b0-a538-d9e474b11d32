"""
OPC UA Server Simulator for Learning

This module creates simulated OPC UA servers that behave like real industrial machines.
Used for learning OPC UA client development without needing real hardware.

Learning Goal: Understand both client and server sides of OPC UA communication
"""

import asyncio
import random
import math
from datetime import datetime, timezone
from asyncua import Server, ua
import logging

class OPCUAMachineSimulator:
    """
    Simulate an OPC UA server representing an industrial machine.
    
    Learning Goal: Understand OPC UA server structure and data organization
    """
    
    def __init__(self, machine_type, port, namespace_index=4):
        """
        Initialize OPC UA machine simulator.
        
        Args:
            machine_type (str): Type of machine to simulate
            port (int): Port number for OPC UA server
            namespace_index (int): OPC UA namespace index
        """
        self.machine_type = machine_type
        self.port = port
        self.namespace_index = namespace_index
        self.server = None
        self.nodes = {}
        self.running = False
        
        print(f"Initializing {machine_type} simulator on port {port}")
        
    async def start_server(self):
        """
        Start the OPC UA server with simulated machine data.
        
        TODO: Implement OPC UA server startup
        Learning Goal: Understand OPC UA server structure
        """
        # TODO: Create OPC UA server instance
        self.server = Server()
        await self.server.init()
        
        # TODO: Set server properties
        self.server.set_endpoint(f"opc.tcp://localhost:{self.port}")
        self.server.set_server_name(f"Simulated {self.machine_type}")
        
        # TODO: Set up namespace
        namespace_uri = f"http://simulated.{self.machine_type}.com"
        namespace_idx = await self.server.register_namespace(namespace_uri)
        
        # TODO: Create node structure based on machine type
        await self._create_machine_nodes(namespace_idx)
        
        # TODO: Start server
        await self.server.start()
        self.running = True
        
        print(f"✅ {self.machine_type} OPC UA server started on port {self.port}")
        
        # TODO: Start data simulation
        asyncio.create_task(self._simulate_machine_data())
        
    async def _create_machine_nodes(self, namespace_idx):
        """
        Create OPC UA nodes based on machine type.
        
        TODO: Implement machine-specific node structures
        Args:
            namespace_idx (int): Namespace index for nodes
            
        Learning Goal: Understand OPC UA node organization
        """
        # TODO: Get objects root
        objects = self.server.nodes.objects
        
        # TODO: Create machine root folder
        machine_folder = await objects.add_folder(namespace_idx, f"{self.machine_type}_Machine")
        
        if self.machine_type == "3d_printer":
            # TODO: Create 3D printer specific nodes
            await self._create_3d_printer_nodes(machine_folder, namespace_idx)
        elif self.machine_type == "cnc_machine":
            # TODO: Create CNC machine specific nodes
            await self._create_cnc_machine_nodes(machine_folder, namespace_idx)
        elif self.machine_type == "power_meter":
            # TODO: Create power meter specific nodes
            await self._create_power_meter_nodes(machine_folder, namespace_idx)
            
    async def _create_3d_printer_nodes(self, parent_folder, namespace_idx):
        """
        Create nodes specific to 3D printer simulation.
        
        TODO: Implement 3D printer node structure
        """
        # TODO: Create environment folder
        env_folder = await parent_folder.add_folder(namespace_idx, "Environment")
        
        # TODO: Create temperature node
        self.nodes['temperature'] = await env_folder.add_variable(
            namespace_idx, "Temperature", 245.0, ua.VariantType.Double
        )
        await self.nodes['temperature'].set_writable()
        
        # TODO: Create pressure node
        self.nodes['pressure'] = await env_folder.add_variable(
            namespace_idx, "Pressure", 2.3, ua.VariantType.Double
        )
        await self.nodes['pressure'].set_writable()
        
        # TODO: Create humidity node
        self.nodes['humidity'] = await env_folder.add_variable(
            namespace_idx, "Humidity", 45.2, ua.VariantType.Double
        )
        await self.nodes['humidity'].set_writable()
        
        # TODO: Create status folder and nodes
        status_folder = await parent_folder.add_folder(namespace_idx, "Status")
        self.nodes['status'] = await status_folder.add_variable(
            namespace_idx, "Current", "Running", ua.VariantType.String
        )
        await self.nodes['status'].set_writable()
        
        # TODO: Create process folder and nodes
        process_folder = await parent_folder.add_folder(namespace_idx, "Process")
        self.nodes['build_progress'] = await process_folder.add_variable(
            namespace_idx, "BuildProgress", 0.0, ua.VariantType.Double
        )
        await self.nodes['build_progress'].set_writable()
        
    async def _create_cnc_machine_nodes(self, parent_folder, namespace_idx):
        """
        Create nodes specific to CNC machine simulation.
        
        TODO: Implement CNC machine node structure
        """
        # TODO: Create spindle folder
        spindle_folder = await parent_folder.add_folder(namespace_idx, "spindle")
        self.nodes['spindle_speed'] = await spindle_folder.add_variable(
            namespace_idx, "SpindleSpeed", 1200.0, ua.VariantType.Double
        )
        await self.nodes['spindle_speed'].set_writable()

        # TODO: Create sensors folder
        sensors_folder = await parent_folder.add_folder(namespace_idx, "sensors")
        self.nodes['vibration'] = await sensors_folder.add_variable(
            namespace_idx, "Vibration", 0.1, ua.VariantType.Double
        )
        await self.nodes['vibration'].set_writable()

        # TODO: Create tools folder
        tools_folder = await parent_folder.add_folder(namespace_idx, "tools")
        self.nodes['tool_wear'] = await tools_folder.add_variable(
            namespace_idx, "ToolWear", 0.0, ua.VariantType.Double
        )
        await self.nodes['tool_wear'].set_writable()
        
        # TODO: Create status nodes
        status_folder = await parent_folder.add_folder(namespace_idx, "status")
        self.nodes['status'] = await status_folder.add_variable(
            namespace_idx, "Status", "Idle", ua.VariantType.String
        )
        await self.nodes['status'].set_writable()
        
        
    async def _create_power_meter_nodes(self, parent_folder, namespace_idx):
        """
        Create nodes specific to power meter simulation.
        """
        # For a simple device like a power meter, we can group all related
        # electrical readings into a single "Power" folder for clarity.
        power_folder = await parent_folder.add_folder(namespace_idx, "Power")

        # Group all voltage readings in a sub-folder
        voltage_folder = await power_folder.add_folder(namespace_idx, "Voltage")
        self.nodes['voltage_l1'] = await voltage_folder.add_variable(namespace_idx, "L1", 230.1, ua.VariantType.Double)
        self.nodes['voltage_l2'] = await voltage_folder.add_variable(namespace_idx, "L2", 230.5, ua.VariantType.Double)
        self.nodes['voltage_l3'] = await voltage_folder.add_variable(namespace_idx, "L3", 229.8, ua.VariantType.Double)

        # Group current readings
        current_folder = await power_folder.add_folder(namespace_idx, "Current")
        self.nodes['current_total'] = await current_folder.add_variable(namespace_idx, "Total", 15.5, ua.VariantType.Double)

        # Group active power readings
        active_power_folder = await power_folder.add_folder(namespace_idx, "Active")
        self.nodes['power_active'] = await active_power_folder.add_variable(namespace_idx, "Total", 3.5, ua.VariantType.Double) # Starting kW

        # A clean way to set all newly created nodes to writable for the simulation
        for node_name in ['voltage_l1', 'voltage_l2', 'voltage_l3', 'current_total', 'power_active']:
            await self.nodes[node_name].set_writable()
        
    async def _simulate_machine_data(self):
        """
        Continuously update machine data to simulate real behavior.
        
        TODO: Implement realistic data simulation
        Learning Goal: Understand how real machine data changes over time
        """
        print(f"Starting data simulation for {self.machine_type}")
        
        simulation_time = 0
        
        while self.running:
            try:
                # TODO: Update node values based on machine type
                if self.machine_type == "3d_printer":
                    await self._update_3d_printer_data(simulation_time)
                elif self.machine_type == "cnc_machine":
                    await self._update_cnc_machine_data(simulation_time)
                elif self.machine_type == "power_meter":
                    await self._update_power_meter_data(simulation_time)
                    
                simulation_time += 1
                await asyncio.sleep(1)  # Update every second
                
            except Exception as e:
                print(f"Error in data simulation: {e}")
                await asyncio.sleep(5)
                
    async def _update_3d_printer_data(self, time_step):
        """
        Update 3D printer data with realistic patterns.
        """
        # Temperature: Sine wave + noise
        base_temp = 245.0
        temp_variation = 5.0 * math.sin(time_step * 0.1) + random.uniform(-1, 1)
        new_temp = base_temp + temp_variation
        await self.nodes['temperature'].write_value(new_temp)

        # Pressure: Small random fluctuation
        pressure_variation = random.uniform(-0.05, 0.05)
        new_pressure = 2.3 + pressure_variation
        await self.nodes['pressure'].write_value(new_pressure)

        # Build progress: Increases until 100%
        current_progress = await self.nodes['build_progress'].read_value()
        if current_progress < 100.0:
            new_progress = min(100.0, current_progress + random.uniform(0.05, 0.1))
            await self.nodes['build_progress'].write_value(new_progress)
            await self.nodes['status'].write_value("Running")
        else:
            await self.nodes['status'].write_value("Finished")

    async def _update_cnc_machine_data(self, time_step):
        """
        Update CNC machine data with realistic patterns.
        """
        # Spindle speed: Occasional jumps, otherwise fluctuates
        current_speed = await self.nodes['spindle_speed'].read_value()
        if random.random() < 0.1:
            target_speed = random.uniform(500, 8000)
        else:
            target_speed = current_speed + random.uniform(-50, 50)
        new_speed = max(0, min(10000, target_speed))
        await self.nodes['spindle_speed'].write_value(new_speed)

        # Status: Running if speed > 10
        if new_speed > 10:
            await self.nodes['status'].write_value("Running")
        else:
            await self.nodes['status'].write_value("Idle")

        # Vibration: Proportional to speed + noise
        base_vibration = new_speed / 2000
        vibration = base_vibration + random.uniform(-0.1, 0.1)
        await self.nodes['vibration'].write_value(max(0, vibration))

        # Tool wear: Increases only when running
        current_status = await self.nodes['status'].read_value()
        if current_status == "Running":
            current_wear = await self.nodes['tool_wear'].read_value()
            if current_wear < 100.0:
                new_wear = min(100.0, current_wear + 0.01)
                await self.nodes['tool_wear'].write_value(new_wear)

    async def _update_power_meter_data(self, time_step):
        """
        Update power meter data with realistic patterns.
        """
        # Voltage: Stable with tiny fluctuations
        await self.nodes['voltage_l1'].write_value(230.0 + random.uniform(-0.2, 0.2))
        await self.nodes['voltage_l2'].write_value(230.0 + random.uniform(-0.2, 0.2))
        await self.nodes['voltage_l3'].write_value(230.0 + random.uniform(-0.2, 0.2))

        # Power/current: Daily cycle + noise
        day_cycle = math.sin((time_step % 86400) / 86400 * 2 * math.pi - (math.pi/2)) + 1
        base_power = 5 * day_cycle + random.uniform(0, 2)
        base_current = 15 * day_cycle + random.uniform(0, 5)

        await self.nodes['power_active'].write_value(max(0, base_power))
        await self.nodes['current_total'].write_value(max(0, base_current))
        
    async def stop_server(self):
        """
        Stop the OPC UA server gracefully.
        
        TODO: Implement clean server shutdown
        """
        print(f"Stopping {self.machine_type} OPC UA server...")
        self.running = False
        
        if self.server:
            await self.server.stop()
            
        print(f"✅ {self.machine_type} server stopped")

async def start_multiple_simulators(config):
    """
    Start multiple OPC UA simulators based on configuration.
    
    TODO: Implement multi-server startup
    Args:
        config (dict): Configuration for simulators
    """
    simulators = []
    
    # TODO: Create simulators based on config
    for server_config in config['development']['opcua_simulation']['servers']:
        simulator = OPCUAMachineSimulator(
            machine_type=server_config['machine_type'],
            port=server_config['port']
        )
        simulators.append(simulator)
        
    # TODO: Start all simulators
    for simulator in simulators:
        await simulator.start_server()
        
    print(f"\n🔌 All OPC UA simulators running. Press Ctrl+C to stop.")
    print("📊 Connect your client to:")
    for simulator in simulators:
        print(f"   - {simulator.machine_type}: opc.tcp://localhost:{simulator.port}")
        
    try:
        # TODO: Keep simulators running
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\nShutdown requested...")
    finally:
        # TODO: Stop all simulators
        for simulator in simulators:
            await simulator.stop_server()

async def main():
    """Start OPC UA simulators for learning"""
    print("=== OPC UA MACHINE SIMULATORS ===")
    
    # TODO: Load configuration
    import yaml
    try:
        with open("config.yaml") as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print("Error: config.yaml not found")
        return
        
    # TODO: Start simulators
    await start_multiple_simulators(config)

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.WARNING)
    
    # Run simulators
    asyncio.run(main())
