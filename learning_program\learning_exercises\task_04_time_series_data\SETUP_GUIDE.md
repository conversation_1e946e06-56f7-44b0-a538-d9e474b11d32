# 🚀 Task 4 Setup Guide

## Prerequisites

### 1. Completed Previous Tasks
- ✅ Task 1: Logging fundamentals
- ✅ Task 2: Threading concepts  
- ✅ Task 3: Network communication and Modbus

### 2. InfluxDB Installation

You'll need a running InfluxDB instance. Choose one option:

#### Option A: InfluxDB Cloud (Recommended for Learning)
1. Sign up for free at [InfluxDB Cloud](https://cloud2.influxdata.com/)
2. Create a new bucket called "manufacturing_sensors"
3. Generate an API token with read/write permissions
4. Note your organization ID and bucket name

#### Option B: Local InfluxDB Installation
1. Download InfluxDB 2.x from [InfluxData](https://portal.influxdata.com/downloads/)
2. Install and start InfluxDB
3. Access web UI at http://localhost:8086
4. Create initial user, organization, and bucket
5. Generate API token

### 3. Python Environment Setup

```bash
# Create virtual environment (recommended)
python -m venv task04_env
source task04_env/bin/activate  # On Windows: task04_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

## Configuration Setup

### 1. Update config.yaml

Replace the placeholder values in `config.yaml`:

```yaml
development:
  influxdb:
    url: "YOUR_INFLUXDB_URL"           # e.g., "https://us-west-2-1.aws.cloud2.influxdata.com"
    token: "YOUR_API_TOKEN"            # Your InfluxDB API token
    org: "YOUR_ORGANIZATION"           # Your organization name or ID
    bucket: "manufacturing_sensors"    # Your bucket name
```

### 2. Test Your Connection

Create a simple test script to verify your InfluxDB connection:

```python
from influxdb_client import InfluxDBClient
import yaml

with open("config.yaml") as f:
    config = yaml.safe_load(f)

influx_config = config['development']['influxdb']
client = InfluxDBClient(
    url=influx_config['url'],
    token=influx_config['token'],
    org=influx_config['org']
)

# Test connection
try:
    health = client.health()
    print(f"InfluxDB connection successful: {health.status}")
except Exception as e:
    print(f"Connection failed: {e}")
```

## Learning Path

### Phase 1: Fundamentals (2-3 hours)
**Goal**: Master time series concepts and data modeling

**Files to work on**:
- `phase1_time_series_basics.py`

**Key Learning Outcomes**:
- Understand measurements, tags, and fields
- Practice data point creation
- Learn data validation techniques
- Master basic write operations

**Success Criteria**:
- Can create properly structured data points
- Understand different data modeling approaches
- Can validate sensor data for quality
- Successfully write data to InfluxDB

### Phase 2: Data Collection (3-4 hours)
**Goal**: Build a complete sensor monitoring system

**Files to work on**:
- `sensor_simulator.py`
- `phase2_data_collection.py`

**Key Learning Outcomes**:
- Apply threading to data collection
- Handle sensor failures gracefully
- Implement batch data operations
- Build robust error handling

**Success Criteria**:
- Multi-threaded sensor monitoring works
- System handles sensor failures gracefully
- Data is stored efficiently in batches
- Comprehensive error handling and reporting

### Phase 3: Analysis & Visualization (2-3 hours)
**Goal**: Transform data into manufacturing insights

**Files to work on**:
- `phase3_analysis_visualization.py`

**Key Learning Outcomes**:
- Master Flux query language
- Create meaningful visualizations
- Detect anomalies in sensor data
- Generate actionable insights

**Success Criteria**:
- Can write effective Flux queries
- Creates professional dashboards
- Detects meaningful anomalies
- Generates useful insights reports

## Common Issues and Solutions

### InfluxDB Connection Issues
- **Error**: "Connection refused"
  - **Solution**: Check URL and ensure InfluxDB is running
- **Error**: "Unauthorized"
  - **Solution**: Verify API token has correct permissions
- **Error**: "Bucket not found"
  - **Solution**: Create bucket in InfluxDB UI or update config

### Python Environment Issues
- **Error**: "Module not found"
  - **Solution**: Ensure virtual environment is activated and dependencies installed
- **Error**: "Permission denied"
  - **Solution**: Check file permissions and virtual environment setup

### Data Quality Issues
- **Issue**: No data appearing in InfluxDB
  - **Check**: Verify write operations are successful
  - **Check**: Confirm bucket name and organization are correct
- **Issue**: Sensor simulation not realistic
  - **Solution**: Adjust sensor configuration parameters

## Testing Your Implementation

### Phase 1 Testing
```bash
python phase1_time_series_basics.py
```
Expected output: Successful data point creation and validation examples

### Phase 2 Testing
```bash
python phase2_data_collection.py
```
Expected output: Multi-threaded sensor monitoring with statistics

### Phase 3 Testing
```bash
python phase3_analysis_visualization.py
```
Expected output: Dashboard visualizations and analysis reports

## Getting Help

### Debugging Tips
1. **Start simple**: Test each phase independently
2. **Check logs**: Look for error messages and connection issues
3. **Verify data**: Use InfluxDB UI to confirm data is being written
4. **Test queries**: Use InfluxDB Data Explorer to test Flux queries

### Common Learning Challenges
1. **Understanding tags vs fields**: Practice with different data modeling scenarios
2. **Threading complexity**: Review Task 2 patterns and start with single-threaded version
3. **Flux query syntax**: Start with simple queries and build complexity gradually
4. **Data visualization**: Focus on meaningful metrics rather than complex charts

### Resources
- [InfluxDB Documentation](https://docs.influxdata.com/)
- [Flux Query Language Guide](https://docs.influxdata.com/influxdb/v2.0/query-data/flux/)
- [Python Threading Tutorial](https://realpython.com/intro-to-python-threading/)
- [Matplotlib Gallery](https://matplotlib.org/stable/gallery/index.html)

## Success Metrics

By the end of Task 4, you should be able to:
- ✅ Design appropriate time series data models
- ✅ Implement robust data collection systems
- ✅ Write effective Flux queries for analysis
- ✅ Create meaningful manufacturing dashboards
- ✅ Handle real-world data quality issues
- ✅ Apply threading concepts to data collection
- ✅ Generate actionable insights from sensor data

Ready to start? Begin with Phase 1! 🚀
