"""
Phase 2: OPC UA Client Development

Learning Goals:
- Master OPC UA protocol and client operations
- Implement async connections and data reading
- Build real-time subscription systems
- Handle OPC UA errors and connection recovery

This phase builds on async fundamentals to create industrial communication systems.
"""

import asyncio
import yaml
from datetime import datetime, timezone
from asyncua import Client, Node
from asyncua.common.subscription import DataChangeNotif
from asyncua.common.ua_utils import string_to_variant
from asyncua.ua import UaStatusCodeError
import logging

class OPCUAClientBasics:
    """
    Learn OPC UA client fundamentals with async patterns.
    
    Learning Goal: Master industrial communication protocols
    Real Project Pattern: Similar to EOS M290 OPCUA implementation
    """
    
    def __init__(self, config):
        """
        Initialize OPC UA client learning environment.
        
        TODO: Set up client configuration and connection parameters
        Learning Focus: Understanding async client setup and connection management
        """
        self.config = config
        self.client = None
        self.subscription = None
        self.data_handler = None
        
        print("=== OPC UA CLIENT LEARNING ENVIRONMENT ===")
        print("Goal: Master industrial OPC UA communication patterns\n")
        
    async def learn_basic_connections(self):
        """
        Master OPC UA connection patterns.
        
        Learning Goal: Understand async connection management
        Real Project Pattern: Connection handling from main.py
        """
        print("=== OPC UA CONNECTION FUNDAMENTALS ===")
        
        # TODO: Learn different connection patterns
        
        # Pattern 1: Basic connection
        print("1. Basic connection pattern:")
        await self._demonstrate_basic_connection()
        
        # Pattern 2: Authenticated connection
        print("\n2. Authenticated connection pattern:")
        await self._demonstrate_authenticated_connection()
        
        # Pattern 3: Connection with automatic retry
        print("\n3. Robust connection with retry:")
        await self._demonstrate_retry_connection()
        
    async def _demonstrate_basic_connection(self):
        """
        Demonstrate basic OPC UA connection.
        
        TODO: Implement basic async connection
        Learning Focus: Understanding OPC UA client lifecycle
        """
        server_url = "opc.tcp://localhost:4840/"
        
        try:
            print(f"  Connecting to {server_url}...")
            
            # TODO: Create client and connect
            async with Client(url=server_url) as client:
                print("Connection with OPC UA server successfully established!")
            
                # TODO: Test basic server information
                server_time_node = client.get_node("i=2258")
                server_time = await server_time_node.read_value()
                print(f"Current server time is {server_time}")
            
        except Exception as e:
            print(f"  ❌ Connection failed: {e}")
            
    async def _demonstrate_authenticated_connection(self):
        """
        Demonstrate authenticated OPC UA connection.
        
        TODO: Implement connection with username/password
        Learning Focus: Understanding OPC UA security
        """
        # TODO: Try connection with authentication
        server_url = "opc.tcp://localhost:4840/"
        username = "username"
        password = "password"
        
        try:
            print(f"  Attempting authenticated connection...")
            client = Client(url=server_url)
            client.set_user(username=username)
            client.set_password(pwd=password)
            async with client:
                print(f"Client has been initialized and connected to with:\nUsername: {username}\nPassword: {password}")
            
        except Exception as e:
            print(f"  ❌ Authentication failed: {e}")
            
    async def _demonstrate_retry_connection(self):
        """
        Demonstrate connection with retry logic.
        
        TODO: Implement connection retry logic
        Learning Focus: Robust connection handling for production
        """
        server_url = "opc.tcp://localhost:4840/"
        max_retries = 3
        retry_delay = 2
        for i in range(0, max_retries):
            try:
                if i == 1:
                    async with Client(url=server_url, timeout=1):
                        print("Repeated connection attempts successful!")
                        break
                else:
                    raise TimeoutError
            except TimeoutError as error:
                print("Timed out!")
                await asyncio.sleep(retry_delay)
        
        # TODO: Implement connection with timeout
                    
    async def explore_node_operations(self):
        """
        Learn different ways to read and interact with OPC UA nodes.
        
        Learning Goal: Master node operations and data access
        """
        print("\n=== NODE OPERATIONS ===")
        
        server_url = "opc.tcp://localhost:4840/"
        
        try:
            async with Client(url=server_url) as client:
                # TODO: Basic node reading
                print("1. Basic node reading:")
                await self._demonstrate_node_reading(client)
                
                # TODO: Node browsing and discovery
                print("\n2. Node browsing:")
                await self._demonstrate_node_browsing(client)
                
                # TODO: Batch operations
                print("\n3. Batch operations:")
                await self._demonstrate_batch_operations(client)
                
        except Exception as e:
            print(f"Node operations failed: {e}")
            print("Make sure OPC UA simulator is running!")
            
    async def _demonstrate_node_reading(self, client):
        """
        Demonstrate different ways to read node values.
        
        TODO: Implement various node reading patterns
        """
        try:
            # TODO: Read server time (standard node)
            server_time_node = client.get_node("i=2258")
            
            # TODO: Read with full data value (includes timestamp, quality)
            full_data = await server_time_node.read_data_value()
            
            print(full_data)
        except Exception as e:
            print(f"  Node reading error: {e}")
            
    async def recursive_browse(self, node, depth=0):
        """
        A function that recursively browses and prints the node tree.
        """
        # Create an indent string based on the depth for pretty printing
        indent = "  " * depth

        # Print the current node's browse name
        browse_name = await node.read_browse_name()
        print(f"{indent}- Node Address: {node}\tNode name: {browse_name.Name}")

        # Now, the recursive part:
        # 1. Get the children of the current 'node'
        # 2. Loop through each 'child'
        # 3. Call this same 'recursive_browse' function for the 'child'
        child_nodes = await node.get_children()
        for child in child_nodes:
            await self.recursive_browse(child, depth + 1)
        

    async def _demonstrate_node_browsing(self, client):
        """
        Demonstrate browsing the OPC UA node tree.
        
        TODO: Implement node tree exploration
        """
        try:
            root_node = client.get_node("i=85")

            # TODO: Start from Objects root
            await self.recursive_browse(root_node)            
        except Exception as e:
            print(f"  Node browsing error: {e}")

            
    async def _demonstrate_batch_operations(self, client):
        """
        Demonstrate reading multiple nodes efficiently.
        
        TODO: Implement batch reading operations
        """
        try:
            # TODO: Prepare list of nodes to read
            node_ids = ["ns=2;i=3", "ns=2;i=4", "ns=2;i=5"]
            nodes_to_read = [client.get_node(nid) for nid in node_ids]    

            # TODO: Read all nodes in one operation
            results = await client.read_values(nodes_to_read)
            
            # TODO: Display results
            for result in results:
                print(result)
        except Exception as e:
            print(f"  Batch operations error: {e}")
            
    async def implement_subscriptions(self):
        """
        Build real-time data subscription system.
        
        Learning Goal: Master real-time data monitoring
        Real Project Pattern: Subscription handling from main.py
        """
        print("\n=== REAL-TIME SUBSCRIPTIONS ===")
        
        server_url = "opc.tcp://localhost:4840/"
        
        try:
            async with Client(url=server_url) as client:
                # Acquire all machine nodes
                machine_root_node = client.get_node("ns=2;i=1")
                machine_nodes = []
                async def recursive_append(node):
                    children_nodes = await node.get_children()
                    if len(children_nodes) == 0:
                        machine_nodes.append(node)
                    else:
                        for child in children_nodes:
                            await recursive_append(child)
                await recursive_append(machine_root_node)

                # Create handler
                subscription_handler = await OPCUADataHandler.create(machine_nodes)
                subscription = await client.create_subscription(period=500.0, handler=subscription_handler)

                # Subscribe to all nodes in handler
                await subscription.subscribe_data_change(machine_nodes)
                
                # TODO: Monitor for specified duration
                monitoring_duration = 10
                print(f"Monitoring the subscription for {monitoring_duration}s")
                await asyncio.sleep(monitoring_duration)
                print(f"\nTotal update count: {subscription_handler.update_count}")
                await subscription.delete()
                
        except Exception as e:
            print(f"Subscription error: {e}")
            print("Make sure OPC UA simulator is running!")
            
    async def handle_opcua_errors(self):
        """
        Learn OPC UA-specific error handling patterns.
        
        Manufacturing Reality: OPC UA has specific error conditions
        """
        print("\n=== OPC UA ERROR HANDLING ===")
        
        # TODO: Demonstrate different error scenarios
        await self._demonstrate_connection_errors()
        await self._demonstrate_node_errors()
        await self._demonstrate_timeout_errors()
        
    async def _demonstrate_connection_errors(self):
        """
        Demonstrate handling connection errors.
        
        TODO: Implement connection error handling
        """
        print("1. Connection error handling:")
        
        # TODO: Try connecting to non-existent server
        server_url = "opc.tcp://localhost:4840/"
        try:
            async with Client(url=server_url) as client:
                # Try to access a non-existent node to trigger an error
                node = client.get_node("ns=2;i=50")
                await node.read_value()
        except UaStatusCodeError as e:
            print(f"  Connection error caught: {e}")
        
    async def _demonstrate_node_errors(self):
        """
        Demonstrate handling node-related errors.
        
        TODO: Implement node error handling
        """
        print("\n2. Node error handling:")
        
        server_url = "opc.tcp://localhost:4840/"
        
        try:
            async with Client(url=server_url) as client:
                # Try reading a non-existent node
                bad_node = client.get_node("ns=2;i=99999")  # unlikely to exist
                try:
                    value = await bad_node.read_value()
                    print(f"Unexpectedly read value from non-existent node: {value}")
                except UaStatusCodeError as e:
                    print(f"  Node error caught: {e}")
                except Exception as e:
                    print(f"  Unexpected error: {e}")
        except Exception as e:
            print(f"  Connection failed: {e}")

    async def _demonstrate_timeout_errors(self):
        """
        Demonstrate handling timeout errors.
        
        TODO: Implement timeout error handling
        """
        print("\n3. Timeout error handling:")
        
        # Simulate a timeout when reading a node value
        server_url = "opc.tcp://localhost:4840/"
        try:
            async with Client(url=server_url) as client:
                node = client.get_node("i=2258")
                try:
                    # Artificially short timeout to trigger error
                    value = await asyncio.wait_for(node.read_value(), timeout=0.001)
                    print(f"Read value: {value}")
                except asyncio.TimeoutError:
                    print("  Timeout error caught: Operation took too long.")
                except Exception as e:
                    print(f"  Unexpected error: {e}")
        except Exception as e:
            print(f"  Connection failed: {e}")
            
class OPCUADataHandler:
    """
    Handle incoming OPC UA data changes.
    
    Learning Goal: Process real-time industrial data
    Real Project Pattern: SubscriptionHandler from main.py
    """
    
    def __init__(self):
        """
        Initialize data handler.
        
        TODO: Set up data processing infrastructure
        """
        self.data_count = 0
        self.last_data_time = None
        self.node_names = {}
        self.update_count = 0
        
        print("  📊 Data handler initialized")

    @classmethod
    async def create(cls, nodes_to_cache):
        handler = cls()

        name_cache = {}
        for node in nodes_to_cache:
            node_address = node
            node_name = await node.read_browse_name()
            name_cache[node_address] = node_name.Name

        handler.node_names = name_cache

        return handler
        
    def datachange_notification(self, node: Node, val, data: DataChangeNotif):
        """
        Handle incoming data change notifications.
        
        This is called automatically when subscribed data changes.
        
        Args:
            node: The OPC UA node that changed
            val: The new value
            data: Additional metadata about the change
            
        TODO: Implement data processing logic
        Real Project Pattern: Similar to datachange_notification in main.py
        """
        display_name = self.node_names.get(node, "Unknown Node") 
        timestamp = data.monitored_item.Value.SourceTimestamp
        print(f"Node name: {display_name} ({node}) = {val}\tTimestamp: {timestamp}")
        self.update_count += 1

async def main():
    """Phase 2: Master OPC UA client development"""
    print("=== PHASE 2: OPC UA CLIENT DEVELOPMENT ===")
    print("Building industrial communication skills\n")
    
    # Load configuration
    try:
        with open("config.yaml") as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print("Warning: config.yaml not found. Using defaults.")
        config = {}
    
    # TODO: Start OPC UA server simulator if enabled
    print("Note: Make sure OPC UA simulator is running!")
    print("Run: python opcua_simulator.py")
    print()
    
    # Learn OPC UA client operations
    client_basics = OPCUAClientBasics(config)
    
    try:
        # TODO: Complete each learning exercise
        # await client_basics.learn_basic_connections()
        await client_basics.explore_node_operations()
        # await client_basics.implement_subscriptions()
        # await client_basics.handle_opcua_errors()
        
        print("\n=== PHASE 2 COMPLETE ===")
        print("Key concepts mastered:")
        print("✓ OPC UA client connections and authentication")
        print("✓ Node operations and data reading")
        print("✓ Real-time subscriptions and event handling")
        print("✓ OPC UA error handling and recovery")
        print("\nReady for Phase 3: Industrial Monitoring System")
        
    except KeyboardInterrupt:
        print("\nPhase 2 interrupted by user")
    except Exception as e:
        print(f"Phase 2 error: {e}")

if __name__ == "__main__":
    asyncio.run(main())