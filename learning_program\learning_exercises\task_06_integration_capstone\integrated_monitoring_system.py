"""
Phase 2: System Integration & Architecture

Learning Goals:
- Design complete monitoring system architectures
- Master integration patterns from real project
- Build production-ready configuration management
- Implement robust error handling and recovery

This phase builds production-ready systems using real project patterns.
"""

import asyncio
import yaml
import threading
from datetime import datetime, timezone
from pathlib import Path
import logging
from typing import Dict, List, Any
import json

# Import all the components from previous tasks
try:
    from asyncua import Client as OPCUAClient
    OPCUA_AVAILABLE = True
except ImportError:
    OPCUA_AVAILABLE = False
    print("Warning: asyncua not available")

try:
    from pyModbusTCP.client import ModbusClient
    MODBUS_AVAILABLE = True
except ImportError:
    MODBUS_AVAILABLE = False
    print("Warning: pyModbusTCP not available")

try:
    from influxdb_client import InfluxDBClient, Point
    from influxdb_client.client.write_api import ASYNCHRONOUS
    INFLUXDB_AVAILABLE = True
except ImportError:
    INFLUXDB_AVAILABLE = False
    print("Warning: influxdb-client not available")

class IntegratedMonitoringSystem:
    """
    Complete production monitoring system integrating all protocols.
    
    Learning Goal: Build systems matching real project architecture
    Real Project Pattern: Integration of power meter + OPC UA + InfluxDB systems
    """
    
    def __init__(self, config_path="config/"):
        """
        Initialize integrated monitoring system.
        
        TODO: Set up complete system architecture
        """
        self.config_path = Path(config_path)
        self.config = self.load_configuration()
        
        # System components
        self.opcua_clients = {}
        self.modbus_clients = {}
        self.influx_client = None
        self.write_api = None
        self.data_processors = {}
        
        # System state
        self.running = False
        self.monitoring_tasks = {}
        self.system_health = {
            'start_time': None,
            'total_data_points': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'active_connections': 0
        }
        
        # Error handling
        self.error_handlers = {}
        self.retry_strategies = {}
        
        # Set up logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        print("=== INTEGRATED MONITORING SYSTEM ===")
        print("Goal: Build production-ready multi-protocol monitoring\n")
        
    def load_configuration(self):
        """
        Load configuration from multiple files (real project pattern).
        
        TODO: Implement comprehensive configuration loading
        Learning Goal: Master production configuration management
        """
        config = {}
        
        try:
            # TODO: Load environment configuration
            env_file = self.config_path / "environments.yaml"
            if env_file.exists():
                with open(env_file) as f:
                    env_config = yaml.safe_load(f)
                    config.update(env_config)
                    print(f"✅ Loaded environment config: {env_file}")
            
            # TODO: Load machine configuration
            machines_file = self.config_path / "machines.yaml"
            if machines_file.exists():
                with open(machines_file) as f:
                    machines_config = yaml.safe_load(f)
                    config['machines'] = machines_config
                    print(f"✅ Loaded machine config: {machines_file}")
            
            # TODO: Apply environment-specific settings
            environment = config.get('environment', 'development')
            if environment in config:
                config.update(config[environment])
                print(f"✅ Applied {environment} environment settings")
                
        except Exception as e:
            print(f"❌ Configuration loading error: {e}")
            self.logger.error(f"Configuration error: {e}")
            
        return config
        
    async def initialize_system(self):
        """
        Initialize all system components.
        
        TODO: Implement complete system initialization
        Real Project Pattern: Initialization from main.py files
        """
        print("Initializing integrated monitoring system...")
        
        # TODO: Initialize InfluxDB client
        if INFLUXDB_AVAILABLE:
            await self.initialize_influxdb()
        else:
            print("⚠️ InfluxDB not available - data storage disabled")
        
        # TODO: Initialize OPC UA clients for each machine
        if OPCUA_AVAILABLE:
            await self.initialize_opcua_clients()
        else:
            print("⚠️ OPC UA not available - OPC UA monitoring disabled")
        
        # TODO: Initialize Modbus clients for power meters
        if MODBUS_AVAILABLE:
            await self.initialize_modbus_clients()
        else:
            print("⚠️ Modbus not available - Modbus monitoring disabled")
        
        # TODO: Initialize data processors
        await self.initialize_data_processors()
        
        # TODO: Initialize error handling systems
        await self.initialize_error_handling()
        
        print("✅ System initialization complete")
        
    async def initialize_influxdb(self):
        """
        Initialize InfluxDB client with production settings.
        
        TODO: Implement InfluxDB initialization
        Real Project Pattern: InfluxDB setup from actual project
        """
        try:
            # TODO: Get InfluxDB configuration
            influx_config = self.config.get('influxdb', {})
            
            if not influx_config:
                print("⚠️ No InfluxDB configuration found")
                return
                
            # TODO: Create InfluxDB client
            self.influx_client = InfluxDBClient(
                url=influx_config.get('url', 'http://localhost:8086'),
                token=influx_config.get('token', 'your-token'),
                org=influx_config.get('org', 'your-org')
            )
            
            # TODO: Test connection
            health = self.influx_client.health()
            print(f"✅ InfluxDB connection: {health.status}")
            
            # TODO: Set up write API with appropriate settings
            self.write_api = self.influx_client.write_api(write_options=ASYNCHRONOUS)
            
        except Exception as e:
            print(f"❌ InfluxDB initialization failed: {e}")
            self.logger.error(f"InfluxDB initialization error: {e}")
            
    async def initialize_opcua_clients(self):
        """
        Initialize OPC UA clients for all configured machines.
        
        TODO: Implement OPC UA client initialization
        Real Project Pattern: OPC UA setup from EOS M290 project
        """
        machines_config = self.config.get('machines', {})
        opcua_machines = machines_config.get('opcua_machines', {})
        
        for machine_id, machine_config in opcua_machines.items():
            try:
                # TODO: Extract OPC UA configuration
                opcua_config = machine_config.get('opcua', {})
                
                if not opcua_config:
                    continue
                    
                # TODO: Create OPC UA client configuration
                client_config = {
                    'url': opcua_config.get('url'),
                    'timeout': opcua_config.get('connection', {}).get('timeout_seconds', 30),
                    'retry_attempts': opcua_config.get('connection', {}).get('retry_attempts', 5),
                    'authentication': opcua_config.get('authentication', {})
                }
                
                self.opcua_clients[machine_id] = {
                    'config': client_config,
                    'client': None,
                    'status': 'configured'
                }
                
                print(f"✅ OPC UA client configured: {machine_id}")
                
            except Exception as e:
                print(f"❌ OPC UA client configuration failed for {machine_id}: {e}")
                
    async def initialize_modbus_clients(self):
        """
        Initialize Modbus clients for power meters and other devices.
        
        TODO: Implement Modbus client initialization
        Real Project Pattern: Modbus setup from power meter project
        """
        machines_config = self.config.get('machines', {})
        modbus_devices = machines_config.get('modbus_devices', {})
        
        for device_id, device_config in modbus_devices.items():
            try:
                # TODO: Extract Modbus configuration
                modbus_config = device_config.get('modbus', {})
                
                if not modbus_config:
                    continue
                    
                # TODO: Create Modbus client
                client = ModbusClient(
                    host=modbus_config.get('ip'),
                    port=modbus_config.get('port', 502),
                    unit_id=modbus_config.get('unit_id', 1),
                    timeout=modbus_config.get('timeout_seconds', 10),
                    auto_open=True,
                    auto_close=True
                )
                
                self.modbus_clients[device_id] = {
                    'client': client,
                    'config': device_config,
                    'status': 'configured'
                }
                
                print(f"✅ Modbus client configured: {device_id}")
                
            except Exception as e:
                print(f"❌ Modbus client configuration failed for {device_id}: {e}")
                
    async def initialize_data_processors(self):
        """
        Initialize data processors for efficient data handling.
        
        TODO: Set up data processing infrastructure
        """
        # TODO: Create data processors for each machine/device
        all_machines = {}
        
        # Add OPC UA machines
        opcua_machines = self.config.get('machines', {}).get('opcua_machines', {})
        all_machines.update(opcua_machines)
        
        # Add Modbus devices
        modbus_devices = self.config.get('machines', {}).get('modbus_devices', {})
        all_machines.update(modbus_devices)
        
        for machine_id in all_machines.keys():
            processor = DataProcessor(
                machine_id,
                self.influx_client,
                self.config.get('influxdb', {})
            )
            self.data_processors[machine_id] = processor
            print(f"✅ Data processor initialized: {machine_id}")
            
    async def initialize_error_handling(self):
        """
        Initialize error handling and recovery systems.
        
        TODO: Set up comprehensive error handling
        """
        # TODO: Initialize error handlers for different error types
        self.error_handlers = {
            'connection_error': self.handle_connection_error,
            'data_error': self.handle_data_error,
            'timeout_error': self.handle_timeout_error
        }
        
        # TODO: Initialize retry strategies
        self.retry_strategies = {
            'exponential_backoff': ExponentialBackoffRetry(),
            'linear_backoff': LinearBackoffRetry(),
            'immediate_retry': ImmediateRetry()
        }
        
        print("✅ Error handling systems initialized")
        
    async def start_monitoring(self):
        """
        Start monitoring all configured machines and devices.
        
        TODO: Implement complete monitoring startup
        Learning Goal: Master concurrent monitoring patterns
        """
        print("=== STARTING INTEGRATED MONITORING ===")
        self.running = True
        self.system_health['start_time'] = datetime.now(timezone.utc)
        
        # TODO: Start OPC UA monitoring tasks
        for machine_id, client_info in self.opcua_clients.items():
            task = asyncio.create_task(
                self.monitor_opcua_machine(machine_id, client_info)
            )
            self.monitoring_tasks[f"opcua_{machine_id}"] = task
            print(f"✅ Started OPC UA monitoring: {machine_id}")
            
        # TODO: Start Modbus monitoring tasks
        for device_id, client_info in self.modbus_clients.items():
            task = asyncio.create_task(
                self.monitor_modbus_device(device_id, client_info)
            )
            self.monitoring_tasks[f"modbus_{device_id}"] = task
            print(f"✅ Started Modbus monitoring: {device_id}")
            
        # TODO: Start system health monitoring
        health_task = asyncio.create_task(self.monitor_system_health())
        self.monitoring_tasks["system_health"] = health_task
        
        # TODO: Start data processing and storage
        storage_task = asyncio.create_task(self.process_and_store_data())
        self.monitoring_tasks["data_storage"] = storage_task
        
        print(f"✅ Started {len(self.monitoring_tasks)} monitoring tasks")
        
        # TODO: Wait for tasks or handle shutdown
        try:
            await asyncio.gather(*self.monitoring_tasks.values())
        except KeyboardInterrupt:
            print("\nShutdown requested...")
        finally:
            await self.stop_monitoring()
            
    async def monitor_opcua_machine(self, machine_id, client_info):
        """
        Monitor single OPC UA machine (real project pattern).
        
        TODO: Implement OPC UA monitoring
        Real Project Pattern: Monitoring from EOS M290 main.py
        """
        print(f"  📡 Starting OPC UA monitoring for {machine_id}")
        
        retry_count = 0
        max_retries = client_info['config'].get('retry_attempts', 5)
        
        while self.running:
            try:
                # TODO: Connect to machine
                url = client_info['config']['url']
                
                async with OPCUAClient(url=url) as client:
                    print(f"  ✅ Connected to OPC UA machine: {machine_id}")
                    client_info['status'] = 'connected'
                    self.system_health['active_connections'] += 1
                    retry_count = 0
                    
                    # TODO: Set up subscriptions for real-time data
                    handler = OPCUADataHandler(machine_id, self.data_processors[machine_id])
                    subscription = await client.create_subscription(1000, handler)
                    
                    # TODO: Subscribe to configured data points
                    machines_config = self.config.get('machines', {}).get('opcua_machines', {})
                    machine_config = machines_config.get(machine_id, {})
                    data_points = machine_config.get('data_points', {})
                    
                    nodes_to_monitor = []
                    for point_name, point_config in data_points.items():
                        try:
                            node_id = point_config.get('node_id')
                            if node_id:
                                node = client.get_node(node_id)
                                nodes_to_monitor.append(node)
                        except Exception as e:
                            print(f"    ❌ Failed to add node {point_name}: {e}")
                            
                    if nodes_to_monitor:
                        await subscription.subscribe_data_change(nodes_to_monitor)
                        print(f"    📊 Subscribed to {len(nodes_to_monitor)} data points")
                        
                    # TODO: Keep connection alive
                    while self.running:
                        await asyncio.sleep(5)
                        # TODO: Health check
                        try:
                            await client.nodes.server_time.read_value()
                        except Exception as e:
                            print(f"    ⚠️ Health check failed: {e}")
                            break
                            
            except Exception as e:
                retry_count += 1
                client_info['status'] = 'error'
                self.system_health['failed_operations'] += 1
                
                # TODO: Handle OPC UA specific errors
                await self.handle_opcua_error(machine_id, e, retry_count, max_retries)
                
    async def monitor_modbus_device(self, device_id, client_info):
        """
        Monitor single Modbus device (real project pattern).
        
        TODO: Implement Modbus monitoring
        Real Project Pattern: Monitoring from power meter main.py
        """
        print(f"  🔌 Starting Modbus monitoring for {device_id}")
        
        client = client_info['client']
        device_config = client_info['config']
        collection_config = device_config.get('collection', {})
        interval = collection_config.get('interval_seconds', 5)
        
        while self.running:
            try:
                # TODO: Read Modbus registers
                if client.open():
                    client_info['status'] = 'connected'
                    
                    # TODO: Read configured registers
                    registers = device_config.get('registers', {})
                    data_points = {}
                    
                    for register_name, register_config in registers.items():
                        try:
                            # TODO: Read register based on type
                            address = register_config.get('address')
                            reg_type = register_config.get('type', 'float32')
                            
                            if reg_type == 'float32':
                                # Read 2 registers for float32
                                values = client.read_holding_registers(address, 2)
                                if values:
                                    # Convert to float (implementation depends on byte order)
                                    data_points[register_name] = self.convert_modbus_float(values)
                            else:
                                # Read single register
                                values = client.read_holding_registers(address, 1)
                                if values:
                                    data_points[register_name] = values[0]
                                    
                        except Exception as e:
                            print(f"    ❌ Failed to read {register_name}: {e}")
                            
                    # TODO: Process and store data
                    if data_points:
                        await self.data_processors[device_id].process_modbus_data(data_points)
                        self.system_health['successful_operations'] += 1
                        
                    client.close()
                    
                else:
                    client_info['status'] = 'error'
                    print(f"    ❌ Failed to connect to {device_id}")
                    self.system_health['failed_operations'] += 1
                    
                await asyncio.sleep(interval)
                
            except Exception as e:
                client_info['status'] = 'error'
                # TODO: Handle Modbus specific errors
                await self.handle_modbus_error(device_id, e)
                await asyncio.sleep(interval * 2)  # Wait longer on error
                
    def convert_modbus_float(self, values):
        """
        Convert Modbus register values to float.
        
        TODO: Implement proper float conversion
        """
        # This is a simplified conversion - real implementation depends on byte order
        if len(values) >= 2:
            return float(values[0] + values[1] / 1000.0)
        return 0.0
        
    async def process_and_store_data(self):
        """
        Process and store data from all sources.
        
        TODO: Implement unified data processing
        Learning Goal: Master data integration patterns
        """
        flush_interval = self.config.get('influxdb', {}).get('flush_interval_seconds', 10)
        
        while self.running:
            try:
                await asyncio.sleep(flush_interval)
                
                # TODO: Flush all data processors
                for machine_id, processor in self.data_processors.items():
                    await processor.flush_data()
                    
            except Exception as e:
                print(f"❌ Data processing error: {e}")
                
    async def monitor_system_health(self):
        """
        Monitor overall system health and performance.
        
        TODO: Implement system health monitoring
        """
        while self.running:
            try:
                await asyncio.sleep(30)  # Health check every 30 seconds
                
                # TODO: Calculate health metrics
                uptime = datetime.now(timezone.utc) - self.system_health['start_time']
                
                print(f"\n📊 System Health:")
                print(f"   Uptime: {uptime}")
                print(f"   Active connections: {self.system_health['active_connections']}")
                print(f"   Total data points: {self.system_health['total_data_points']}")
                print(f"   Success rate: {self.calculate_success_rate():.1f}%")
                
            except Exception as e:
                print(f"❌ Health monitoring error: {e}")
                
    def calculate_success_rate(self):
        """Calculate system success rate."""
        total = self.system_health['successful_operations'] + self.system_health['failed_operations']
        if total == 0:
            return 100.0
        return (self.system_health['successful_operations'] / total) * 100
        
    async def handle_opcua_error(self, machine_id, error, retry_count, max_retries):
        """Handle OPC UA specific errors."""
        print(f"    ❌ OPC UA error for {machine_id}: {error}")
        
        if retry_count >= max_retries:
            print(f"    ❌ Max retries reached for {machine_id}")
            await asyncio.sleep(60)
        else:
            wait_time = min(retry_count * 5, 30)
            await asyncio.sleep(wait_time)
            
    async def handle_modbus_error(self, device_id, error):
        """Handle Modbus specific errors."""
        print(f"    ❌ Modbus error for {device_id}: {error}")
        
    async def handle_connection_error(self, component, error):
        """Handle connection errors with appropriate recovery."""
        pass
        
    async def handle_data_error(self, component, error):
        """Handle data processing errors."""
        pass
        
    async def handle_timeout_error(self, component, error):
        """Handle timeout errors."""
        pass
        
    async def stop_monitoring(self):
        """Gracefully stop all monitoring operations."""
        print("\n🛑 Stopping integrated monitoring system...")
        self.running = False
        
        # Cancel all tasks
        for task_name, task in self.monitoring_tasks.items():
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self.monitoring_tasks.values(), return_exceptions=True)
        
        # Close connections
        if self.influx_client:
            self.influx_client.close()
            
        print("✅ Integrated monitoring system stopped")

class OPCUADataHandler:
    """Handle OPC UA data changes."""
    
    def __init__(self, machine_id, data_processor):
        self.machine_id = machine_id
        self.data_processor = data_processor
        
    def datachange_notification(self, node, val, data):
        """Handle data change notifications."""
        asyncio.create_task(
            self.data_processor.process_opcua_data(str(node.nodeid), val, data.monitored_item.Value.SourceTimestamp)
        )

class DataProcessor:
    """Process and store data efficiently."""
    
    def __init__(self, machine_id, influx_client, influx_config):
        self.machine_id = machine_id
        self.influx_client = influx_client
        self.influx_config = influx_config
        self.data_buffer = []
        
    async def process_opcua_data(self, node_id, value, timestamp):
        """Process OPC UA data."""
        point = Point("opcua_data")
        point = point.tag("machine", self.machine_id)
        point = point.tag("node_id", node_id)
        point = point.field("value", float(value) if isinstance(value, (int, float)) else str(value))
        point = point.time(timestamp)
        
        self.data_buffer.append(point)
        
    async def process_modbus_data(self, data_points):
        """Process Modbus data."""
        timestamp = datetime.now(timezone.utc)
        
        for register_name, value in data_points.items():
            point = Point("modbus_data")
            point = point.tag("device", self.machine_id)
            point = point.tag("register", register_name)
            point = point.field("value", float(value))
            point = point.time(timestamp)
            
            self.data_buffer.append(point)
            
    async def flush_data(self):
        """Flush data buffer to InfluxDB."""
        if not self.data_buffer or not self.influx_client:
            return
            
        try:
            bucket = self.influx_config.get('bucket', 'integrated_manufacturing')
            write_api = self.influx_client.write_api()
            write_api.write(bucket=bucket, record=self.data_buffer)
            
            buffer_size = len(self.data_buffer)
            self.data_buffer = []
            print(f"    💾 Flushed {buffer_size} data points for {self.machine_id}")
            
        except Exception as e:
            print(f"    ❌ Data flush error for {self.machine_id}: {e}")

class ExponentialBackoffRetry:
    """Exponential backoff retry strategy."""
    pass

class LinearBackoffRetry:
    """Linear backoff retry strategy."""
    pass

class ImmediateRetry:
    """Immediate retry strategy."""
    pass

async def main():
    """Phase 2: Build complete integrated monitoring system"""
    print("=== PHASE 2: SYSTEM INTEGRATION & ARCHITECTURE ===")
    print("Building production-ready monitoring systems\n")
    
    # Create integrated monitoring system
    monitoring_system = IntegratedMonitoringSystem()
    
    try:
        # Initialize all components
        await monitoring_system.initialize_system()
        
        # Start monitoring
        await monitoring_system.start_monitoring()
        
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"System error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
