'''
This is an example to show how to pull lots of data from the InfluxDB database in bulk for plotting.
'''

import os
import time
import datetime
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from influxdb_client import InfluxDBClient

client = InfluxDBClient(url="http://influxdb.ami.modelfactory.sg:8086",
                        token='OLZ2GVjo1uEFHzbFr35HSED04JXOk9WEzta8BeYeK1b9hCJBUrxwpmhzKwS7EDtMr6wN6A8P6EpghaXif2F35Q==', # Token for "Read endpoint for Python analysis scripts"
                        #token='BAUefNIXOaHhiGcJlHSOXUPeZFsECwKuw3Cz72EUr9fOtWZdLwYlHLUQn7Pzq__qqesPFjXYV2hyjCGItHx__Q==', # Token for "Generic RW token for test bucket"
                        org='266e2e2e067fbe5b',
                        )

query_text = '''
import "experimental"

query1 = from(bucket: "eos_opcua")
  //|> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> range(start: -360d, stop: 0d)
  |> filter(fn: (r) => r["_measurement"] == "printer stats")
  |> filter(fn: (r) => r["eqpt"] == "SI3654")
  |> filter(fn: (r) => r["_field"] == "EOS.Machine.Sensors.ProcessChamber.O2ConcentrationTop")
  |> aggregateWindow(every: 180s, fn: mean, createEmpty: false)
  |> filter(fn: (r) => r._value >= 10.0 and r._value <= 15.0)

query2 = from(bucket: "eos_opcua")
  |> range(start: -360d, stop: 0d)
  |> filter(fn: (r) => r["_measurement"] == "printer stats")
  |> filter(fn: (r) => r["eqpt"] == "SI3654")
  |> filter(fn: (r) => r["_field"] == "EOS.Machine.Sensors.ProcessChamber.O2ConcentrationTop")
  |> aggregateWindow(every: 180s, fn: mean, createEmpty: false)
  |> difference(nonNegative: false)
  |> filter(fn: (r) => r._value < -0.5)

referenceTime = join(
    tables: {t1: query1, t2: query2},
    on: ["_time"],
    method: "inner"
)
  //|> tail(n: 1, offset: 0)  // Get the second last data point
  |> keep(columns: ["_time"])  // Keep only the _time column
  |> yield()
  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
'''
query_api = client.query_api()
start_time = time.time()
df_results = query_api.query_data_frame(query=query_text)
#print(df_results) # Shows, e.g
#result  table                     _time
#0  _result      0 2024-07-31 03:56:00+00:00
#1  _result      0 2024-08-07 08:44:00+00:00
#0.6695709228515625
print("Time taken to find pumpdown date times (s):",time.time() - start_time)

fig, axes = plt.subplots(2,1,figsize=(12,8),sharex=True)
records = []

# For each _time, take -5m and +15m and get the data points
for ref_time in df_results['_time']:
    start_time = ref_time - datetime.timedelta(minutes=5)
    end_time = ref_time + datetime.timedelta(minutes=15)
    age_of_data_days = (datetime.datetime.now(datetime.timezone.utc) - ref_time).total_seconds() / 86400
    
    query = f'''
    from(bucket: "eos_opcua")
      |> range(start: {start_time.isoformat()}, stop: {end_time.isoformat()})
      |> filter(fn: (r) => r["_measurement"] == "printer stats")
      |> filter(fn: (r) => r["eqpt"] == "SI3654")
      |> filter(fn: (r) => r["_field"] == "EOS.Machine.Sensors.ProcessChamber.O2ConcentrationTop")
      |> yield()
    '''
    
    result = query_api.query(query=query)[0]
    # Plots the result
    datetime_label = ref_time.strftime("%d %b %Y")
    X_values = np.asarray([(r['_time']-ref_time).total_seconds() for r in result.records])
    Y_values = np.asarray([r['_value'] for r in result.records])
    # Finds the point at which Y first drops
    Y_index_crossing = np.where(Y_values < 19)[0][0]
    X_shifted_values = X_values - X_values[Y_index_crossing] + 20
    
    # If the final 10% of the datapoints in Y_values is not less than the value of 1.0 then we discard this record
    if np.mean(Y_values[-int(len(Y_values)*0.1):]) > 1.0:
        continue # Then the data is rejected
    if np.mean(Y_values[:int(len(Y_values)*0.01)]) < 15.0:
        continue # Then the data is rejected
    if age_of_data_days > 2: # Only do filtering for old data so that the operator can run troubleshooting on recent data.
      if np.max(Y_values[X_shifted_values>600]) > 1.0: # After 10 minutes, the Y value must be less than 1.0
          continue # Then the data is rejected
      if np.max(Y_values[X_shifted_values>70]) > 14:
          continue # Then the data is rejected

    # If the data is good, we append that to the records
    record = {'X_shifted_values': X_shifted_values,
              'Y_values': Y_values,
              'datetime_label': datetime_label}
    records.append(record)

# Plots all the valid records:
for record_number, record in enumerate(records):
    color = [record_number/len(records),0,0]
    kwargs = {'color':color, 'markersize':2, 'linewidth':1, 'label':record['datetime_label'],'alpha':0.8}
    axes[0].plot(record['X_shifted_values']/60, record['Y_values'], '.-', **kwargs)
    axes[1].semilogy(record['X_shifted_values']/60, record['Y_values'], '.-', **kwargs)
    
# Format the plot
#axes[0].legend(fontsize=6)
axes[0].set_title(f"From {records[0]['datetime_label']} (black) to {records[-1]['datetime_label']} (red)", fontsize=10)
axes[1].set_xlabel('Time (minutes)')
axes[0].set_ylabel('Oxygen (%)')
axes[1].set_ylabel('Oxygen (%)')
plt.tight_layout()

plt.savefig(os.path.join(os.path.dirname(__file__), os.path.basename(__file__).split('.',1)[0] + '(output).png'),dpi=300)
plt.show()