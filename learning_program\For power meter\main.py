import os
import time
import yaml
import signal
import base64
import socket
import struct
import requests
import platform
import threading
import subprocess
from contextlib import closing

import influxdb_client # https://influxdb-client.readthedocs.io/en/latest/usage.html
from pyModbusTCP.client import ModbusClient # Expecting version 0.2.1

# Loads environment file
try:
    with open("env.yaml") as f:
        env = yaml.safe_load(f)
except FileNotFoundError as e:
    raise FileNotFoundError('The env.yaml file should be present. Just copy the env.yaml.template file if no production token is required.')

from lib_loggers import set_logger
logger = set_logger()

env_choice = 'production' # Either production or development
logger.debug(f'Loading environment for choice of {env_choice}')
influxdb_token = env[env_choice]['influxdb_token']
influxdb_bucket_name = env[env_choice]['influxdb_bucket_name']
influxdb_org_id = '266e2e2e067fbe5b' # For AMI
influxdb_url = 'http://influxdb.ami.modelfactory.sg:8086'

# Shared dictionary so that dead thread can be restarted
last_active_epoches = {}
lock = threading.Lock()

script_is_active = True
eqptnames_active = {}

def check_socket(host, port, timeout_ms=500,type='ipv4'):
    assert type in ['ipv4','ipv6']
    with closing(socket.socket(socket.AF_INET if type=='ipv4' else socket.AF_INET6, socket.SOCK_STREAM)) as sock:
        sock.settimeout(timeout_ms/1000)
        if sock.connect_ex((host, port)) == 0:
            return True
        else:
            return False

def check_ping(host):
    # Works for Linux, Mac and Windows
    param = '-n' if platform.system().lower()=='windows' else '-c'
    command = ['ping', param, '1', host]
    return subprocess.call(command,stdout=subprocess.DEVNULL,stderr=subprocess.STDOUT) == 0

def responsive_sleep(wait_s):
    # This is time.sleep() but watches to see if the script is still active.
    start_time = time.time()
    while script_is_active:
        if time.time() - start_time > wait_s:
            break
        time.sleep(0.05)
    return

class SDM630PowerMeter():
    client = None # Has attributes .host and .port if info needs to be retrieved
    last_known_is_connected = None
    monitored_equipment_name = None # e.g EOS_SI3654, wet_sep, sieve_shaker, air_dryer
    
    counters = {'read_success':0,
                'read_failure':0,
                'consecutive_read_errors':0,
                'connect_success':0,
                'connect_failure':0,
                'connection_dropped':0,
                'reboot':0,
                }
    
    class PW11ReadError(Exception):
        pass
        
    def __init__(self,IP_address,monitored_equipment_name,port=8899):
        logger.debug(f'Client started for {IP_address}.')
        self.monitored_equipment_name = monitored_equipment_name
        self.client = ModbusClient(host=IP_address, port=port, auto_open=True, auto_close=False,timeout=1)
        self.open_comms_if_needed()
    
    def open_comms_if_needed(self):
        if self.client.is_open:
            logger.debug('Found that connection is still open, hence no retrying effort is required.')
            self.last_known_is_connected = self.client.is_open
            return 

        # Changes counters
        if self.last_known_is_connected is True:
            self.counters['connection_dropped'] += 1
        self.last_known_is_connected = False
            
        # Since there is no connection, we will keep retrying until we are successful
        retry_counter = 0
        while script_is_active:
            if eqptnames_active.get(self.monitored_equipment_name) is False:
                logger.info(f'Thread for {self.monitored_equipment_name} received notice to stop. Stopping SDM630 client.')
                break
                
            # Attempts a connection to equipment
            open_attempt_response = self.client.open()
            if open_attempt_response:
                self.counters['connect_success'] += 1
                if retry_counter==0:
                    logger.debug(f'Connection to {self.client.host} successfully opened.')
                else:
                    logger.debug(f'Connection to {self.client.host} successfully opened on retry {retry_counter}.')
                self.last_known_is_connected = True

                # Verifies that the connection is good and that a read would be successful
                try:
                    self.read_registers(reg_addr=0x000,read_count=1,register_type='input')
                    break
                except SDM630PowerMeter.PW11ReadError:
                    pass # The device is still bad, so we continue with retrying with other methods
                    
            else:
                self.counters['connect_failure'] += 1
                if self.counters['connect_failure'] < 100 or self.counters['connect_failure'] % 1000 == 0:
                    logger.debug(f'Connection to {self.client.host} was not successful.')
        
            # Checks whether the equipment is even online or cannot even ping
            ping_status = check_ping(host=self.client.host)
            serial_socket_status = check_socket(host=self.client.host, port=self.client.port)
            webport_socket_status = check_socket(host=self.client.host, port=80)
            if (ping_status is False) and (serial_socket_status is False):
                offline_wait_time_s = 30
                if (retry_counter < 100) or (retry_counter < 10000 and retry_counter % 1000 == 0) or (retry_counter % 100000 == 0):
                    logger.debug(f'Found with retry {retry_counter} that device is responding neither to a ping or port check. Device is probably offline. Waiting {offline_wait_time_s}s...')
                responsive_sleep(offline_wait_time_s)
            else:
                logger.debug(f'Found that ping check is {ping_status}, web port check is {webport_socket_status} and socket port check is {serial_socket_status}')
            
            # Attempts a reboot sometimes
            if ping_status or serial_socket_status or webport_socket_status:
                if retry_counter % 3 == 0:
                    self.reboot_PW11()
            
            retry_counter += 1
            responsive_sleep(5)
    
    def reboot_PW11(self,with_wait_s=6):
        # After rebooting the PW11, the device take between 3s to 6s to become responsive again
        self.counters['reboot'] += 1
        webport_socket_status = check_socket(host=self.client.host, port=80)
        if webport_socket_status:
            headers = {'Authorization': 'Basic '+ base64.b64encode(b'admin:admin').decode('utf-8')}
            data = 'msg={"CID":20003,"PL":{}}' # This is the command to restart the PW11
            try:
                response = requests.post(f'http://{self.client.host}/cmd', headers=headers, data=data)
                assert response.text == '{"CID":20004,"RC":0}', f'The response was unexpected: {response.text}'
                logger.debug(f'Reboot command was sent to PWS11. Now waiting {with_wait_s}s...')
            except ConnectionError:
                logger.debug('Webserver has port 80 showing opened but the webserver is actually dead. A human is likely required at the PW11 to physically restart the hardware.')
        else:
            logger.debug('Reboot command failed as the web port 80 is not opened. This might happen if the web service of the PW11 is dead or if the PW11 is behind a NAT which only forwarded the serial port.')
        
        responsive_sleep(with_wait_s)


    def get_PW11_wifi_rssi(self):
        webport_socket_status = check_socket(host=self.client.host, port=80)
        if not webport_socket_status:
            raise ConnectionError(f'Web port 80 is not open for {self.client.host}.')

        headers = {'Authorization': 'Basic '+ base64.b64encode(b'admin:admin').decode('utf-8')}
        
        # Sends first shot, for which the server will reply with SOCK values
        data = 'msg={"CID":10001,"PL":["SYS","UART","SOCK","VPN","CLIENTS","ROUTING","TRAFFIC"]}'
        response = requests.post(f'http://{self.client.host}/cmd', headers=headers, data=data)
        assert response.json()['CID']==10002 and list(response.json()['PL'].keys())[0]=='SOCK'
        
        # Sends second shot, for which the server will reply with UART values
        data = 'msg={"CID":10001,"PL":["SYS","UART","SOCK","VPN","CLIENTS","ROUTING","TRAFFIC"],"Remain":1}'
        response = requests.post(f'http://{self.client.host}/cmd', headers=headers, data=data)
        assert response.json()['CID']==10002 and list(response.json()['PL'].keys())[0]=='UART'

        # Sends third shot, for which the server will reply with SYS values
        data = 'msg={"CID":10001,"PL":["SYS","UART","SOCK","VPN","CLIENTS","ROUTING","TRAFFIC"],"Remain":1}'
        response = requests.post(f'http://{self.client.host}/cmd', headers=headers, data=data)
        assert response.json()['CID']==10002 and list(response.json()['PL'].keys())[0]=='SYS'
        wifi_rssi = response.json()['PL']['SYS']['WiFiRssi']

        if self.throttle_log():
            logger.debug(f'PW11 wifi RSSI for {self.client.host} is: {wifi_rssi}')
        return wifi_rssi

    @staticmethod
    def decode_for_float(byte_pair):
        if len(byte_pair)>2:
            return [SDM630PowerMeter.decode_for_float(byte_pair[2*n:(2*(n+1))]) for n in range(len(byte_pair)//2)]
        combined_value = (byte_pair[0] << 16) | byte_pair[1]
        packed_value = struct.pack('>I', combined_value)
        float_value = struct.unpack('>f', packed_value)[0]
        return float_value

    def throttle_log(self):
        if self.counters['read_success']<50:
            return True
        elif (50<=self.counters['read_success']<500) and ((self.counters['read_success'] + self.counters['read_failure']) % 20 == 0):
            return True
        elif (500<=self.counters['read_success']) and ((self.counters['read_success'] + self.counters['read_failure']) % 100 == 0):
            return True
        elif (5000<=self.counters['read_success']) and ((self.counters['read_success'] + self.counters['read_failure']) % 1000 == 0):
            return True
        elif (50000<=self.counters['read_success']) and ((self.counters['read_success'] + self.counters['read_failure']) % 10000 == 0):
            return True
        else:
            return False

    def read_registers(self,reg_addr,read_count=1,register_type='input'):
        assert register_type in ['input','holding']
        if read_count > 80:
            logger.warning(f'The user manual says not to read more than 80 registers at the same time but you requested {read_count}.')
        if register_type=='input':
            response = self.client.read_input_registers(reg_addr=reg_addr,reg_nb=read_count)
            #logger.debug('Reading client input registers.')
        elif register_type=='holding':
            # Note that attempting to read the holding registers of a PW11 seems likely to cause a hang that requires a human to press the Reload button
            # on the PW11 for a hard reset. The web port will not be functional for a software restart via reboot_PW11().
            raise NotImplementedError
        else:
            raise ValueError('Unexpected register type')
        
        # Chaos monkey trial for debugging
        if False:
            if 5<= self.counters['read_success'] + self.counters['read_failure'] <= 8:
                logger.info(f"Chaos monkey simulating conns failure for read counter {self.counters['read_success'] + self.counters['read_failure']}")
                self.counters['read_failure'] += 1
                self.counters['consecutive_read_errors'] += 1
                raise SDM630PowerMeter.PW11ReadError
        
        # Tracks the success and failure rate
        if response is None:
            self.counters['read_failure'] += 1
            self.counters['consecutive_read_errors'] += 1
            logger.error(f'Failure reading input registers starting at {reg_addr} (0x{reg_addr:<02X}). Read success={self.counters["read_success"]}, failure={self.counters["read_failure"]}, reboot={self.counters["reboot"]}.')
            raise SDM630PowerMeter.PW11ReadError
        else:
            self.counters['read_success'] += 1
            self.counters['consecutive_read_errors'] = 0
            if self.throttle_log():
                logger.debug(f'Success reading input register (success={self.counters["read_success"]}, failure={self.counters["read_failure"]} ({self.counters["read_failure"]/self.counters["read_success"]*100:.3f}%), reboot={self.counters["reboot"]} ({self.counters["reboot"]/self.counters["read_success"]*100:.3f}%)).')
            
        # For debugging use to print all the register being read
        if False:
            for byte_index, single_byte in enumerate(response):
                print(f'Reg 0x{reg_addr+byte_index:0>4X} = {single_byte}')
            
        return response

    def read_registers_with_retries(self,reg_addr,read_count=1,register_type='input'):
        while True:
            try:
                return self.read_registers(reg_addr=reg_addr,read_count=read_count,register_type=register_type)
            except SDM630PowerMeter.PW11ReadError:
                logger.info('Error when reading registers. Attempting to re-open comms...')
                
                if self.counters['consecutive_read_errors'] > 3:
                    # Just re-reading the client doesn't help, so let's try to close the TCP connection (which open_comms_if_needed will auto open later)
                    logger.debug('More than 3 consecutive read errors encountered, thus re-opening the TCP connection.')
                    self.client.close()
                
                self.open_comms_if_needed()

                # Rate-limiter
                responsive_sleep(3)

    def get_voltages_V(self):
        # Phase {1,2,3} lien to neutral volts in Volts.
        response = self.read_registers_with_retries(reg_addr=0x0000,read_count=6)
        values = self.decode_for_float(response)
        if self.throttle_log():
            logger.debug(f'Read that voltages were {values}')
        return values

    def get_currents_A(self):
        # Phase {1,2,3} current in Amps.
        response = self.read_registers_with_retries(reg_addr=0x0006,read_count=6)
        values = self.decode_for_float(response)
        if self.throttle_log():
            logger.debug(f'Read that currents were {values}')
        return values

    def get_current_sum_A(self):
        # Sum of line currents in A
        response = self.read_registers_with_retries(reg_addr=0x0030,read_count=2)
        return self.decode_for_float(response)

    def get_average_current_A(self):
        # Average of line currents in A
        response = self.read_registers_with_retries(reg_addr=0x002e,read_count=2)
        return self.decode_for_float(response)

    def get_powers_W(self):
        # Phase {1,2,3} power in Watts.
        response = self.read_registers_with_retries(reg_addr=0x000C,read_count=6)
        values = self.decode_for_float(response)
        if self.throttle_log():
            logger.debug(f'Read that powers were {values}')
        return values
    
    def get_powers_kW(self):
        return [f/1000 for f in self.get_powers_W()]

    def get_voltamps_VA(self):
        # Phase {1,2,3} volt amps in units of VA.
        response = self.read_registers_with_retries(reg_addr=0x0012,read_count=6)
        values = self.decode_for_float(response)
        if self.throttle_log():
            logger.debug(f'Read that voltamps were {values}')
        return values

    def get_reactive_power_VAr(self):
        # Reactive power {1,2,3} in units of VAr.
        response = self.read_registers_with_retries(reg_addr=0x0018,read_count=6)
        values = self.decode_for_float(response)
        return values
    
    def get_power_factors(self):
        # Power factor of {1,2,3}
        response = self.read_registers_with_retries(reg_addr=0x001e,read_count=6)
        values = self.decode_for_float(response)
        return values    
        
    def get_phase_angles(self):
        # Phase angle of {1,2,3}
        response = self.read_registers_with_retries(reg_addr=0x0024,read_count=6)
        values = self.decode_for_float(response)
        return values
      
    def get_current_THDs(self):
        # Reads phase {1,2,3} current THD in %
        response = self.read_registers_with_retries(reg_addr=0x00F0,read_count=6)
        values = self.decode_for_float(response)
        return values

    def get_voltage_THDs(self):
        # Reads phase {1,2,3} V to N voltage THD in %
        response = self.read_registers_with_retries(reg_addr=0x00EA,read_count=6)
        values = self.decode_for_float(response)
        return values

      
    def get_line_to_line_volts(self):
        # Get L1-L2, L2-L3, L3-L1 voltage in V
        response = self.read_registers_with_retries(reg_addr=0x00c8,read_count=6)
        values = self.decode_for_float(response)
        return values
   
    def get_neutral_current_A(self):
        response = self.read_registers_with_retries(reg_addr=0x00e0,read_count=2)
        return self.decode_for_float(response)
        

    def get_total_system_power_W(self):
        response = self.read_registers_with_retries(reg_addr=0x0034,read_count=2)
        return self.decode_for_float(response)
    
    def get_total_system_power_factor(self):
        response = self.read_registers_with_retries(reg_addr=0x003e,read_count=2)
        return self.decode_for_float(response)
   
    def get_total_system_phase_angle(self):
        response = self.read_registers_with_retries(reg_addr=0x0042,read_count=2)
        return self.decode_for_float(response)
   
    def get_supply_frequency_Hz(self):
        response = self.read_registers_with_retries(reg_addr=0x0046,read_count=2)
        return self.decode_for_float(response)
        
    def get_total_import_kWh(self):
        response = self.read_registers_with_retries(reg_addr=0x0048,read_count=2)
        return self.decode_for_float(response)
            
    def get_total_export_kWh(self):
        response = self.read_registers_with_retries(reg_addr=0x004a,read_count=2)
        return self.decode_for_float(response)
        
    def get_total_power_demand_W(self):
        response = self.read_registers_with_retries(reg_addr=0x0054,read_count=2)
        return self.decode_for_float(response)
    
    def get_entire_readable_input_registers(self):
        response = self.read_registers_with_retries(reg_addr=0x0000,read_count=64)
        response += self.read_registers_with_retries(reg_addr=0x0040,read_count=64)
        response += self.read_registers_with_retries(reg_addr=0x0080,read_count=64)
        response += self.read_registers_with_retries(reg_addr=0x00c0,read_count=64)
        response += self.read_registers_with_retries(reg_addr=0x0100,read_count=64)
        response += self.read_registers_with_retries(reg_addr=0x0120,read_count=64)
        response += self.read_registers_with_retries(reg_addr=0x0140,read_count=64)
        response += self.read_registers_with_retries(reg_addr=0x0160,read_count=30) # Since the last parameter is the 2 bytes at 0x017c
        logger.debug(f'Entire readable registers were: {response}')
        return response
        


class InfluxDBEastronWatcher():
    # This links to both InfluxDB and Eastron SDM630 to periodically retrieve data
    SDM630_client = None
    influxdb_client = None
    influxdb_write_api = None
    monitored_equipment_name = None # e.g EOS_SI3654, wet_sep, sieve_shaker, air_dryer
    data_sent_counter = 0
    
    def __init__(self,monitored_equipment_name,SDM630_IP_address,SDM630_port=8899):
        self.monitored_equipment_name = monitored_equipment_name
        self.influxdb_client = influxdb_client.InfluxDBClient(url=influxdb_url,token=influxdb_token,org=influxdb_org_id)
        self.influxdb_write_api = self.influxdb_client.write_api(write_options=influxdb_client.client.write_api.SYNCHRONOUS)
        
        # The thread shall make itself known even before .periodic_data_fetching() as the next line will not return until the device is online
        with lock:
            last_active_epoches[self.monitored_equipment_name] = time.time()
        self.SDM630_client = SDM630PowerMeter(IP_address=SDM630_IP_address,monitored_equipment_name=monitored_equipment_name,port=SDM630_port)

    def fetch_data(self, verbosity=None):
        p = influxdb_client.Point(measurement_name="power_consumption")
        p = p.tag("eqpt", self.monitored_equipment_name)
        p = p.tag("source", 'bridged_SDM630')
        
        # Decides how much data to collect
        if verbosity is None:
            if self.data_sent_counter % 50 == 0:
                verbosity = 10
            elif self.data_sent_counter % 10 == 0:
                verbosity = 6
            else:
                verbosity = 3
        assert 1<=verbosity<=10
        
        start_time = time.time()
        
        if verbosity>=3:
            voltages = self.SDM630_client.get_voltages_V()
            p = p.field("Voltage A-N (V)",voltages[0])
            p = p.field("Voltage B-N (V)",voltages[1])
            p = p.field("Voltage C-N (V)",voltages[2])
        
        if verbosity>=5:
            ll_voltages = self.SDM630_client.get_line_to_line_volts()
            p = p.field("Voltage A-B (V)",ll_voltages[0])
            p = p.field("Voltage B-C (V)",ll_voltages[1])
            p = p.field("Voltage C-A (V)",ll_voltages[2])
        
        if verbosity>=3:
            currents = self.SDM630_client.get_currents_A()
            p = p.field("Current A (A)",currents[0])
            p = p.field("Current B (A)",currents[1])
            p = p.field("Current C (A)",currents[2])

        if verbosity>=1:
            powers = self.SDM630_client.get_powers_kW()
            p = p.field("Active Power A (kW)",powers[0])
            p = p.field("Active Power B (kW)",powers[1])
            p = p.field("Active Power C (kW)",powers[2])
        
        if verbosity>=8:
            powers_VA = self.SDM630_client.get_voltamps_VA()
            p = p.field("Apparent Power A (kVA)",powers_VA[0]/1000)
            p = p.field("Apparent Power B (kVA)",powers_VA[1]/1000)
            p = p.field("Apparent Power C (kVA)",powers_VA[2]/1000)
        
        if verbosity>=7:
            powers_VAR = self.SDM630_client.get_reactive_power_VAr()
            p = p.field("Reactive Power A (kVAr)",powers_VAR[0]/1000)
            p = p.field("Reactive Power B (kVAr)",powers_VAR[1]/1000)
            p = p.field("Reactive Power C (kVAr)",powers_VAR[2]/1000)
        
        if verbosity>=8:
            power_factors = self.SDM630_client.get_power_factors()
            p = p.field("Power Factor A",power_factors[0])
            p = p.field("Power Factor B",power_factors[1])
            p = p.field("Power Factor C",power_factors[2])
            p = p.field("Power Factor Total",self.SDM630_client.get_total_system_power_factor())
        
        if verbosity>=8:
            phase_angles = self.SDM630_client.get_phase_angles()
            p = p.field("Phase Angle A (deg)",phase_angles[0])
            p = p.field("Phase Angle B (deg)",phase_angles[1])
            p = p.field("Phase Angle C (deg)",phase_angles[2])
            p = p.field("Phase Angle Total (deg)",self.SDM630_client.get_total_system_phase_angle())
            
        if verbosity>=6:
            current_THDs = self.SDM630_client.get_current_THDs()
            p = p.field("THD Current A (%)",current_THDs[0])
            p = p.field("THD Current B (%)",current_THDs[1])
            p = p.field("THD Current C (%)",current_THDs[2])
    
        if verbosity>=6:
            voltage_THDs = self.SDM630_client.get_voltage_THDs()
            p = p.field("THD Voltage A-N (%)",voltage_THDs[0])
            p = p.field("THD Voltage B-N (%)",voltage_THDs[1])
            p = p.field("THD Voltage C-N (%)",voltage_THDs[2])

        if verbosity>=8:
            p = p.field("Current Sum (A)",self.SDM630_client.get_current_sum_A())
            p = p.field("Current Avg (A)",self.SDM630_client.get_average_current_A())
            p = p.field("Current N (A)",self.SDM630_client.get_neutral_current_A())

        if verbosity>=8:
            p = p.field("Total Export (kWh)",self.SDM630_client.get_total_export_kWh())
            p = p.field("Demand Power Total (kW)",self.SDM630_client.get_total_power_demand_W()/1000)
            p = p.field("Frequency (Hz)",self.SDM630_client.get_supply_frequency_Hz())
        
        if verbosity>=2:
            p = p.field("Active Power Total (kW)",self.SDM630_client.get_total_system_power_W()/1000)
            p = p.field("Total Import (kWh)",self.SDM630_client.get_total_import_kWh())
        
        if verbosity>=8:
            p = p.field("PW11 read successes",self.SDM630_client.counters['read_success'])
            p = p.field("PW11 read failures",self.SDM630_client.counters['read_failure'])
            p = p.field("PW11 reboots",self.SDM630_client.counters['reboot'])
            p = p.field("PW11 connection successes",self.SDM630_client.counters['connect_success'])
            p = p.field("PW11 connection failures",self.SDM630_client.counters['connect_failure'])
            p = p.field("PW11 connection drops",self.SDM630_client.counters['connection_dropped'])

        if verbosity>=8:
            try:
                p = p.field("PW11 wifi RSSI",self.SDM630_client.get_PW11_wifi_rssi())
            except:
                 # This will error when the PW11 web port is not available
                pass

        # Checks whether data is worthy to be uploaded to cloud
        data_time_span_s = time.time() - start_time
        if data_time_span_s > 10:
            logger.info(f'Took too long ({data_time_span_s:.2f}s) to fetch data so data time range spans too long. Discarding data.')
            return
        
        
        self.influxdb_write_api.write(bucket=influxdb_bucket_name, org=influxdb_org_id, record=p)
        self.data_sent_counter += 1
        
        # Prints to log
        print_log = False
        if self.data_sent_counter < 20:
            print_log = True
        elif (20 <= self.data_sent_counter < 200) and self.data_sent_counter % 20 == 0:
            print_log = True
        elif (200 <= self.data_sent_counter < 5000) and self.data_sent_counter % 100 == 0:
            print_log = True
        elif (5000 <= self.data_sent_counter) and self.data_sent_counter % 500 == 0:
            print_log = True
        if print_log:
            logger.debug(f'Successfully sent data {self.data_sent_counter} (collection took {data_time_span_s:.2f}s) to InfluxDB server.')

    def periodic_data_fetching(self,period_s=5):
        while script_is_active:
            if eqptnames_active.get(self.monitored_equipment_name) is False:
                logger.info(f'Thread for {self.monitored_equipment_name} received notice to stop. Stopping InfluxDBEastronWatcher.')
                break
            last_data_collection_time = time.time()
            with lock: # Lets the main thread know that we are still active
                last_active_epoches[self.monitored_equipment_name] = last_data_collection_time
            self.fetch_data()
            while (time.time() - last_data_collection_time < period_s) and (script_is_active) and (eqptnames_active.get(self.monitored_equipment_name,False)):
                time.sleep(0.1)
        logger.debug('Periodic fetching ended.')
        
def thread_starter(monitored_equipment_name,SDM630_IP_address,SDM630_port,period_s=5):
    watcher_PHR_M290 = InfluxDBEastronWatcher(monitored_equipment_name=monitored_equipment_name,SDM630_IP_address=SDM630_IP_address,SDM630_port=SDM630_port)
    watcher_PHR_M290.periodic_data_fetching() # Will continuously poll
    
    return # This line will not be reached

def sigterm_handler(signum, frame):
    global script_is_active
    logger.debug('Sigterm signal received. Propagating signal to all other threads.')
    script_is_active = False

if __name__ == '__main__':
    
    eqpts = {}
    eqpts['EOS_SI3654'] = ('***************',8899)  # PW11-01, 28:9c:6e:12:14:4e
    eqpts['sieve_shaker'] = ('***************', 8899)  # PW11-02, e8:fd:f8:3a:de:d4
    #eqpts['wet_sep'] = ('***************', 8899)  # PW11-03, e8:fd:f8:3a:e9:98, discarded since it appears to be damaged by dropping
    eqpts['air_dryer'] = ('***************', 8899)  # PW11-04, e8:fd:f8:46:82:48
    eqpts['EOS_SI2373'] = ('***************', 8899)  # PW11-05, d4:27:87:42:0b:2e

    threads = {}
    for eqpt, (ip, port) in eqpts.items():
        threads[eqpt] = threading.Thread(target=thread_starter, args=(eqpt, ip, port))
        eqptnames_active[eqpt] = True  # Mark the equipment as active
        threads[eqpt].start()
    
    signal.signal(signal.SIGTERM, sigterm_handler)
    signal.signal(signal.SIGINT, sigterm_handler)

    # Have the main thread monitor the last active times
    while script_is_active:
        with lock:
            current_time = time.time()
            for eqpt, last_active_epoch in last_active_epoches.items():
                if current_time - last_active_epoch > 600 * 99999: # Skipping the logic as this seems to cause periods of InfluxDB inactivity of 300 to 600s
                    logger.debug(f"last_active_epoches={last_active_epoches}")
                    logger.debug(f"Thread of {eqpt} last active more than 600s ago. Current thread count is {threading.active_count()} . Restarting...")
                    #threads[eqpt].join(timeout=1)  # Terminates the thread
                    if threads[eqpt].is_alive():
                        if eqptnames_active[eqpt] is True:
                            eqptnames_active[eqpt] = False  # Mark the equipment as active
                            time_of_termination = time.time()
                        else:
                            time.sleep(1) # Still waiting for the thread to die
                    else:
                        threads[eqpt] = threading.Thread(target=thread_starter, args=(eqpt, eqpts[eqpt][0], eqpts[eqpt][1]))
                        threads[eqpt].start()
                        eqptnames_active[eqpt] = True
                    time.sleep(60) # So that there is no flood of restarts
                else:
                    #logger.debug(f"{eqpt} last active {current_time - last_active_epoch:.2f}s ago.")
                    pass
        time.sleep(1)  # Adjust the sleep time as needed