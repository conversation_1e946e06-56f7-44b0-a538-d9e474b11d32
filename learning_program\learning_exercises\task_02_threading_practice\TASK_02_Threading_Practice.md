# 🎯 Task 2: Multi-Sensor Threading Practice

## **Your Mission**
Create a multi-sensor monitoring system using threading to simulate monitoring multiple temperature sensors simultaneously. This builds on Task 1 and introduces threading concepts needed for the actual project.

---

## **What You'll Learn**

✅ **Threading basics** - Running multiple tasks simultaneously  
✅ **Thread management** - Starting, stopping, organizing threads  
✅ **Shared data** - How threads communicate safely  
✅ **Real-world simulation** - Multiple sensors like in actual manufacturing  
✅ **Performance benefits** - Why threading matters for sensor monitoring  

---

## **Background: Why Threading?**

In real manufacturing environments:
- You have **multiple machines** to monitor (CNC, 3D printers, sensors)
- Each machine responds at **different speeds**
- You can't wait for one slow machine to block monitoring others
- Data needs to be collected **simultaneously** for accurate timestamping

**Without Threading (Sequential):**
```
Sensor A ────[5s]────> Sensor B ────[3s]────> Sensor C ────[2s]────>
Total time: 10 seconds for one reading cycle
```

**With Threading (Parallel):**
```
Sensor A ────[5s]────>
Sensor B ──[3s]──>
Sensor C ─[2s]─>
Total time: 5 seconds for one reading cycle (much faster!)
```

---

## **Step-by-Step Instructions**

### **Step 1: Copy Required Files**
1. Copy `lib_loggers.py` from task_01 into this folder
2. This ensures your threading code can use the same logging system

### **Step 2: Create Multi-Sensor Configuration**
Create `config.yaml` with multiple sensor definitions:

```yaml
development:
  sensors:
    Temperature_Sensor_A:
      min_temp: 18.0
      max_temp: 35.0
      response_time: 2
      failure_rate: 0.03
    Temperature_Sensor_B:
      min_temp: 15.0
      max_temp: 40.0
      response_time: 1
      failure_rate: 0.02
    Temperature_Sensor_C:
      min_temp: 20.0
      max_temp: 30.0
      response_time: 3
      failure_rate: 0.01
    Pressure_Sensor_1:
      min_temp: 10.0
      max_temp: 50.0
      response_time: 4
      failure_rate: 0.05
  monitoring:
    duration_seconds: 30
    check_interval: 0.5

production:
  sensors:
    Temperature_Sensor_A:
      min_temp: 16.0
      max_temp: 32.0
      response_time: 1
      failure_rate: 0.01
    Temperature_Sensor_B:
      min_temp: 14.0
      max_temp: 38.0
      response_time: 1
      failure_rate: 0.01
  monitoring:
    duration_seconds: 300
    check_interval: 2.0
```

### **Step 3: Create Your Practice Script**
Create `multi_sensor_monitor.py` with this basic structure:

```python
import threading
import time
import random
import yaml
from lib_loggers import set_logger

# Load configuration
try:
    with open("config.yaml") as config_file:
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The config.yaml file should be present.')

logger = set_logger()

def simulate_sensor_reading(min_temp, max_temp):
    """Simulate reading from a sensor"""
    # Generate a random temperature within sensor range
    temperature = random.uniform(min_temp, max_temp)
    return round(temperature, 1)

def check_temperature_status(temp, min_temp, max_temp):
    """Check if temperature is in acceptable range (reuse from Task 1)"""
    # TODO: Implement using same logic as Task 1
    pass

def monitor_single_sensor(sensor_name, sensor_config, duration):
    """Monitor one sensor for specified duration"""
    logger.info(f"Starting monitoring for {sensor_name}")
    
    # Extract sensor configuration
    min_temp = sensor_config['min_temp']
    max_temp = sensor_config['max_temp']
    response_time = sensor_config['response_time']
    failure_rate = sensor_config['failure_rate']
    
    start_time = time.time()
    reading_count = 0
    
    # TODO: Implement sensor monitoring loop
    # 1. Run for specified duration
    # 2. Simulate sensor response time with time.sleep(response_time)
    # 3. Check for sensor failures using failure_rate
    # 4. Generate and log temperature readings
    # 5. Track statistics
    
    logger.info(f"Completed monitoring for {sensor_name}: {reading_count} readings")
    return reading_count

def main():
    env_choice = 'development'  # Try changing to 'production'
    config_env = config[env_choice]
    
    logger.info("Starting multi-sensor monitoring system...")
    
    # Get monitoring configuration
    duration = config_env['monitoring']['duration_seconds']
    sensors = config_env['sensors']
    
    logger.info(f"Monitoring {len(sensors)} sensors for {duration} seconds")
    
    # TODO: Implement threading
    # 1. Create a dictionary to store threads
    # 2. Create one thread per sensor
    # 3. Start all threads simultaneously
    # 4. Wait for all threads to complete
    # 5. Log summary statistics
    
    logger.info("Multi-sensor monitoring completed")

if __name__ == "__main__":
    main()
```

### **Step 4: Your Coding Tasks**

1. **Complete `check_temperature_status()`**:
   - Reuse logic from Task 1
   - Return "COLD", "HOT", or "NORMAL"

2. **Complete `monitor_single_sensor()`**:
   - Run monitoring loop for specified duration
   - Simulate realistic response times with `time.sleep()`
   - Handle sensor failures randomly
   - Log readings with sensor name prefix
   - Return count of successful readings

3. **Implement threading in `main()`**:
   - Create one thread per sensor using `threading.Thread()`
   - Store threads in a dictionary for management
   - Start all threads simultaneously
   - Use `thread.join()` to wait for completion

### **Step 5: Expected Behavior**

**Console Output (threads running simultaneously):**
```
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting multi-sensor monitoring system...
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Monitoring 4 sensors for 30 seconds
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting monitoring for Temperature_Sensor_A
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting monitoring for Temperature_Sensor_B
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting monitoring for Temperature_Sensor_C
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting monitoring for Pressure_Sensor_1
2025-07-31 14:30:16    multi_sensor_monitor    12345    DEBUG    Temperature_Sensor_B: 28.1°C - Status: NORMAL
2025-07-31 14:30:17    multi_sensor_monitor    12345    WARNING  Temperature_Sensor_A: 19.2°C - Status: COLD  
2025-07-31 14:30:17    multi_sensor_monitor    12345    DEBUG    Temperature_Sensor_B: 29.3°C - Status: NORMAL
2025-07-31 14:30:18    multi_sensor_monitor    12345    ERROR    Temperature_Sensor_C: 31.7°C - Status: HOT
2025-07-31 14:30:19    multi_sensor_monitor    12345    CRITICAL Pressure_Sensor_1: Sensor failure detected!
...
```

**Notice:** Messages from different sensors appear interleaved because threads run simultaneously!

---

## **Bonus Challenges** 🏆

### **Challenge 1: Thread-Safe Data Collection**

#### **The Business Problem: We Need Centralized Data Analysis**

**Current Issue:** Right now, each sensor thread runs independently and only logs individual readings. But in real manufacturing, you need to:

- **Compare sensors**: "Is Sensor A consistently hotter than Sensor B?"
- **Generate reports**: "What was the average temperature across all sensors during the last shift?"
- **Detect patterns**: "Do all sensors spike at the same time? (indicating external heat source)"
- **Quality control**: "Which sensors had the most failures this week?"

**Real-World Example:**
```
Manufacturing Manager: "The parts from Machine A are failing quality tests. 
I need to see if all temperature sensors were within spec during production."

Current System: "Sorry, I can only show you individual sensor logs..."
Improved System: "Here's a comprehensive report showing all 4 sensors were 
running 3°C above normal between 2-4 PM when those parts were made."
```

#### **What is Thread-Safe Data Collection?**

**The Goal:** Collect ALL sensor readings into a central database that can be analyzed together.

**The Challenge:** Multiple threads trying to write to the same data structure simultaneously can corrupt the data.

**The Solution:** Use locks to ensure only one thread writes data at a time.

**What We're Building:**
```python
# This shared data structure will contain ALL sensor readings:
shared_data = {
    'Temperature_Sensor_A': [
        {'timestamp': 1627834567.1, 'temperature': 25.4, 'status': 'NORMAL'},
        {'timestamp': 1627834569.2, 'temperature': 26.1, 'status': 'NORMAL'},
    ],
    'Temperature_Sensor_B': [
        {'timestamp': 1627834567.3, 'temperature': 31.2, 'status': 'HOT'},
        {'timestamp': 1627834568.1, 'temperature': 30.8, 'status': 'NORMAL'},
    ],
    # ... all other sensors
}
```

**Why This Matters:**
- **Cross-sensor analysis**: Compare readings between sensors
- **Comprehensive reporting**: Generate shift summaries and quality reports
- **Pattern detection**: Identify system-wide issues
- **Data export**: Send complete datasets to quality control systems

#### **Implementation Steps:**

**Step 1: Add Shared Data Structure (after imports)**
```python
# Central database for all sensor readings
shared_data = {}
data_lock = threading.Lock()  # Prevents data corruption
```

**Step 2: Create Thread-Safe Update Function (after simulate_sensor_reading)**
```python
def update_sensor_data(sensor_name, reading, status):
    """Thread-safe function to store sensor readings in central database"""
    with data_lock:  # Only one thread can write at a time
        if sensor_name not in shared_data:
            shared_data[sensor_name] = []
        shared_data[sensor_name].append({
            'timestamp': time.time(),
            'temperature': reading,
            'status': status
        })
```

**Step 3: Modify monitor_single_sensor() (in the else block where you log readings)**
```python
# In your existing else block, add this line after status calculation:
update_sensor_data(sensor_name, temp, status)  # Store in central database
```

**Step 4: Add Comprehensive Report Generation (after monitor_single_sensor)**
```python
def generate_report():
    """Generate comprehensive cross-sensor analysis report"""
    with data_lock:
        logger.info("=== COMPREHENSIVE SENSOR ANALYSIS REPORT ===")
        
        all_temps = []
        total_readings = 0
        total_failures = 0
        
        for sensor_name, readings in shared_data.items():
            if readings:
                temps = [r['temperature'] for r in readings]
                avg_temp = sum(temps) / len(temps)
                min_temp = min(temps)
                max_temp = max(temps)
                statuses = [r['status'] for r in readings]
                normal_count = statuses.count('NORMAL')
                
                logger.info(f"{sensor_name}: {len(readings)} readings, avg: {avg_temp:.1f}°C, range: {min_temp:.1f}-{max_temp:.1f}°C, normal: {normal_count}/{len(readings)}")
                
                all_temps.extend(temps)
                total_readings += len(readings)
                total_failures += (len(readings) - normal_count)
            else:
                logger.info(f"{sensor_name}: No successful readings")
        
        # Cross-sensor analysis
        if all_temps:
            system_avg = sum(all_temps) / len(all_temps)
            system_min = min(all_temps)
            system_max = max(all_temps)
            failure_rate = (total_failures / total_readings) * 100 if total_readings > 0 else 0
            
            logger.info("=== SYSTEM-WIDE ANALYSIS ===")
            logger.info(f"Overall average temperature: {system_avg:.1f}°C")
            logger.info(f"System temperature range: {system_min:.1f}°C to {system_max:.1f}°C")
            logger.info(f"Total readings collected: {total_readings}")
            logger.info(f"System failure rate: {failure_rate:.1f}%")
```

**Step 5: Call Report in main() (before final log message)**
```python
# Generate comprehensive analysis report
generate_report()
```

---

### **Challenge 2: Graceful Shutdown**

#### **The Business Problem: Production Systems Can't Just "Crash"**

**Current Issue:** When you press Ctrl+C, your program immediately terminates, potentially:

**Real-World Consequences:**
- **Data loss**: "We lost 2 hours of sensor data because the monitoring crashed during a shift change"
- **Incomplete reports**: "The quality report is missing the last 30 minutes of production"
- **Resource leaks**: "The database connection is still open, blocking other systems"
- **Operator confusion**: "Did the monitoring stop because of an error or intentionally?"

**Manufacturing Reality:**
```
Shift Supervisor: "I need to restart the monitoring system for maintenance."
Current System: *CRASH* - All data lost, connections left open
Improved System: "Gracefully stopping... saving final reports... 
                 all sensors stopped cleanly... system ready for maintenance."
```

#### **What is Graceful Shutdown?**

**The Goal:** When shutdown is requested, allow all threads to finish their current work and save all data before exiting.

**The Process:**
1. **Signal all threads** to stop after current operation
2. **Wait for threads** to complete their current sensor readings
3. **Generate final reports** with all collected data
4. **Close resources** properly (files, database connections)
5. **Confirm clean shutdown** to operators

#### **Implementation Steps:**

**Step 1: Add Signal Handling Imports (add to existing imports)**
```python
import signal
import sys
```

**Step 2: Add Shutdown Coordination (after data_lock declaration)**
```python
# Global shutdown flag that all threads can check
shutdown_flag = threading.Event()

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully - like a professional shutdown button"""
    logger.info("=== GRACEFUL SHUTDOWN REQUESTED ===")
    logger.info("Signaling all sensors to complete current readings...")
    shutdown_flag.set()  # Tell all threads to stop
    logger.info("Please wait while system shuts down cleanly...")
```

**Step 3: Register Signal Handler (in main(), before creating threads)**
```python
# Set up graceful shutdown handling
signal.signal(signal.SIGINT, signal_handler)  # Handle Ctrl+C
logger.info("Monitoring system ready. Press Ctrl+C for graceful shutdown.")
```

**Step 4: Modify Sensor Loop (in monitor_single_sensor(), change while condition)**
```python
# Change your while loop to respect shutdown requests:
while not shutdown_flag.is_set() and elapsed_time < duration:
    # ... existing monitoring code ...
    
    # Check for shutdown request at end of each cycle
    if shutdown_flag.is_set():
        logger.info(f"{sensor_name}: Received shutdown signal, stopping gracefully")
        break
```

**Step 5: Add Graceful Thread Waiting (in main(), modify join logic)**
```python
# Replace your existing join loop with graceful waiting:
try:
    for sensor_name, thread in threads.items():
        logger.info(f"Waiting for {sensor_name} to finish current reading...")
        thread.join(timeout=10.0)  # Wait max 10 seconds per thread
        if thread.is_alive():
            logger.warning(f"{sensor_name} did not stop gracefully (may be stuck)")
        else:
            logger.info(f"{sensor_name} stopped cleanly")
            
    logger.info("All sensors stopped. Generating final reports...")
    generate_report()  # Save all collected data
    logger.info("=== SYSTEM SHUTDOWN COMPLETE ===")
    
except KeyboardInterrupt:
    logger.error("Force shutdown requested - some data may be lost")
```

---

### **Challenge 3: Dynamic Thread Management**

#### **The Business Problem: 24/7 Manufacturing Needs Self-Healing Systems**

**Current Issue:** In real manufacturing, systems run 24/7 for weeks. Static threads can't handle:

**Real-World Scenarios:**
- **Network hiccups**: "Sensor B lost connection for 30 seconds, but monitoring didn't recover"
- **Sensor malfunctions**: "Temperature sensor C has been stuck at 0°C for 2 hours, but nobody noticed"
- **Performance issues**: "Sensor A is responding slowly, affecting the whole system"
- **Maintenance needs**: "We need real-time status to know which sensors are healthy"

**Manufacturing Reality:**
```
Night Shift Operator: "The monitoring dashboard shows Sensor C hasn't 
                      updated in 5 minutes. Is it broken?"
Current System: "I don't know, check the logs..."
Improved System: "Sensor C marked as FAILED - automatic restart attempted. 
                 Status: RECOVERED. All systems normal."
```

#### **What is Dynamic Thread Management?**

**The Goal:** Create a self-monitoring system that:
- **Tracks thread health** in real-time
- **Detects stuck or failed threads** automatically  
- **Provides live status dashboard** for operators
- **Attempts automatic recovery** when possible
- **Alerts operators** to persistent issues

**The Management Dashboard:**
```
=== REAL-TIME THREAD STATUS ===
Temperature_Sensor_A: RUNNING (Thread-12345) - Last reading: 25.4°C (NORMAL)
Temperature_Sensor_B: RUNNING (Thread-12346) - Last reading: 31.1°C (NORMAL)  
Temperature_Sensor_C: FAILED (Thread-12347) - No updates for 10+ seconds
Pressure_Sensor_1: RUNNING (Thread-12348) - Last reading: 44.4°C (NORMAL)
```

#### **Implementation Steps:**

**Step 1: Add Thread Status Tracking (after shutdown_flag)**
```python
# Real-time thread health monitoring system
thread_status = {}
status_lock = threading.Lock()

def update_thread_status(sensor_name, status, message=""):
    """Update thread status for real-time monitoring dashboard"""
    with status_lock:
        thread_status[sensor_name] = {
            'status': status,  # 'STARTING', 'RUNNING', 'FAILED', 'COMPLETED'
            'message': message,
            'last_update': time.time(),
            'thread_id': threading.current_thread().ident
        }
```

**Step 2: Add Health Monitoring Function (after update_thread_status)**
```python
def monitor_thread_health():
    """Continuously monitor all threads for failures and performance issues"""
    logger.info("Thread health monitor started - watching for stuck threads")
    
    while not shutdown_flag.is_set():
        with status_lock:
            current_time = time.time()
            for sensor_name, status_info in thread_status.items():
                # Detect stuck threads (no updates for 10 seconds)
                if current_time - status_info['last_update'] > 10:
                    if status_info['status'] == 'RUNNING':
                        logger.warning(f"ALERT: {sensor_name} appears stuck - no updates for 10+ seconds")
                        status_info['status'] = 'FAILED'
                        status_info['message'] = 'Thread appears stuck - no recent updates'
        
        time.sleep(2)  # Check every 2 seconds
    
    logger.info("Thread health monitor stopped")
```

**Step 3: Add Live Status Dashboard (after monitor_thread_health)**
```python
def display_status():
    """Live status dashboard for operators"""
    logger.info("Live status dashboard started - updates every 5 seconds")
    
    while not shutdown_flag.is_set():
        with status_lock:
            logger.info("=== LIVE SENSOR STATUS DASHBOARD ===")
            healthy_count = 0
            total_count = len(thread_status)
            
            for sensor_name, status_info in thread_status.items():
                status = status_info['status']
                message = status_info['message']
                thread_id = status_info['thread_id']
                
                if status == 'RUNNING':
                    healthy_count += 1
                    
                logger.info(f"{sensor_name}: {status} (Thread-{thread_id}) - {message}")
            
            if total_count > 0:
                health_percentage = (healthy_count / total_count) * 100
                logger.info(f"System Health: {healthy_count}/{total_count} sensors healthy ({health_percentage:.0f}%)")
        
        # Wait 5 seconds or until shutdown
        shutdown_flag.wait(5)
    
    logger.info("Status dashboard stopped")
```

**Step 4: Modify Sensor Function (add status updates in monitor_single_sensor)**
```python
# Add at start of monitor_single_sensor():
update_thread_status(sensor_name, 'STARTING', 'Initializing sensor monitoring')

# Add after logger.info(f"Starting monitoring for {sensor_name}"):
update_thread_status(sensor_name, 'RUNNING', 'Monitoring active - collecting data')

# Add in the monitoring loop (after successful reading):
update_thread_status(sensor_name, 'RUNNING', f'Last reading: {temp}°C ({status})')

# Add when sensor fails:
update_thread_status(sensor_name, 'RUNNING', f'Sensor failure detected - continuing monitoring')

# Add at end before return:
update_thread_status(sensor_name, 'COMPLETED', f'Monitoring finished - {reading_count} readings collected')
```

**Step 5: Add Management Threads (in main(), after creating sensor threads)**
```python
# Start management and monitoring threads
management_threads = {}

# Health monitor thread - watches for stuck/failed threads
health_thread = threading.Thread(target=monitor_thread_health, name="HealthMonitor")
management_threads['health'] = health_thread
health_thread.start()

# Status display thread - provides live dashboard for operators
status_thread = threading.Thread(target=display_status, name="StatusDisplay")
management_threads['status'] = status_thread
status_thread.start()

logger.info("Management threads started - system is now self-monitoring")

# ... existing sensor thread code ...

# Gracefully stop management threads (add after sensor thread joins)
logger.info("Stopping management threads...")
for name, thread in management_threads.items():
    logger.info(f"Stopping {name} thread...")
    thread.join(timeout=3)
    if thread.is_alive():
        logger.warning(f"{name} thread did not stop gracefully")
```

**Why This Matters for Manufacturing:**
- **Proactive monitoring**: Detect issues before they cause production problems
- **Operator visibility**: Real-time dashboard shows system health at a glance
- **Automatic recovery**: System can detect and restart failed monitoring
- **Performance tracking**: Monitor how well each sensor is performing
- **Maintenance planning**: Identify sensors that frequently fail or respond slowly

---

## **Key Threading Concepts You'll Practice**

### **1. Creating and Starting Threads**
```python
import threading

def worker_function(name, duration):
    print(f"Worker {name} starting")
    time.sleep(duration)
    print(f"Worker {name} finished")

# Create thread
thread = threading.Thread(target=worker_function, args=("Sensor1", 5))

# Start thread (non-blocking)
thread.start()

# Wait for thread to complete (blocking)
thread.join()
```

### **2. Managing Multiple Threads**
```python
threads = {}

# Start multiple threads
for sensor_name, sensor_config in sensors.items():
    thread = threading.Thread(
        target=monitor_single_sensor, 
        args=(sensor_name, sensor_config, duration)
    )
    threads[sensor_name] = thread
    thread.start()

# Wait for all threads to complete
for sensor_name, thread in threads.items():
    thread.join()
    logger.info(f"Thread for {sensor_name} completed")
```

### **3. Thread-Safe Data Sharing**
```python
import threading

# Global shared data
sensor_readings = {}
readings_lock = threading.Lock()

def add_reading(sensor_name, reading):
    with readings_lock:  # Only one thread can access at a time
        if sensor_name not in sensor_readings:
            sensor_readings[sensor_name] = []
        sensor_readings[sensor_name].append(reading)
```

---

## **Real-World Connection**

This exercise directly prepares you for the actual project:

**Power Meter Project Pattern:**
```python
# From main.py - Multiple equipment monitored simultaneously
threads = {}
for eqpt, (ip, port) in eqpts.items():
    threads[eqpt] = threading.Thread(target=thread_starter, args=(eqpt, ip, port))
    threads[eqpt].start()

# Wait for all monitoring threads
for thread in threads.values():
    thread.join()
```

**Benefits you'll understand:**
- Multiple OPC-UA clients connect to different machines simultaneously
- Each machine has different response times and doesn't block others
- Parallel data collection ensures accurate timestamps
- System continues working even if individual machines fail
- Centralized logging tracks all activities across threads

---

## **Success Criteria**

✅ **Basic Implementation:**
- Multiple sensors monitored simultaneously
- Different response times simulated correctly
- Proper logging with thread identification
- All threads complete successfully

✅ **Advanced Implementation:**
- Thread-safe data collection
- Graceful shutdown handling
- Comprehensive statistics reporting
- Error handling for thread failures

✅ **Professional Implementation:**
- Clean code organization
- Configurable behavior through YAML
- Robust error handling
- Production-ready patterns

---

## **Getting Help**

- Check that all sensors start logging simultaneously (not sequentially)
- Monitor the logs folder for the output file
- Compare timing with single-threaded vs multi-threaded approaches
- Remember: Threading is about concurrency, not necessarily speed

**Ready to master threading? Good luck! 🚀**
