"""
Phase 3: Industrial Monitoring System

Learning Goals:
- Build complete multi-machine monitoring system
- Integrate async OPC UA with InfluxDB
- Implement robust error handling and recovery
- Create production-ready monitoring patterns

This phase integrates all previous learning into a realistic industrial system.
"""

import asyncio
import yaml
from datetime import datetime, timezone
from asyncua import Client, Node
from asyncua.common.subscription import DataChangeNotif
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import ASYNCHRONOUS
import logging
from typing import Dict, List, Any

class IndustrialMonitoringSystem:
    """
    Complete industrial monitoring system using async patterns.
    
    Learning Goal: Build production-ready monitoring systems
    Real Project Pattern: Integration of all project components
    """
    
    def __init__(self, config):
        """
        Initialize industrial monitoring system.
        
        TODO: Set up multi-machine monitoring infrastructure
        """
        self.config = config
        self.machines = {}
        self.monitoring_tasks = {}
        self.influx_client = None
        self.write_api = None
        self.running = False
        self.data_processors = {}
        self.system_stats = {
            'total_data_points': 0,
            'successful_writes': 0,
            'failed_writes': 0,
            'connection_errors': 0,
            'start_time': None
        }
        
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        print("=== INDUSTRIAL MONITORING SYSTEM ===")
        print("Goal: Build production-ready multi-machine monitoring\n")
        
    async def initialize_system(self):
        """
        Initialize all system components.
        
        TODO: Implement complete system initialization
        Real Project Pattern: Initialization from main.py files
        """
        print("Initializing industrial monitoring system...")
        
        # TODO: Initialize InfluxDB client
        await self.initialize_influxdb()
        
        # TODO: Initialize machine configurations
        await self.initialize_machines()
        
        # TODO: Initialize data processors
        await self.initialize_data_processors()
        
        print("✅ System initialization complete")
        
    async def initialize_influxdb(self):
        """
        Initialize InfluxDB client with production settings.
        
        TODO: Implement InfluxDB initialization
        Real Project Pattern: InfluxDB setup from actual project
        """
        try:
            # TODO: Get InfluxDB configuration
            influx_config = self.config.get('development', {}).get('influxdb', {})
            
            if not influx_config:
                print("Warning: No InfluxDB configuration found")
                return
                
            # TODO: Create InfluxDB client
            self.influx_client = InfluxDBClient(
                url=influx_config.get('url', 'http://localhost:8086'),
                token=influx_config.get('token', 'your-token'),
                org=influx_config.get('org', 'your-org')
            )
            
            # TODO: Test connection
            health = self.influx_client.health()
            print(f"✅ InfluxDB connection: {health.status}")
            
            # TODO: Set up write API
            self.write_api = self.influx_client.write_api(write_options=ASYNCHRONOUS)
            
        except Exception as e:
            print(f"❌ InfluxDB initialization failed: {e}")
            self.logger.error(f"InfluxDB initialization error: {e}")
            
    async def initialize_machines(self):
        """
        Initialize machine configurations.
        
        TODO: Load machine configurations from config
        """
        machines_config = self.config.get('development', {}).get('machines', {})
        
        for machine_name, machine_config in machines_config.items():
            self.machines[machine_name] = {
                'config': machine_config,
                'client': None,
                'subscription': None,
                'data_processor': None,
                'status': 'initialized'
            }
            print(f"✅ Machine configured: {machine_name}")
            
    async def initialize_data_processors(self):
        """
        Initialize data processors for each machine.
        
        TODO: Create data processors for efficient data handling
        """
        for machine_name in self.machines.keys():
            processor = MachineDataProcessor(
                self.influx_client,
                machine_name,
                self.config.get('development', {}).get('influxdb', {})
            )
            self.data_processors[machine_name] = processor
            print(f"✅ Data processor ready: {machine_name}")
            
    async def start_monitoring(self):
        """
        Start monitoring all configured machines.
        
        TODO: Implement concurrent machine monitoring
        Learning Goal: Apply async patterns to real industrial scenarios
        """
        print("=== STARTING INDUSTRIAL MONITORING SYSTEM ===")
        
        self.running = True
        self.system_stats['start_time'] = datetime.now(timezone.utc)
        
        # TODO: Create monitoring tasks for each machine
        for machine_name, machine_info in self.machines.items():
            print(f"Starting monitoring for {machine_name}...")
            
            task = asyncio.create_task(
                self.monitor_single_machine(machine_name, machine_info)
            )
            self.monitoring_tasks[machine_name] = task
            
        # TODO: Start system health monitoring
        health_task = asyncio.create_task(self.monitor_system_health())
        self.monitoring_tasks['system_health'] = health_task
        
        # TODO: Start data flushing task
        flush_task = asyncio.create_task(self.periodic_data_flush())
        self.monitoring_tasks['data_flush'] = flush_task
        
        print(f"✅ Started {len(self.monitoring_tasks)} monitoring tasks")
        
        # TODO: Wait for all tasks or handle shutdown
        try:
            await asyncio.gather(*self.monitoring_tasks.values())
        except KeyboardInterrupt:
            print("\nShutdown requested...")
        finally:
            await self.stop_monitoring()
            
    async def monitor_single_machine(self, machine_name, machine_info):
        """
        Monitor a single machine using async OPC UA patterns.
        
        TODO: Implement robust single-machine monitoring
        Args:
            machine_name (str): Name of the machine
            machine_info (dict): Machine configuration and state
            
        Learning Goal: Apply all previous learning to real monitoring
        """
        print(f"  📡 Starting {machine_name} monitoring...")
        
        retry_count = 0
        max_retries = 5
        
        while self.running:
            try:
                # TODO: Get machine configuration
                machine_config = machine_info['config']
                opcua_config = machine_config.get('opcua', {})
                
                if not opcua_config:
                    print(f"  ❌ No OPC UA config for {machine_name}")
                    await asyncio.sleep(10)
                    continue
                    
                # TODO: Connect to machine using async OPC UA
                server_url = opcua_config.get('url', 'opc.tcp://localhost:4840/')
                
                async with Client(url=server_url) as client:
                    print(f"  ✅ Connected to {machine_name}")
                    machine_info['status'] = 'connected'
                    retry_count = 0  # Reset retry count on successful connection
                    
                    # TODO: Set up subscriptions for real-time data
                    handler = IndustrialDataHandler(
                        machine_name,
                        self.data_processors[machine_name]
                    )
                    
                    subscription = await client.create_subscription(
                        opcua_config.get('subscription_interval_ms', 1000),
                        handler
                    )
                    
                    # TODO: Subscribe to configured nodes
                    nodes_to_monitor = []
                    data_points = machine_config.get('data_points', {})
                    
                    for point_name, point_config in data_points.items():
                        try:
                            node_id = point_config.get('node_id')
                            if node_id:
                                node = client.get_node(node_id)
                                nodes_to_monitor.append(node)
                                print(f"    📊 Monitoring: {point_name}")
                        except Exception as e:
                            print(f"    ❌ Failed to add node {point_name}: {e}")
                            
                    if nodes_to_monitor:
                        await subscription.subscribe_data_change(nodes_to_monitor)
                        print(f"  ✅ Subscribed to {len(nodes_to_monitor)} data points")
                        
                        # TODO: Keep connection alive and monitor
                        while self.running:
                            await asyncio.sleep(1)
                            
                            # TODO: Check connection health
                            try:
                                await client.nodes.server_time.read_value()
                            except Exception as e:
                                print(f"  ⚠️ Connection health check failed: {e}")
                                break
                                
                    else:
                        print(f"  ❌ No valid nodes to monitor for {machine_name}")
                        await asyncio.sleep(30)
                        
            except Exception as e:
                retry_count += 1
                machine_info['status'] = 'error'
                self.system_stats['connection_errors'] += 1
                
                print(f"  ❌ Error monitoring {machine_name}: {e}")
                self.logger.error(f"Machine {machine_name} error: {e}")
                
                if retry_count >= max_retries:
                    print(f"  ❌ Max retries reached for {machine_name}")
                    await asyncio.sleep(60)  # Wait longer before trying again
                    retry_count = 0
                else:
                    wait_time = min(retry_count * 5, 30)  # Exponential backoff
                    print(f"  ⏳ Retrying {machine_name} in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                    
    async def monitor_system_health(self):
        """
        Monitor overall system health and performance.
        
        TODO: Implement system-wide health monitoring
        Learning Goal: System-level monitoring and diagnostics
        """
        print("  🏥 Starting system health monitoring...")
        
        while self.running:
            try:
                # TODO: Collect system health metrics
                health_metrics = {
                    'timestamp': datetime.now(timezone.utc),
                    'uptime_seconds': 0,
                    'active_connections': 0,
                    'data_rate_per_minute': 0,
                    'error_rate_percent': 0
                }
                
                # TODO: Calculate uptime
                if self.system_stats['start_time']:
                    uptime = datetime.now(timezone.utc) - self.system_stats['start_time']
                    health_metrics['uptime_seconds'] = uptime.total_seconds()
                    
                # TODO: Count active connections
                active_connections = sum(
                    1 for machine in self.machines.values()
                    if machine['status'] == 'connected'
                )
                health_metrics['active_connections'] = active_connections
                
                # TODO: Calculate data rate
                total_points = self.system_stats['total_data_points']
                if health_metrics['uptime_seconds'] > 0:
                    data_rate = (total_points / health_metrics['uptime_seconds']) * 60
                    health_metrics['data_rate_per_minute'] = round(data_rate, 2)
                    
                # TODO: Calculate error rate
                total_operations = (self.system_stats['successful_writes'] + 
                                  self.system_stats['failed_writes'])
                if total_operations > 0:
                    error_rate = (self.system_stats['failed_writes'] / total_operations) * 100
                    health_metrics['error_rate_percent'] = round(error_rate, 2)
                    
                # TODO: Display health status
                print(f"\n📊 System Health Report:")
                print(f"   Uptime: {health_metrics['uptime_seconds']:.0f}s")
                print(f"   Active connections: {health_metrics['active_connections']}/{len(self.machines)}")
                print(f"   Data rate: {health_metrics['data_rate_per_minute']} points/min")
                print(f"   Error rate: {health_metrics['error_rate_percent']}%")
                print(f"   Total data points: {self.system_stats['total_data_points']}")
                
                # TODO: Store health metrics in InfluxDB
                if self.influx_client:
                    await self.store_health_metrics(health_metrics)
                    
                await asyncio.sleep(30)  # Health check every 30 seconds
                
            except Exception as e:
                print(f"  ❌ Health monitoring error: {e}")
                await asyncio.sleep(60)
                
    async def periodic_data_flush(self):
        """
        Periodically flush data buffers to ensure data persistence.
        
        TODO: Implement periodic data flushing
        """
        flush_interval = self.config.get('development', {}).get('influxdb', {}).get('flush_interval_seconds', 10)
        
        while self.running:
            try:
                await asyncio.sleep(flush_interval)
                
                # TODO: Flush all data processors
                for machine_name, processor in self.data_processors.items():
                    await processor.flush_data_buffer()
                    
            except Exception as e:
                print(f"  ❌ Data flush error: {e}")
                
    async def store_health_metrics(self, metrics):
        """
        Store system health metrics in InfluxDB.
        
        TODO: Implement health metrics storage
        """
        try:
            # TODO: Create InfluxDB point for health metrics
            point = Point("system_health")
            point = point.tag("system", "industrial_monitor")
            point = point.field("uptime_seconds", metrics['uptime_seconds'])
            point = point.field("active_connections", metrics['active_connections'])
            point = point.field("data_rate_per_minute", metrics['data_rate_per_minute'])
            point = point.field("error_rate_percent", metrics['error_rate_percent'])
            point = point.time(metrics['timestamp'])
            
            # TODO: Write to InfluxDB
            if self.write_api:
                bucket = self.config.get('development', {}).get('influxdb', {}).get('bucket', 'async_manufacturing')
                self.write_api.write(bucket=bucket, record=point)
                
        except Exception as e:
            print(f"  ❌ Health metrics storage error: {e}")
            
    async def stop_monitoring(self):
        """
        Gracefully stop all monitoring operations.
        
        TODO: Implement clean shutdown
        """
        print("\n🛑 Stopping monitoring system...")
        self.running = False
        
        # TODO: Cancel all monitoring tasks
        for task_name, task in self.monitoring_tasks.items():
            print(f"  Stopping {task_name}...")
            task.cancel()
            
        # TODO: Wait for tasks to complete
        await asyncio.gather(*self.monitoring_tasks.values(), return_exceptions=True)
        
        # TODO: Close InfluxDB client
        if self.influx_client:
            self.influx_client.close()
            
        # TODO: Generate final statistics
        print("\n📊 Final System Statistics:")
        print(f"   Total data points collected: {self.system_stats['total_data_points']}")
        print(f"   Successful writes: {self.system_stats['successful_writes']}")
        print(f"   Failed writes: {self.system_stats['failed_writes']}")
        print(f"   Connection errors: {self.system_stats['connection_errors']}")
        
        print("✅ Monitoring system stopped")

class IndustrialDataHandler:
    """
    Handle incoming data from industrial machines.
    
    Learning Goal: Process real-time industrial data efficiently
    """
    
    def __init__(self, machine_name, data_processor):
        """
        Initialize data handler for a specific machine.
        
        Args:
            machine_name (str): Name of the machine
            data_processor: Data processor for this machine
        """
        self.machine_name = machine_name
        self.data_processor = data_processor
        self.data_count = 0
        
    def datachange_notification(self, node: Node, val, data: DataChangeNotif):
        """
        Handle incoming data change notifications.
        
        TODO: Process data changes efficiently
        """
        try:
            # TODO: Extract data information
            node_id = str(node.nodeid.Identifier)
            timestamp = data.monitored_item.Value.SourceTimestamp
            
            # TODO: Process data asynchronously
            asyncio.create_task(
                self.data_processor.process_data_change(node_id, val, timestamp)
            )
            
            self.data_count += 1
            
            # TODO: Log data change (limited frequency to avoid spam)
            if self.data_count % 10 == 0:
                print(f"    📈 {self.machine_name}: {self.data_count} data points processed")
                
        except Exception as e:
            print(f"    ❌ Data handling error for {self.machine_name}: {e}")

class MachineDataProcessor:
    """
    Process and store machine data efficiently.
    
    Learning Goal: Efficient data processing and storage patterns
    """
    
    def __init__(self, influx_client, machine_name, influx_config):
        """
        Initialize data processor for a specific machine.
        
        TODO: Set up data processing infrastructure
        """
        self.influx_client = influx_client
        self.machine_name = machine_name
        self.influx_config = influx_config
        self.data_buffer = []
        self.last_flush = datetime.now(timezone.utc)
        self.batch_size = influx_config.get('batch_size', 50)
        
    async def process_data_change(self, node_id, value, timestamp):
        """
        Process incoming data change from OPC UA subscription.
        
        TODO: Implement efficient data processing
        Args:
            node_id (str): OPC UA node identifier
            value: The new value
            timestamp (datetime): When the change occurred
            
        Learning Goal: Real-time data processing patterns
        """
        try:
            # TODO: Create InfluxDB data point
            point = Point("machine_data")
            point = point.tag("machine", self.machine_name)
            point = point.tag("node_id", node_id)
            point = point.field("value", float(value) if isinstance(value, (int, float)) else str(value))
            point = point.time(timestamp)
            
            # TODO: Add to buffer
            self.data_buffer.append(point)
            
            # TODO: Flush buffer if it's full
            if len(self.data_buffer) >= self.batch_size:
                await self.flush_data_buffer()
                
        except Exception as e:
            print(f"    ❌ Data processing error: {e}")
            
    async def flush_data_buffer(self):
        """
        Flush buffered data to InfluxDB.
        
        TODO: Implement efficient batch writing
        """
        if not self.data_buffer or not self.influx_client:
            return
            
        try:
            # TODO: Write buffered data to InfluxDB
            bucket = self.influx_config.get('bucket', 'async_manufacturing')
            write_api = self.influx_client.write_api()
            
            write_api.write(bucket=bucket, record=self.data_buffer)
            
            # TODO: Update statistics
            # This would be passed back to the main system
            
            # TODO: Clear buffer
            buffer_size = len(self.data_buffer)
            self.data_buffer = []
            self.last_flush = datetime.now(timezone.utc)
            
            print(f"    💾 Flushed {buffer_size} data points for {self.machine_name}")
            
        except Exception as e:
            print(f"    ❌ Data flush error for {self.machine_name}: {e}")

async def main():
    """Phase 3: Build complete industrial monitoring system"""
    print("=== PHASE 3: INDUSTRIAL MONITORING SYSTEM ===")
    print("Building production-ready monitoring systems\n")
    
    # Load configuration
    try:
        with open("config.yaml") as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print("Error: config.yaml not found. Please create configuration file.")
        return
    
    # Create and start monitoring system
    monitoring_system = IndustrialMonitoringSystem(config)
    
    try:
        # Initialize all components
        await monitoring_system.initialize_system()
        
        # Start monitoring
        await monitoring_system.start_monitoring()
        
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user")
    except Exception as e:
        print(f"Monitoring system error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
