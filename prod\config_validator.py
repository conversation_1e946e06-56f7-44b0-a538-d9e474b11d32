# config_validator.py
import logging
import os
from typing import Dict, Any, Optional

log = logging.getLogger("manufacturing_monitor.config_validator")


class ConfigValidationError(Exception):
    """Exception raised when configuration validation fails."""
    pass


class ConfigValidator:
    """Handles validation of application configuration."""
    
    def __init__(self):
        self.required_environments = ['development', 'production']
        self.required_env_sections = ['machines', 'influxdb']
        self.required_influxdb_fields = ['url', 'token', 'org', 'bucket']
        self.supported_protocols = ['opcua']  # , 'modbus' when implemented
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate the complete configuration structure.
        
        Args:
            config: The loaded configuration dictionary
            
        Returns:
            bool: True if configuration is valid
            
        Raises:
            ConfigValidationError: If validation fails
        """
        try:
            log.info("Validating configuration structure...")
            
            self._validate_environment_sections(config)
            
            environment = self._get_current_environment()
            env_config = self._validate_environment_config(config, environment)
            
            self._validate_environment_sections_exist(env_config, environment)
            self._validate_machines_config(env_config['machines'])
            self._validate_influxdb_config(env_config['influxdb'])
            
            machines_count = len(env_config['machines']) if env_config['machines'] else 0
            log.info(f"Configuration validation completed successfully for {machines_count} machines")
            return True
            
        except ConfigValidationError:
            raise
        except Exception as e:
            error_msg = f"Error during configuration validation: {e}"
            log.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    def _validate_environment_sections(self, config: Dict[str, Any]) -> None:
        """Validate that required top-level environment sections exist."""
        for section in self.required_environments:
            if section not in config:
                error_msg = f"Missing required configuration section: '{section}'"
                log.error(error_msg)
                raise ConfigValidationError(error_msg)
    
    def _get_current_environment(self) -> str:
        """Get the current environment from environment variable."""
        return os.getenv('ENVIRONMENT', 'development').lower()
    
    def _validate_environment_config(self, config: Dict[str, Any], environment: str) -> Dict[str, Any]:
        """Validate that the current environment exists in config."""
        if environment not in config:
            error_msg = f"Environment '{environment}' not found in configuration"
            log.error(error_msg)
            log.info(f"Available environments: {list(config.keys())}")
            raise ConfigValidationError(error_msg)
        
        log.info(f"Using environment: {environment}")
        return config[environment]
    
    def _validate_environment_sections_exist(self, env_config: Dict[str, Any], environment: str) -> None:
        """Validate that required sections exist in environment config."""
        for section in self.required_env_sections:
            if section not in env_config:
                error_msg = f"Missing required section '{section}' in {environment} configuration"
                log.error(error_msg)
                raise ConfigValidationError(error_msg)
    
    def _validate_machines_config(self, machines: Optional[Dict[str, Any]]) -> None:
        """Validate machines configuration."""
        if not machines:
            log.warning("No machines configured for monitoring")
            return  # This might be OK for testing
        
        for machine_name, machine_config in machines.items():
            self._validate_single_machine_config(machine_name, machine_config)
    
    def _validate_single_machine_config(self, machine_name: str, machine_config: Dict[str, Any]) -> None:
        """
        Validate individual machine configuration.
        
        Args:
            machine_name: Name of the machine
            machine_config: Machine configuration dictionary
            
        Raises:
            ConfigValidationError: If validation fails
        """
        try:
            self._validate_machine_type(machine_name, machine_config)
            
            protocol = machine_config.get('protocol', 'opcua')
            self._validate_protocol_support(machine_name, protocol)
            
            if protocol == 'opcua':
                self._validate_opcua_machine_config(machine_name, machine_config)
            # elif protocol == 'modbus':
            #     self._validate_modbus_machine_config(machine_name, machine_config)
            
            log.debug(f"Machine '{machine_name}' ({protocol.upper()}) configuration is valid")
            
        except ConfigValidationError:
            raise
        except Exception as e:
            error_msg = f"Error validating machine '{machine_name}' configuration: {e}"
            log.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    def _validate_machine_type(self, machine_name: str, machine_config: Dict[str, Any]) -> None:
        """Validate that machine has a type field."""
        if 'type' not in machine_config:
            error_msg = f"Machine '{machine_name}' missing required field: 'type'"
            log.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    def _validate_protocol_support(self, machine_name: str, protocol: str) -> None:
        """Validate that the protocol is supported."""
        if protocol not in self.supported_protocols:
            error_msg = f"Machine '{machine_name}' has unsupported protocol: '{protocol}'. Supported: {', '.join(self.supported_protocols)}"
            log.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    def _validate_opcua_machine_config(self, machine_name: str, machine_config: Dict[str, Any]) -> None:
        """Validate OPC UA specific machine configuration."""
        required_sections = ['opcua', 'nodes', 'monitoring']
        for section in required_sections:
            if section not in machine_config:
                error_msg = f"OPC UA machine '{machine_name}' missing required section: '{section}'"
                log.error(error_msg)
                raise ConfigValidationError(error_msg)
        
        # Validate OPC UA URL
        opcua_config = machine_config['opcua']
        if 'url' not in opcua_config:
            error_msg = f"OPC UA machine '{machine_name}' missing OPC UA URL"
            log.error(error_msg)
            raise ConfigValidationError(error_msg)
        
        # Check nodes configuration
        nodes = machine_config['nodes']
        if not nodes:
            log.warning(f"OPC UA machine '{machine_name}' has no nodes configured")
    
    # def _validate_modbus_machine_config(self, machine_name: str, machine_config: Dict[str, Any]) -> None:
    #     """Validate Modbus specific machine configuration."""
    #     required_sections = ['modbus', 'registers', 'monitoring']
    #     for section in required_sections:
    #         if section not in machine_config:
    #             error_msg = f"Modbus machine '{machine_name}' missing required section: '{section}'"
    #             log.error(error_msg)
    #             raise ConfigValidationError(error_msg)
    #
    #     # Validate Modbus host
    #     modbus_config = machine_config['modbus']
    #     if 'host' not in modbus_config:
    #         error_msg = f"Modbus machine '{machine_name}' missing host address"
    #         log.error(error_msg)
    #         raise ConfigValidationError(error_msg)
    #
    #     # Validate registers configuration
    #     registers = machine_config['registers']
    #     if not registers:
    #         log.warning(f"Modbus machine '{machine_name}' has no registers configured")
    #         return
    #
    #     # Validate individual register configurations
    #     for reg_name, reg_config in registers.items():
    #         self._validate_modbus_register_config(machine_name, reg_name, reg_config)
    #
    # def _validate_modbus_register_config(self, machine_name: str, reg_name: str, reg_config: Dict[str, Any]) -> None:
    #     """Validate individual Modbus register configuration."""
    #     required_reg_fields = ['address', 'count', 'type']
    #     for field in required_reg_fields:
    #         if field not in reg_config:
    #             error_msg = f"Modbus machine '{machine_name}' register '{reg_name}' missing field: '{field}'"
    #             log.error(error_msg)
    #             raise ConfigValidationError(error_msg)
    
    def _validate_influxdb_config(self, influxdb_config: Dict[str, Any]) -> None:
        """
        Validate InfluxDB configuration.
        
        Args:
            influxdb_config: InfluxDB configuration dictionary
            
        Raises:
            ConfigValidationError: If validation fails
        """
        # Defensive check: ensure influxdb_config is present and a mapping
        if not isinstance(influxdb_config, dict):
            error_msg = "InfluxDB configuration is missing or not a mapping"
            log.error(error_msg)
            raise ConfigValidationError(error_msg)

        try:
            for field in self.required_influxdb_fields:
                if field not in influxdb_config:
                    error_msg = f"InfluxDB configuration missing required field: '{field}'"
                    log.error(error_msg)
                    raise ConfigValidationError(error_msg)

            log.debug("InfluxDB configuration is valid")

        except ConfigValidationError:
            raise
        except Exception as e:
            error_msg = f"Error validating InfluxDB configuration: {e}"
            log.error(error_msg)
            raise ConfigValidationError(error_msg)


def validate_config(config: Dict[str, Any]) -> bool:
    """
    Convenience function to validate configuration using ConfigValidator.
    
    Args:
        config: The loaded configuration dictionary
        
    Returns:
        bool: True if configuration is valid
        
    Raises:
        ConfigValidationError: If validation fails
    """
    validator = ConfigValidator()
    return validator.validate_config(config)
