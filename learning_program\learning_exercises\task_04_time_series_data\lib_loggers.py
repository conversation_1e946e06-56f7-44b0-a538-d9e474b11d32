''' To use:
from lib_loggers import set_logger
logger = set_logger()
'''

import os
import logging
import inspect


def set_logger():
    """
    Creates and configures a logger for the calling script.
    Log output goes to both a file (named after the script) and the console.
    """
    # Identify the calling script
    caller_frame = inspect.currentframe().f_back
    caller_file = os.path.basename(inspect.getframeinfo(caller_frame).filename)
    script_name = caller_file.rsplit('.', 1)[0]

    # Prepare log directory and file path
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    log_file = os.path.join(log_dir, f"{script_name}.log")

    # Ensure log directory exists
    os.makedirs(log_dir, exist_ok=True)

    # Define log format
    log_format = (
        '%(asctime)s\t%(name)s\t%(thread)d\t%(levelname)s\t%(message)s'
    )
    date_format = "%Y-%m-%d %H:%M:%S"

    # Create logger
    logger = logging.getLogger(script_name)
    logger.setLevel(logging.DEBUG)
    logger.propagate = False

    # File handler (DEBUG level)
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(logging.Formatter(log_format, date_format))
    logger.addHandler(file_handler)

    # Console handler (DEBUG level)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(logging.Formatter(log_format, date_format))
    logger.addHandler(console_handler)

    return logger
