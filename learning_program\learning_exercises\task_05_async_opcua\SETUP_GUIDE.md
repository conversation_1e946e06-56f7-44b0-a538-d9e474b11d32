# 🚀 Task 5 Setup Guide

## Prerequisites

### 1. Completed Previous Tasks
- ✅ Task 1: Logging fundamentals
- ✅ Task 2: Threading concepts  
- ✅ Task 3: Network communication and Modbus
- ✅ Task 4: Time series data and InfluxDB

### 2. Python Environment Setup

```bash
# Create virtual environment (recommended)
python -m venv task05_env
source task05_env/bin/activate  # On Windows: task05_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. InfluxDB Setup (from Task 4)
Ensure you have InfluxDB running from Task 4, or set up a new instance:
- Update `config.yaml` with your InfluxDB connection details
- Create a new bucket called "async_manufacturing" (or use existing)

## Understanding Async Programming

### Key Concepts Review
Before starting, ensure you understand these concepts:

1. **Event Loop**: The core of async programming that manages task execution
2. **async/await**: Keywords for defining and calling asynchronous functions
3. **Concurrency vs Parallelism**: Async provides concurrency (interleaved execution)
4. **Non-blocking I/O**: Operations that don't freeze the entire program

### Common Async Patterns
```python
# Basic async function
async def my_function():
    await some_operation()

# Running async code
asyncio.run(my_function())

# Concurrent execution
results = await asyncio.gather(task1(), task2(), task3())

# Context managers
async with resource:
    await use_resource()
```

## OPC UA Background

### What is OPC UA?
**OPC UA (Open Platform Communications Unified Architecture)** is the industrial standard for machine-to-machine communication:

- **Standardized**: Works across different manufacturers
- **Secure**: Built-in authentication and encryption
- **Real-time**: Supports subscriptions for live data
- **Rich Data Model**: Complex hierarchical data structures

### OPC UA vs Other Protocols
| Protocol | Use Case | Complexity | Real-time |
|----------|----------|------------|-----------|
| Modbus | Simple devices | Low | Limited |
| OPC UA | Complex machines | High | Excellent |
| MQTT | IoT sensors | Medium | Good |
| HTTP/REST | Web services | Medium | Poor |

### Why Async for OPC UA?
- **Multiple Machines**: Monitor many machines simultaneously
- **Real-time Subscriptions**: Handle live data streams efficiently
- **Network Delays**: Don't block while waiting for responses
- **Resource Efficiency**: Better CPU and memory utilization

## Learning Path

### Phase 1: Async Fundamentals (3-4 hours)
**Goal**: Master async/await patterns and concurrency concepts

**Files to work on**:
- `phase1_async_fundamentals.py`

**Key Learning Outcomes**:
- Understand sync vs async performance differences
- Master task management patterns
- Learn async context managers
- Practice error handling in async code

**Success Criteria**:
- Can explain when and why to use async programming
- Demonstrates significant performance improvements with concurrency
- Handles errors gracefully in async code
- Uses async context managers for resource management

### Phase 2: OPC UA Client Development (4-5 hours)
**Goal**: Build OPC UA clients for industrial communication

**Files to work on**:
- `opcua_simulator.py` (understand server structure)
- `phase2_opcua_client.py`

**Key Learning Outcomes**:
- Connect to OPC UA servers asynchronously
- Read data from industrial machines
- Implement real-time subscriptions
- Handle OPC UA-specific errors

**Success Criteria**:
- Successfully connects to OPC UA servers
- Reads data from multiple node types
- Implements working subscriptions for real-time data
- Handles connection failures and recovery

### Phase 3: Industrial Monitoring System (3-4 hours)
**Goal**: Build complete multi-machine monitoring system

**Files to work on**:
- `phase3_industrial_monitor.py`

**Key Learning Outcomes**:
- Monitor multiple machines concurrently
- Integrate with InfluxDB for data storage
- Implement robust error handling
- Create production-ready monitoring patterns

**Success Criteria**:
- Monitors multiple machines simultaneously
- Stores data efficiently in InfluxDB
- Handles machine failures gracefully
- Provides system health monitoring

## Testing Your Implementation

### Phase 1 Testing
```bash
# Test async fundamentals
python phase1_async_fundamentals.py
```
Expected output: Performance comparisons showing async advantages

### Phase 2 Testing
```bash
# Start OPC UA simulators (in separate terminal)
python opcua_simulator.py

# Test OPC UA client (in another terminal)
python phase2_opcua_client.py
```
Expected output: Successful connections and data reading

### Phase 3 Testing
```bash
# Ensure simulators are running
python opcua_simulator.py

# Start monitoring system
python phase3_industrial_monitor.py
```
Expected output: Multi-machine monitoring with InfluxDB integration

## Common Issues and Solutions

### Async Programming Issues
- **Error**: "RuntimeError: asyncio.run() cannot be called from a running event loop"
  - **Solution**: Don't call `asyncio.run()` from within async functions
- **Error**: "coroutine was never awaited"
  - **Solution**: Use `await` when calling async functions
- **Issue**: Slow performance despite using async
  - **Solution**: Ensure you're using `await asyncio.sleep()` not `time.sleep()`

### OPC UA Connection Issues
- **Error**: "Connection refused"
  - **Solution**: Ensure OPC UA simulator is running first
- **Error**: "BadNodeIdUnknown"
  - **Solution**: Check node identifiers match server structure
- **Error**: "BadTimeout"
  - **Solution**: Increase connection timeout in config

### InfluxDB Integration Issues
- **Error**: "Bucket not found"
  - **Solution**: Create bucket or update config with correct name
- **Issue**: Data not appearing in InfluxDB
  - **Solution**: Check batch writing and flush intervals

## Performance Optimization Tips

### Async Best Practices
1. **Use appropriate concurrency**: Don't create too many concurrent tasks
2. **Batch operations**: Group related operations together
3. **Connection pooling**: Reuse connections when possible
4. **Timeout handling**: Always set reasonable timeouts

### OPC UA Optimization
1. **Subscription intervals**: Balance real-time needs with performance
2. **Node grouping**: Subscribe to related nodes together
3. **Data filtering**: Only collect data you actually need
4. **Connection management**: Handle reconnections efficiently

## Debugging Tips

### Async Debugging
1. **Enable debug mode**: Set `debug=True` in asyncio.run()
2. **Use logging**: Add structured logging to track async operations
3. **Task monitoring**: Track running tasks and their states
4. **Timeout debugging**: Log slow operations

### OPC UA Debugging
1. **Use OPC UA clients**: Tools like UaExpert for testing servers
2. **Node browsing**: Explore server structure before coding
3. **Connection logging**: Log all connection attempts and failures
4. **Data validation**: Verify data types and ranges

## Success Metrics

By the end of Task 5, you should be able to:
- ✅ Write efficient asynchronous code using async/await
- ✅ Implement OPC UA clients for industrial communication
- ✅ Handle real-time data subscriptions and events
- ✅ Build concurrent monitoring systems for multiple machines
- ✅ Integrate async data collection with time series databases
- ✅ Handle network failures and implement robust error recovery
- ✅ Optimize async applications for industrial performance requirements

## Additional Resources

### Async Programming
- [Real Python - Async IO](https://realpython.com/async-io-python/)
- [Python asyncio Documentation](https://docs.python.org/3/library/asyncio.html)
- [Async/Await Best Practices](https://docs.python.org/3/library/asyncio-dev.html)

### OPC UA Protocol
- [OPC Foundation](https://opcfoundation.org/)
- [asyncua Documentation](https://asyncua.readthedocs.io/)
- [OPC UA Specification](https://reference.opcfoundation.org/)

### Industrial Communication
- [Industrial IoT Protocols](https://www.influxdata.com/solutions/industrial-iot/)
- [Manufacturing Data Architecture](https://www.automationworld.com/factory/iiot)

Ready to master async programming for industrial systems? Start with Phase 1! ⚡
