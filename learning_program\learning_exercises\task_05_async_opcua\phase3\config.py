import yaml
import logging

# Get a child logger from the central logger configured in main.py
log = logging.getLogger("manufacturing_monitor.config")

def load_config(path="config.yaml"):
    """
    Loads and validates the YAML configuration file.

    Args:
        path (str): The file path to the YAML configuration file.
                    Defaults to "config.yaml".

    Returns:
        dict: A dictionary containing the application configuration.

    Raises:
        FileNotFoundError: If the configuration file cannot be found at the given path.
        yaml.YAMLError: If the file contains invalid YAML.
        ValueError: If the configuration file is empty.
    """
    
    log.info(f"Attempting to load configuration from: {path}")
    try:
        with open(path, 'r') as f:
            config = yaml.safe_load(f)
            if not config:
                raise ValueError("Configuration file is empty or invalid.")
            log.info("Configuration loaded successfully.")
            return config
    except FileNotFoundError:
        log.critical(f"Configuration file not found at: {path}. Application cannot start.")
        # Re-raise the exception to stop the application
        raise
    except yaml.YAMLError as e:
        log.critical(f"Error parsing YAML file: {e}. Application cannot start.")
        raise
    except ValueError as e:
        log.critical(str(e))
        raise