"""OPC UA Connection Management"""
import logging
from asyncua import Client
from asyncua.ua import UaStatusCodeError

log = logging.getLogger(__name__)


class OPCUAConnection:
    """Handles OPC UA server connection and disconnection."""
    
    def __init__(self, server_url, timeout=10):
        """
        Initialize the OPC UA connection.
        
        Args:
            server_url (str): OPC UA server URL
            timeout (int): Connection timeout in seconds
        """
        self.server_url = server_url
        self.timeout = timeout
        self.client = None
    
    async def connect(self):
        """
        Connect to the OPC UA server with proper error handling.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            log.info(f"Attempting to connect to OPC UA server: {self.server_url}")
            self.client = Client(url=self.server_url, timeout=self.timeout)
            await self.client.connect()
            log.info("Successfully connected to OPC UA server")
            return True
            
        except TimeoutError:
            log.error(f"Connection timed out after {self.timeout} seconds")
            return False
        except UaStatusCodeError as e:
            log.error(f"OPC UA status error: {e}")
            return False
        except ConnectionRefusedError:
            log.error(f"Connection refused. Make sure the server is running at {self.server_url}")
            return False
        except Exception as e:
            log.error(f"Unexpected error connecting to server: {e}")
            return False
    
    async def disconnect(self):
        """Safely disconnect from the OPC UA server."""
        try:
            if self.client:
                await self.client.disconnect()
                log.info("Disconnected from OPC UA server")
        except Exception as e:
            log.warning(f"Error during disconnection: {e}")
    
    def get_node(self, node_id):
        """
        Get a node from the connected client.
        
        Args:
            node_id (str): Node ID to retrieve
            
        Returns:
            Node: OPC UA node object
            
        Raises:
            RuntimeError: If not connected to OPC UA server
        """
        if not self.client:
            raise RuntimeError("Not connected to OPC UA server")
        return self.client.get_node(node_id)
