"""
Phase 4: Customization & Adaptation

Learning Goals:
- Master adaptation techniques for new machine types
- Build reusable components and templates
- Create comprehensive documentation and deployment guides
- Complete final capstone project demonstrating all skills

This phase demonstrates the ability to quickly adapt systems for new requirements.
"""

import asyncio
import yaml
import json
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional
from jinja2 import Template
import logging

class MachineAdapter:
    """
    Adapt the monitoring system for new machine types.
    
    Learning Goal: Master system adaptation and extensibility
    Real Project Pattern: Adding new machines to existing monitoring infrastructure
    """
    
    def __init__(self, config_path="config/"):
        """
        Initialize machine adapter.
        
        TODO: Set up adaptation infrastructure
        """
        self.config_path = Path(config_path)
        self.templates_path = Path("templates/")
        self.templates_path.mkdir(exist_ok=True)
        
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        print("=== MACHINE ADAPTER ===")
        print("Goal: Quickly adapt monitoring system for new machine types\n")
        
    async def adapt_for_new_machine(self, machine_info):
        """
        Adapt the monitoring system for a new machine type.
        
        TODO: Implement complete machine adaptation workflow
        Learning Goal: Systematic approach to adding new machines
        
        Args:
            machine_info (dict): Information about the new machine
            
        Returns:
            dict: Adaptation results and configuration
        """
        print(f"=== ADAPTING FOR NEW MACHINE: {machine_info.get('name', 'Unknown')} ===")
        
        adaptation_results = {
            'machine_info': machine_info,
            'adaptation_timestamp': datetime.now(timezone.utc).isoformat(),
            'configuration_generated': False,
            'templates_created': False,
            'documentation_generated': False,
            'integration_tested': False
        }
        
        try:
            # TODO: Step 1 - Analyze machine capabilities
            print("1. Analyzing machine capabilities...")
            capabilities = await self.analyze_machine_capabilities(machine_info)
            adaptation_results['capabilities'] = capabilities
            
            # TODO: Step 2 - Generate configuration
            print("2. Generating configuration...")
            config = await self.generate_machine_configuration(machine_info, capabilities)
            adaptation_results['configuration'] = config
            adaptation_results['configuration_generated'] = True
            
            # TODO: Step 3 - Create monitoring templates
            print("3. Creating monitoring templates...")
            templates = await self.create_monitoring_templates(machine_info, capabilities)
            adaptation_results['templates'] = templates
            adaptation_results['templates_created'] = True
            
            # TODO: Step 4 - Generate documentation
            print("4. Generating documentation...")
            docs = await self.generate_machine_documentation(machine_info, capabilities, config)
            adaptation_results['documentation'] = docs
            adaptation_results['documentation_generated'] = True
            
            # TODO: Step 5 - Test integration
            print("5. Testing integration...")
            test_results = await self.test_machine_integration(config)
            adaptation_results['test_results'] = test_results
            adaptation_results['integration_tested'] = True
            
            print("✅ Machine adaptation complete!")
            
        except Exception as e:
            print(f"❌ Machine adaptation failed: {e}")
            self.logger.error(f"Adaptation error: {e}")
            adaptation_results['error'] = str(e)
            
        return adaptation_results
        
    async def analyze_machine_capabilities(self, machine_info):
        """
        Analyze what the new machine can do.
        
        TODO: Implement capability analysis
        """
        capabilities = {
            'protocols_supported': [],
            'data_points_available': {},
            'communication_requirements': {},
            'integration_complexity': 'unknown'
        }
        
        # TODO: Determine supported protocols
        if machine_info.get('opcua_endpoint'):
            capabilities['protocols_supported'].append('opcua')
            
        if machine_info.get('modbus_config'):
            capabilities['protocols_supported'].append('modbus')
            
        if machine_info.get('http_api'):
            capabilities['protocols_supported'].append('http')
            
        # TODO: Analyze data points
        if 'opcua_endpoint' in machine_info:
            # TODO: Connect and browse OPC UA nodes
            capabilities['data_points_available']['opcua'] = await self.discover_opcua_nodes(
                machine_info['opcua_endpoint']
            )
            
        if 'modbus_config' in machine_info:
            # TODO: Scan Modbus registers
            capabilities['data_points_available']['modbus'] = await self.discover_modbus_registers(
                machine_info['modbus_config']
            )
            
        # TODO: Assess integration complexity
        capabilities['integration_complexity'] = self.assess_integration_complexity(capabilities)
        
        return capabilities
        
    async def discover_opcua_nodes(self, endpoint):
        """
        Discover available OPC UA nodes.
        
        TODO: Implement OPC UA node discovery
        """
        nodes = {}
        
        try:
            # TODO: Connect to OPC UA server and browse nodes
            # This would use the patterns from previous tasks
            print(f"    📡 Discovering OPC UA nodes at {endpoint}")
            
            # Simulated discovery for template
            nodes = {
                'temperature': {
                    'node_id': 'ns=4;s=Machine.Environment.Temperature',
                    'data_type': 'float',
                    'unit': 'celsius'
                },
                'status': {
                    'node_id': 'ns=4;s=Machine.Status.Current',
                    'data_type': 'string',
                    'unit': None
                }
            }
            
        except Exception as e:
            print(f"    ❌ OPC UA discovery failed: {e}")
            
        return nodes
        
    async def discover_modbus_registers(self, modbus_config):
        """
        Discover available Modbus registers.
        
        TODO: Implement Modbus register discovery
        """
        registers = {}
        
        try:
            # TODO: Connect to Modbus device and scan registers
            print(f"    🔌 Discovering Modbus registers at {modbus_config.get('ip')}")
            
            # Simulated discovery for template
            registers = {
                'voltage': {
                    'address': 0,
                    'type': 'float32',
                    'unit': 'V'
                },
                'current': {
                    'address': 6,
                    'type': 'float32',
                    'unit': 'A'
                }
            }
            
        except Exception as e:
            print(f"    ❌ Modbus discovery failed: {e}")
            
        return registers
        
    def assess_integration_complexity(self, capabilities):
        """
        Assess how complex it will be to integrate this machine.
        
        TODO: Implement complexity assessment
        """
        complexity_score = 0
        
        # TODO: Score based on protocols
        protocol_scores = {'opcua': 3, 'modbus': 2, 'http': 1}
        for protocol in capabilities['protocols_supported']:
            complexity_score += protocol_scores.get(protocol, 5)
            
        # TODO: Score based on data points
        total_data_points = sum(
            len(points) for points in capabilities['data_points_available'].values()
        )
        complexity_score += min(total_data_points, 10)
        
        # TODO: Determine complexity level
        if complexity_score <= 5:
            return 'low'
        elif complexity_score <= 15:
            return 'medium'
        else:
            return 'high'
            
    async def generate_machine_configuration(self, machine_info, capabilities):
        """
        Generate configuration for the new machine.
        
        TODO: Implement configuration generation
        """
        config = {
            'machine_id': machine_info.get('name', 'new_machine'),
            'type': machine_info.get('type', 'unknown'),
            'manufacturer': machine_info.get('manufacturer', 'unknown'),
            'location': machine_info.get('location', 'unknown'),
            'protocols': {},
            'data_points': {},
            'monitoring': {
                'enabled': True,
                'collection_interval_seconds': 5,
                'retry_attempts': 3
            }
        }
        
        # TODO: Configure protocols
        for protocol in capabilities['protocols_supported']:
            if protocol == 'opcua':
                config['protocols']['opcua'] = {
                    'url': machine_info.get('opcua_endpoint'),
                    'namespace': 4,
                    'authentication': {
                        'enabled': False
                    }
                }
                
            elif protocol == 'modbus':
                modbus_config = machine_info.get('modbus_config', {})
                config['protocols']['modbus'] = {
                    'ip': modbus_config.get('ip'),
                    'port': modbus_config.get('port', 502),
                    'unit_id': modbus_config.get('unit_id', 1)
                }
                
        # TODO: Configure data points
        for protocol, data_points in capabilities['data_points_available'].items():
            config['data_points'][protocol] = data_points
            
        # TODO: Save configuration
        config_file = self.config_path / f"machine_{config['machine_id']}.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
            
        print(f"    💾 Configuration saved: {config_file}")
        
        return config
        
    async def create_monitoring_templates(self, machine_info, capabilities):
        """
        Create monitoring code templates for the new machine.
        
        TODO: Implement template generation
        """
        templates = {}
        
        # TODO: Create OPC UA monitoring template
        if 'opcua' in capabilities['protocols_supported']:
            opcua_template = await self.create_opcua_monitoring_template(machine_info, capabilities)
            templates['opcua_monitor'] = opcua_template
            
        # TODO: Create Modbus monitoring template
        if 'modbus' in capabilities['protocols_supported']:
            modbus_template = await self.create_modbus_monitoring_template(machine_info, capabilities)
            templates['modbus_monitor'] = modbus_template
            
        # TODO: Create integration template
        integration_template = await self.create_integration_template(machine_info, capabilities)
        templates['integration'] = integration_template
        
        return templates
        
    async def create_opcua_monitoring_template(self, machine_info, capabilities):
        """
        Create OPC UA monitoring template.
        
        TODO: Implement OPC UA template generation
        """
        template_content = '''
"""
OPC UA Monitoring for {{ machine_name }}

Auto-generated template for monitoring {{ machine_type }} via OPC UA.
Generated on: {{ generation_timestamp }}
"""

import asyncio
from asyncua import Client, Node
from asyncua.common.subscription import DataChangeNotif

class {{ machine_class_name }}Monitor:
    """Monitor {{ machine_name }} via OPC UA."""
    
    def __init__(self, config):
        self.config = config
        self.client = None
        self.subscription = None
        
    async def start_monitoring(self):
        """Start monitoring {{ machine_name }}."""
        server_url = "{{ opcua_endpoint }}"
        
        async with Client(url=server_url) as client:
            # Set up subscription
            handler = {{ machine_class_name }}DataHandler()
            subscription = await client.create_subscription(1000, handler)
            
            # Subscribe to data points
            nodes_to_monitor = [
                {% for point_name, point_config in data_points.items() %}
                client.get_node("{{ point_config.node_id }}"),  # {{ point_name }}
                {% endfor %}
            ]
            
            await subscription.subscribe_data_change(nodes_to_monitor)
            
            # Keep monitoring
            while True:
                await asyncio.sleep(1)

class {{ machine_class_name }}DataHandler:
    """Handle data changes from {{ machine_name }}."""
    
    def datachange_notification(self, node: Node, val, data: DataChangeNotif):
        """Process data change notification."""
        node_id = str(node.nodeid.Identifier)
        timestamp = data.monitored_item.Value.SourceTimestamp
        
        # TODO: Process and store data
        print(f"{{ machine_name }} data: {node_id} = {val} at {timestamp}")

if __name__ == "__main__":
    # TODO: Load configuration and start monitoring
    config = {}  # Load from configuration file
    monitor = {{ machine_class_name }}Monitor(config)
    asyncio.run(monitor.start_monitoring())
'''
        
        # TODO: Render template
        template = Template(template_content)
        rendered = template.render(
            machine_name=machine_info.get('name', 'Unknown'),
            machine_type=machine_info.get('type', 'unknown'),
            machine_class_name=machine_info.get('name', 'Unknown').replace(' ', '').replace('-', ''),
            opcua_endpoint=machine_info.get('opcua_endpoint', 'opc.tcp://localhost:4840/'),
            data_points=capabilities.get('data_points_available', {}).get('opcua', {}),
            generation_timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # TODO: Save template
        template_file = self.templates_path / f"{machine_info.get('name', 'unknown')}_opcua_monitor.py"
        with open(template_file, 'w') as f:
            f.write(rendered)
            
        print(f"    📝 OPC UA template created: {template_file}")
        
        return {
            'file': str(template_file),
            'content': rendered
        }
        
    async def create_modbus_monitoring_template(self, machine_info, capabilities):
        """
        Create Modbus monitoring template.
        
        TODO: Implement Modbus template generation
        """
        # Similar to OPC UA template but for Modbus
        template_content = '''
"""
Modbus Monitoring for {{ machine_name }}

Auto-generated template for monitoring {{ machine_type }} via Modbus.
Generated on: {{ generation_timestamp }}
"""

import asyncio
from pyModbusTCP.client import ModbusClient

class {{ machine_class_name }}Monitor:
    """Monitor {{ machine_name }} via Modbus."""
    
    def __init__(self, config):
        self.config = config
        self.client = ModbusClient(
            host="{{ modbus_ip }}",
            port={{ modbus_port }},
            unit_id={{ modbus_unit_id }},
            auto_open=True,
            auto_close=True
        )
        
    async def start_monitoring(self):
        """Start monitoring {{ machine_name }}."""
        while True:
            try:
                if self.client.open():
                    # Read configured registers
                    data = {}
                    {% for register_name, register_config in registers.items() %}
                    # Read {{ register_name }}
                    values = self.client.read_holding_registers({{ register_config.address }}, 2)
                    if values:
                        data['{{ register_name }}'] = self.convert_to_float(values)
                    {% endfor %}
                    
                    # Process data
                    await self.process_data(data)
                    
                    self.client.close()
                    
                await asyncio.sleep(5)  # Collection interval
                
            except Exception as e:
                print(f"Monitoring error: {e}")
                await asyncio.sleep(10)
                
    def convert_to_float(self, values):
        """Convert Modbus values to float."""
        # TODO: Implement proper conversion based on data type
        return float(values[0] + values[1] / 1000.0)
        
    async def process_data(self, data):
        """Process collected data."""
        # TODO: Store data in InfluxDB or other storage
        print(f"{{ machine_name }} data: {data}")

if __name__ == "__main__":
    config = {}  # Load from configuration file
    monitor = {{ machine_class_name }}Monitor(config)
    asyncio.run(monitor.start_monitoring())
'''
        
        modbus_config = machine_info.get('modbus_config', {})
        template = Template(template_content)
        rendered = template.render(
            machine_name=machine_info.get('name', 'Unknown'),
            machine_type=machine_info.get('type', 'unknown'),
            machine_class_name=machine_info.get('name', 'Unknown').replace(' ', '').replace('-', ''),
            modbus_ip=modbus_config.get('ip', '*************'),
            modbus_port=modbus_config.get('port', 502),
            modbus_unit_id=modbus_config.get('unit_id', 1),
            registers=capabilities.get('data_points_available', {}).get('modbus', {}),
            generation_timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        template_file = self.templates_path / f"{machine_info.get('name', 'unknown')}_modbus_monitor.py"
        with open(template_file, 'w') as f:
            f.write(rendered)
            
        print(f"    📝 Modbus template created: {template_file}")
        
        return {
            'file': str(template_file),
            'content': rendered
        }
        
    async def create_integration_template(self, machine_info, capabilities):
        """
        Create integration template that combines all protocols.
        
        TODO: Implement integration template generation
        """
        # TODO: Create template that integrates all supported protocols
        # This would be similar to the integrated_monitoring_system.py
        # but customized for this specific machine
        
        return {
            'file': 'integration_template.py',
            'content': '# Integration template placeholder'
        }
        
    async def generate_machine_documentation(self, machine_info, capabilities, config):
        """
        Generate comprehensive documentation for the new machine.
        
        TODO: Implement documentation generation
        """
        docs = {}
        
        # TODO: Create machine overview documentation
        overview_doc = await self.create_machine_overview_doc(machine_info, capabilities)
        docs['overview'] = overview_doc
        
        # TODO: Create integration guide
        integration_guide = await self.create_integration_guide(machine_info, capabilities, config)
        docs['integration_guide'] = integration_guide
        
        # TODO: Create troubleshooting guide
        troubleshooting_guide = await self.create_troubleshooting_guide(machine_info, capabilities)
        docs['troubleshooting'] = troubleshooting_guide
        
        return docs
        
    async def create_machine_overview_doc(self, machine_info, capabilities):
        """Create machine overview documentation."""
        # TODO: Generate markdown documentation
        return "# Machine Overview\n\nTODO: Generate overview documentation"
        
    async def create_integration_guide(self, machine_info, capabilities, config):
        """Create integration guide."""
        # TODO: Generate step-by-step integration guide
        return "# Integration Guide\n\nTODO: Generate integration guide"
        
    async def create_troubleshooting_guide(self, machine_info, capabilities):
        """Create troubleshooting guide."""
        # TODO: Generate troubleshooting documentation
        return "# Troubleshooting Guide\n\nTODO: Generate troubleshooting guide"
        
    async def test_machine_integration(self, config):
        """
        Test the integration with the new machine.
        
        TODO: Implement integration testing
        """
        test_results = {
            'connection_test': False,
            'data_collection_test': False,
            'data_storage_test': False,
            'error_handling_test': False
        }
        
        try:
            # TODO: Test connection to machine
            print("    🔍 Testing connection...")
            test_results['connection_test'] = await self.test_machine_connection(config)
            
            # TODO: Test data collection
            print("    📊 Testing data collection...")
            test_results['data_collection_test'] = await self.test_data_collection(config)
            
            # TODO: Test data storage
            print("    💾 Testing data storage...")
            test_results['data_storage_test'] = await self.test_data_storage(config)
            
            # TODO: Test error handling
            print("    ⚠️ Testing error handling...")
            test_results['error_handling_test'] = await self.test_error_handling(config)
            
        except Exception as e:
            print(f"    ❌ Integration testing failed: {e}")
            test_results['error'] = str(e)
            
        return test_results
        
    async def test_machine_connection(self, config):
        """Test connection to machine."""
        # TODO: Implement connection testing
        return True
        
    async def test_data_collection(self, config):
        """Test data collection from machine."""
        # TODO: Implement data collection testing
        return True
        
    async def test_data_storage(self, config):
        """Test data storage functionality."""
        # TODO: Implement data storage testing
        return True
        
    async def test_error_handling(self, config):
        """Test error handling scenarios."""
        # TODO: Implement error handling testing
        return True

async def main():
    """Phase 4: Demonstrate machine adaptation capabilities"""
    print("=== PHASE 4: CUSTOMIZATION & ADAPTATION ===")
    print("Demonstrating rapid adaptation to new machine types\n")
    
    # Example new machine to adapt for
    new_machine_info = {
        'name': 'CNC_Mill_001',
        'type': 'cnc_machine',
        'manufacturer': 'Haas',
        'model': 'VF-2',
        'location': 'Shop_Floor_A',
        'opcua_endpoint': 'opc.tcp://*************:4840/',
        'modbus_config': {
            'ip': '*************',
            'port': 502,
            'unit_id': 1
        }
    }
    
    # Create machine adapter
    adapter = MachineAdapter()
    
    try:
        # Adapt system for new machine
        results = await adapter.adapt_for_new_machine(new_machine_info)
        
        # Display results
        print(f"\n=== ADAPTATION RESULTS ===")
        print(f"Machine: {results['machine_info']['name']}")
        print(f"Configuration generated: {'✅' if results['configuration_generated'] else '❌'}")
        print(f"Templates created: {'✅' if results['templates_created'] else '❌'}")
        print(f"Documentation generated: {'✅' if results['documentation_generated'] else '❌'}")
        print(f"Integration tested: {'✅' if results['integration_tested'] else '❌'}")
        
        if 'error' in results:
            print(f"Error: {results['error']}")
            
    except KeyboardInterrupt:
        print("\nAdaptation interrupted by user")
    except Exception as e:
        print(f"Adaptation error: {e}")
        
    print("\n=== PHASE 4 COMPLETE ===")
    print("Key skills demonstrated:")
    print("✓ Rapid machine type adaptation")
    print("✓ Automated configuration generation")
    print("✓ Template-based code generation")
    print("✓ Comprehensive documentation creation")
    print("✓ Integration testing and validation")
    print("\n🎯 CAPSTONE PROJECT READY!")

if __name__ == "__main__":
    asyncio.run(main())
