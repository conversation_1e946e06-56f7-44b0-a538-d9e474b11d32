2025-08-20 11:45:19,790 - [INFO] - manufacturing_monitor.main - (main.py).initialize_application(49) - ============================================================
2025-08-20 11:45:19,790 - [INFO] - manufacturing_monitor.main - (main.py).initialize_application(50) - Manufacturing Monitor Starting Up
2025-08-20 11:45:19,791 - [INFO] - manufacturing_monitor.main - (main.py).initialize_application(51) - ============================================================
2025-08-20 11:45:19,791 - [INFO] - manufacturing_monitor.main - (main.py).initialize_application(52) - Log level set to: INFO
2025-08-20 11:45:19,792 - [INFO] - manufacturing_monitor.main - (main.py).initialize_application(53) - Log file location: C:\Users\<USER>\Downloads\SIMTech_Internship\MicroSLMMonitoring\prod\logs\manufacturing_monitor_20250820_114519.log
2025-08-20 11:45:19,792 - [INFO] - manufacturing_monitor.main - (main.py).initialize_application(54) - Logging system initialized successfully
2025-08-20 11:45:19,792 - [INFO] - manufacturing_monitor.main - (main.py).load_application_config(72) - Loading application configuration...
2025-08-20 11:45:19,792 - [INFO] - manufacturing_monitor.config - (config.py).load_config(24) - Attempting to load configuration from: config.yaml
2025-08-20 11:45:19,796 - [INFO] - manufacturing_monitor.config - (config.py).load_config(30) - Configuration loaded successfully.
2025-08-20 11:45:19,796 - [INFO] - manufacturing_monitor.config_validator - (config_validator.py).validate_config(37) - Validating configuration structure...
2025-08-20 11:45:19,797 - [INFO] - manufacturing_monitor.config_validator - (config_validator.py)._validate_environment_config(79) - Using environment: development
2025-08-20 11:45:19,797 - [INFO] - manufacturing_monitor.config_validator - (config_validator.py).validate_config(49) - Configuration validation completed successfully for 1 machines
2025-08-20 11:45:19,797 - [INFO] - manufacturing_monitor.main - (main.py).load_application_config(88) - Configuration loaded and validated successfully
2025-08-20 11:45:19,798 - [INFO] - manufacturing_monitor.main - (main.py).main(121) - Running in development mode
2025-08-20 11:45:19,798 - [INFO] - manufacturing_monitor.main - (main.py).main(124) - Initializing InfluxDB connection...
2025-08-20 11:45:19,798 - [INFO] - manufacturing_monitor.influx_writer - (influx_writer.py).__init__(24) - Initializing InfluxWriter for URL: http://localhost:8086 and Org: docs
2025-08-20 11:45:20,406 - [INFO] - manufacturing_monitor.influx_writer - (influx_writer.py).__init__(39) - InfluxWriter initialized and connection confirmed.
2025-08-20 11:45:20,407 - [INFO] - manufacturing_monitor.main - (main.py).main(127) - InfluxDB writer initialized successfully
2025-08-20 11:45:20,407 - [INFO] - manufacturing_monitor.main - (main.py).main(148) - Starting monitoring for 1 machines...
2025-08-20 11:45:20,407 - [INFO] - manufacturing_monitor.main - (main.py).main(153) - Creating monitoring task for machine: Machine_localhost_4840
2025-08-20 11:45:20,408 - [INFO] - manufacturing_monitor.main - (main.py).main(173) - Created OPCUA monitoring task for machine: Machine_localhost_4840
2025-08-20 11:45:20,408 - [INFO] - manufacturing_monitor.main - (main.py).main(188) - All monitoring tasks created successfully
2025-08-20 11:45:20,408 - [INFO] - manufacturing_monitor.main - (main.py).main(189) - Manufacturing Monitor is now running...
2025-08-20 11:45:20,408 - [INFO] - manufacturing_monitor.main - (main.py).main(190) - Press Ctrl+C to stop the service
2025-08-20 11:45:20,409 - [INFO] - manufacturing_monitor.opcua - (opcua_monitor.py).monitor_opcua_machine(28) - Starting OPC UA monitor for 'Machine_localhost_4840' at opc.tcp://localhost:4840
2025-08-20 11:45:20,451 - [INFO] - manufacturing_monitor.opcua - (opcua_monitor.py).monitor_opcua_machine(37) - Successfully connected to OPC UA server: opc.tcp://localhost:4840
2025-08-20 11:45:20,451 - [INFO] - manufacturing_monitor.opcua - (opcua_monitor.py).__init__(86) - OpcuaDataHandler initialized for machine 'Machine_localhost_4840'.
2025-08-20 11:45:20,485 - [INFO] - manufacturing_monitor.opcua - (opcua_monitor.py).monitor_opcua_machine(55) - Subscribing to 5 nodes for machine 'Machine_localhost_4840'
