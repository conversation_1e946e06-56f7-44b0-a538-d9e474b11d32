import os
import influxdb_client # https://influxdb-client.readthedocs.io/en/latest/usage.html
from influxdb_client import InfluxDBClient
from influxdb_client.client.write_api import SYNCHRONOUS

#token = 'OLZ2GVjo1uEFHzbFr35HSED04JXOk9WEzta8BeYeK1b9hCJBUrxwpmhzKwS7EDtMr6wN6A8P6EpghaXif2F35Q==' # Token for "Read endpoint for Python analysis scripts"
token = 'BAUefNIXOaHhiGcJlHSOXUPeZFsECwKuw3Cz72EUr9fOtWZdLwYlHLUQn7Pzq__qqesPFjXYV2hyjCGItHx__Q==' # Token for "Generic RW token for test bucket"

bucket_name = 'testbucket'
org_id = '266e2e2e067fbe5b'

client = InfluxDBClient(url="http://influxdb.ami.modelfactory.sg:8086",token=token,org=org_id)

write_api = client.write_api(write_options=SYNCHRONOUS)
query_api = client.query_api()

def write_method_1():
    p = influxdb_client.Point(measurement_name="python_testscript").tag("location", "Singapore").field("temperature", 35.0)
    write_api.write(bucket=bucket_name, org=org_id, record=p)

def write_method_2():
    p = influxdb_client.Point(measurement_name="python_testscript")
    p = p.tag("location", "Jurong Island")
    p = p.field("temperature", 36.0)
    p = p.field("humidity", 99.5)
    write_api.write(bucket=bucket_name, org=org_id, record=p)

def read_method_1():
    # Read out all data points wrote
    query_text = '''from(bucket: "testbucket")
                      |> range(start: -60m, stop: 0d)                                        // Comments can be added like that
                    //  |> filter(fn: (r) => r["_measurement"] == "power_consumption")      // Or just comment out the entire line
                    //  |> filter(fn: (r) => r["eqpt"] == "DMG_SLM30")
                    //  |> filter(fn: (r) => r["_field"] == "Active Power Total (kW)")
                    //  |> aggregateWindow(every: 30s, fn: mean, createEmpty: false)
                    //  |> yield(name: "mean")
                      |> last()
    '''

    
    df_results = query_api.query_data_frame(query=query_text) # The .query_data_frame returns a Pandas DataFrame while .query returns..

    if len(df_results)==0:
        raise ValueError('There appears to be no data points returned with your query.')
    
    print(df_results.iloc[0])
    
    return df_results


def read_method_2():
    # Read out last data point
    query_text = '''from(bucket: "testbucket")
                      |> range(start: -1m, stop: 0d)
                      |> last()
    '''
    result = query_api.query(query=query_text) # The .query_data_frame returns a Pandas DataFrame while .query returns an object

    for table_number, table in enumerate(result):
        for record_number, record in enumerate(table.records):
            #print(f'Measurement = {record.get_measurement()}')
            #print(f'Time = {record.get_time()}')
            #print(f'Table = {record.table}')
            #print('{0:5} = {1}'.format(record.get_field(), record.get_value()))

            full_record_dict = record.__dict__
            
            #print(full_record_dict)
            print(f"T{table_number}, R{record_number}, Values={full_record_dict['values']}")




if __name__ == '__main__':
    #write_method_1()
    #read_method_1()
    write_method_2()
    read_method_2()