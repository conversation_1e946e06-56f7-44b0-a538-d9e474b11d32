development:
  power_meters:
    PowerMeter_A:
      ip_address: "127.0.0.1"  # Localhost for simulation
      port: 5020
      device_id: 1
      registers:
        voltage_l1: 0x0000    # Register address for L1 voltage
        voltage_l2: 0x0002    # Register address for L2 voltage  
        voltage_l3: 0x0004    # Register address for L3 voltage
        current_l1: 0x0006    # Register address for L1 current
        current_l2: 0x0008    # Register address for L2 current
        current_l3: 0x000A    # Register address for L3 current
        power_total: 0x0034   # Register address for total power
    PowerMeter_B:
      ip_address: "127.0.0.1"
      port: 5021
      device_id: 2
      registers:
        voltage_l1: 0x0000
        voltage_l2: 0x0002
        current_l1: 0x0006
        power_total: 0x0034
  modbus:
    timeout: 3.0
    unit_id: 1
    register_count: 2  # IEEE 754 floats use 2 registers

production:
  power_meters:
    PowerMeter_A:
      ip_address: "*************"
      port: 502
      device_id: 1
      registers:
        voltage_l1: 0x0000
        voltage_l2: 0x0002
        voltage_l3: 0x0004
        current_l1: 0x0006
        current_l2: 0x0008
        current_l3: 0x000A
        power_total: 0x0034
    PowerMeter_B:
      ip_address: "*************"
      port: 502
      device_id: 2
      registers:
        voltage_l1: 0x0000
        voltage_l2: 0x0002
        voltage_l3: 0x0004
        current_l1: 0x0006
        current_l2: 0x0008
        current_l3: 0x000A
        power_total: 0x0034
  modbus:
    timeout: 5.0
    unit_id: 1
    register_count: 2
