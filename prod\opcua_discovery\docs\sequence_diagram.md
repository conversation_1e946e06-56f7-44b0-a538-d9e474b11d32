# OPC UA Discovery Package - Sequence Diagram

This document shows how the opcua_discovery package components interact during the node discovery and configuration generation process.

## Main Flow Sequence Diagram

```mermaid
sequenceDiagram
    participant C<PERSON><PERSON> as cli.py
    participant Discoverer as OPCUANodeDiscoverer
    participant Connection as OPCUAConnection
    participant <PERSON><PERSON><PERSON> as NodeBrowser
    participant Con<PERSON><PERSON><PERSON><PERSON> as ConfigGenerator
    participant <PERSON><PERSON><PERSON> as FieldNameNormalizer
    participant OPCServer as OPC UA Server
    participant FileSystem as File System

    Note over CLI: User runs: python cli.py opc.tcp://localhost:4841
    
    CLI->>CLI: Parse command line arguments
    CLI->>CLI: Setup logging
    CLI->>CLI: Validate URL format
    
    CLI->>+Discoverer: OPCUANodeDiscoverer(server_url, timeout, max_depth)
    Discoverer->>+Connection: OPCUAConnection(server_url, timeout)
    Discoverer->>+Browser: NodeBrowser(connection, max_depth)
    Discoverer->>+ConfigGen: ConfigGenerator(server_url)
    Discoverer-->>-CLI: discoverer instance
    
    CLI->>+Discoverer: discover_nodes()
    Discoverer->>+Connection: connect()
    Connection->>+OPCServer: Client.connect()
    OPCServer-->>-Connection: connection established
    Connection-->>-Discoverer: success/failure
    
    alt Connection successful
        Discoverer->>+Browser: browse_all_nodes()
        Browser->>+Connection: get_node("i=85") [Objects root]
        Connection-->>-Browser: root_node
        
        Browser->>+Browser: _recursive_browse(root_node, depth=0, path="")
        
        loop For each node in tree (up to max_depth)
            Browser->>Browser: read_browse_name()
            Browser->>Browser: check namespace (skip if ns=0)
            Browser->>Browser: _try_read_node_value()
            
            alt Node has readable value
                Browser->>Browser: Store in discovered_nodes dict
                Note over Browser: {path: {node_id, display_name, value, data_type, namespace}}
            else Node is container
                Browser->>Browser: Log as debug (container node)
            end
            
            Browser->>Browser: _browse_children()
            Browser->>+Browser: _recursive_browse(child, depth+1, new_path)
        end
        
        Browser-->>-Discoverer: discovered_nodes dict
        Discoverer->>+Connection: disconnect()
        Connection->>+OPCServer: Client.disconnect()
        OPCServer-->>-Connection: disconnected
        Connection-->>-Discoverer: disconnected
        Discoverer-->>-CLI: discovered_nodes dict
        
        CLI->>CLI: _print_discovery_summary(server_url, nodes)
        Note over CLI: Prints:<br/>- Server URL<br/>- Total nodes found<br/>- Namespace distribution<br/>- Sample nodes
        
        CLI->>+Discoverer: generate_machine_config(nodes, machine_name, machine_type)
        Discoverer->>+ConfigGen: generate_machine_config(nodes, machine_name, machine_type)
        
        ConfigGen->>ConfigGen: _generate_default_machine_name() [if needed]
        ConfigGen->>ConfigGen: _extract_namespaces(discovered_nodes)
        
        ConfigGen->>+ConfigGen: _process_discovered_nodes(discovered_nodes)
        
        loop For each discovered node
            ConfigGen->>+FieldNorm: normalize_field_name(path)
            FieldNorm->>FieldNorm: Extract last segment from path
            FieldNorm->>FieldNorm: Replace non-alphanumeric with underscore
            FieldNorm->>FieldNorm: Remove common prefixes/suffixes
            FieldNorm-->>-ConfigGen: normalized_field_name
            
            ConfigGen->>+FieldNorm: resolve_field_name_collisions(selected_nodes, field_name, path, node_id)
            FieldNorm->>FieldNorm: Check for existing field names
            FieldNorm->>FieldNorm: Add numeric suffix if collision
            FieldNorm-->>-ConfigGen: unique_field_name
            
            ConfigGen->>ConfigGen: selected_nodes[unique_field_name] = node_id
        end
        
        ConfigGen-->>-ConfigGen: selected_nodes dict
        
        ConfigGen->>ConfigGen: _create_machine_config_structure(machine_name, machine_type, namespaces, selected_nodes)
        ConfigGen-->>-Discoverer: machine_config dict
        Discoverer-->>-CLI: machine_config dict
        
        CLI->>+Discoverer: save_config_to_file(config)
        Discoverer->>+ConfigGen: save_config_to_file(config, filename=None)
        
        ConfigGen->>ConfigGen: Generate timestamp-based filename
        
        alt config.yaml exists
            ConfigGen->>+FileSystem: Read existing config.yaml
            FileSystem-->>-ConfigGen: existing_config dict
            ConfigGen->>ConfigGen: Merge new config into existing
        else config.yaml doesn't exist
            ConfigGen->>ConfigGen: Create new config structure
        end
        
        ConfigGen->>+FileSystem: Write YAML config file
        FileSystem-->>-ConfigGen: file saved
        ConfigGen-->>-Discoverer: success
        Discoverer-->>-CLI: success
        
        CLI->>CLI: Print "=== CONFIGURATION GENERATED ==="
        
    else Connection failed
        Discoverer-->>CLI: empty dict {}
        CLI->>CLI: Log error: "No nodes discovered"
    end
```

## Component Responsibilities

### CLI Module (`cli.py`)
- **Purpose**: Command-line interface and user interaction
- **Key Functions**:
  - `main()`: Parse arguments, validate input, orchestrate discovery
  - `discover_and_configure()`: Main workflow coordinator
  - `_print_discovery_summary()`: Display results to user

### OPCUANodeDiscoverer (`discoverer.py`)
- **Purpose**: Main orchestrator that coordinates all components
- **Key Functions**:
  - `discover_nodes()`: Orchestrates connection and browsing
  - `generate_machine_config()`: Delegates to ConfigGenerator
  - `save_config_to_file()`: Delegates to ConfigGenerator

### OPCUAConnection (`connection.py`)
- **Purpose**: Manages OPC UA server connection lifecycle
- **Key Functions**:
  - `connect()`: Establish connection with error handling
  - `disconnect()`: Clean disconnection
  - `get_node()`: Retrieve node references

### NodeBrowser (`node_browser.py`)
- **Purpose**: Recursive node tree traversal and data collection
- **Key Functions**:
  - `browse_all_nodes()`: Start browsing from Objects root
  - `_recursive_browse()`: Recursive tree traversal
  - `_try_read_node_value()`: Attempt to read node values

### ConfigGenerator (`config_generator.py`)
- **Purpose**: Generate machine configuration from discovered nodes
- **Key Functions**:
  - `generate_machine_config()`: Main config generation logic
  - `_process_discovered_nodes()`: Convert nodes to field mappings
  - `save_config_to_file()`: Write YAML configuration

### FieldNameNormalizer (`field_normalizer.py`)
- **Purpose**: Clean and normalize node paths into field names
- **Key Functions**:
  - `normalize_field_name()`: Convert path to clean field name
  - `resolve_field_name_collisions()`: Handle duplicate names

## Data Flow

1. **Input**: OPC UA server URL and optional parameters
2. **Connection**: Establish connection to OPC UA server
3. **Discovery**: Recursively browse node tree, collect readable nodes
4. **Processing**: Normalize field names, resolve collisions
5. **Configuration**: Generate machine configuration structure
6. **Output**: Save YAML configuration file

## Error Handling Points

- **Connection Errors**: Timeout, refused connection, authentication
- **Browsing Errors**: Node access issues, deep recursion protection
- **File I/O Errors**: Config file read/write failures
- **Data Errors**: Invalid node values, malformed paths

## Key Design Patterns

- **Dependency Injection**: Components receive dependencies in constructor
- **Template Method**: Recursive browsing with customizable behavior
- **Strategy Pattern**: Field normalization with collision resolution
- **Facade Pattern**: OPCUANodeDiscoverer simplifies complex interactions
