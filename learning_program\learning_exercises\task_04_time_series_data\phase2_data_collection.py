"""
Phase 2: Data Collection & Storage

Learning Goals:
- Apply threading concepts from Task 2 to continuous data collection
- Implement robust data storage patterns from the real project
- Handle network failures and data quality issues
- Build a complete sensor monitoring system

This phase combines threading, networking, and database concepts into a realistic system.
"""

import threading
import time
import yaml
from datetime import datetime, timezone
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS
from sensor_simulator import ManufacturingSensorSimulator

class ManufacturingDataCollector:
    """
    Collect data from multiple sensors using threading patterns from Task 2.
    
    Learning Goal: Apply threading to real-world data collection
    Real Project Pattern: Similar to power meter and OPC UA monitoring systems
    """
    
    def __init__(self, config):
        """
        Task 1: Initialize the data collection system.
        Initialize all sensors and threading infrastructure.
        Then, set up InfluxDB client for data storage.
        
        TODO: Set up InfluxDB client, sensor simulators, and threading infrastructure
        """
        self.config = config
        self.sensors = {}
        self.threads = {}
        self.running = False
        self.data_stats = {}
        self.lock = threading.Lock()  # For thread-safe operations
        
        print("Initializing Manufacturing Data Collector...")
        
        # TODO: Initialize InfluxDB client from config
        self.env_choice = 'development'
        self.env_config = config[self.env_choice]
        self.influxdb_config = self.env_config['influxdb']
        try:
            self.influxdb_client = InfluxDBClient(
                url=self.influxdb_config['url'],
                token=self.influxdb_config['token'],
                org=self.influxdb_config['org']
            )
            self.influxdb_write_api = self.influxdb_client.write_api(write_options=SYNCHRONOUS)
        except Exception as e:
            print(f"Error type: {type(e)}\tError message: {e}")

        # TODO: Create sensor simulators for each configured sensor
        self.sensors_config = self.env_config['sensors']
        for sensor_name, sensor_config in self.sensors_config.items():
            try:
                self.sensors[sensor_name] = ManufacturingSensorSimulator(sensor_name, sensor_config)
            except Exception as e:
                print(f"Error creating simulator for {sensor_name}: {e}")

        # TODO: Initialize data statistics tracking
        for sensor_name in self.sensors.keys():
            self.data_stats[sensor_name] = {
                'readings': 0,
                'errors': 0,
                'last_error': None,
                'last_error_timestamp': None
            }
        
    def monitor_single_sensor(self, sensor_name, sensor_config):
        """
        Task 4: Monitor one sensor continuously (runs in its own thread).
        
        Threading Pattern: Similar to Task 2's worker threads
        Data Storage Pattern: Similar to real project's data collection
        
        Args:
            sensor_name (str): Name of the sensor to monitor
            sensor_config (dict): Configuration for this sensor
            
        TODO: Implement continuous sensor monitoring
        Pattern from real project:
        1. Read sensor value
        2. Validate data quality  
        3. Create InfluxDB point
        4. Store in database
        5. Handle errors gracefully
        6. Sleep until next reading
        """
        print(f"Starting monitoring thread for {sensor_name}")

        # Use the already-initialized sensor simulator from self.sensors
        simulator = self.sensors[sensor_name]

        # CRITICAL FIX: Robust exception handling to prevent thread death
        # PROBLEM: If any exception occurs outside the inner try/catch (e.g., in read_sensor_value()),
        # the entire monitoring thread dies, resulting in 0 readings and 100% error rate.
        # SOLUTION: Wrap the entire loop body in try/catch so the thread NEVER dies.
        # LESSON: Always use nested try/catch in threaded loops - inner for expected errors,
        # outer for unexpected errors that would kill the thread.
        while self.running:
            try:
                value = simulator.read_sensor_value()
                try:
                    self._validate_data_quality(sensor_name, value)
                    point = self._create_data_point(sensor_name, sensor_config, value, datetime.now(timezone.utc))
                    self.influxdb_write_api.write(bucket=self.influxdb_config['bucket'], record=point)
                    self._update_statistics(sensor_name, True)
                    time.sleep(sensor_config['response_time_seconds'])
                    print(f"Read {value} from {sensor_name}")
                except Exception as e:
                    self._update_statistics(sensor_name, False, str(e), datetime.now(timezone.utc))
                    print(f"Data quality check failed for {sensor_name}: {e}")
                    time.sleep(sensor_config['response_time_seconds'])
            except Exception as e:
                # Catch any unexpected error to prevent thread from dying
                self._update_statistics(sensor_name, False, f"Unexpected error: {e}", datetime.now(timezone.utc))
                print(f"Unexpected error in {sensor_name} monitoring: {e}")
                time.sleep(sensor_config['response_time_seconds'])
        print(f"Monitoring thread for {sensor_name} stopped")

    def _validate_data_quality(self, sensor_name, value):
        """
        Task 4a: Validate sensor data quality before storing.
        
        TODO: Implement validation logic
        Consider:
        - Type checking (is it a number?)
        - Range checking (realistic values for sensor type?)
        - Special values (error codes like -999?)
        - Missing data handling
        """
        sensor_config = self.sensors_config[sensor_name]
        min_value = sensor_config['min_value']
        max_value = sensor_config['max_value']

        if not value:
            raise Exception(f"Missing value for {sensor_name}")
        if not isinstance(value, (int, float)):
            raise Exception(f"Invalid value type for {sensor_name}: {type(value)}")
        if value == -999.0:
            raise Exception(f"Error code -999 received for {sensor_name}")
        if value < min_value or value > max_value:
            raise Exception(f"Value out of range for {sensor_name}: {value}")
    
    def start_monitoring(self):
        """
        Task 5: Start monitoring all sensors using threading.
        
        Threading Pattern: Apply concepts from Task 2
        TODO: Create and start threads for each sensor
        """
        print("=== STARTING MANUFACTURING DATA COLLECTION ===")
        self.running = True
        
        # TODO: Create and start threads for each sensor
        # Pattern from Task 2:
        # 1. Create thread for each sensor
        # 2. Store thread references for management
        # 3. Start all threads
        # 4. Handle thread creation errors
        
        sensors_config = self.config['development']['sensors']
        
        for sensor_name, sensor_config in sensors_config.items():
            # TODO: Create thread for this sensor
            thread = threading.Thread(
                target=self.monitor_single_sensor, 
                args=(sensor_name, sensor_config)
            )
            # TODO: Store thread reference
            self.threads[sensor_name] = thread

            try:
                # TODO: Start the thread
                thread.start()
            except Exception as e:
                print(f"Error starting thread for {sensor_name}: {e}")
        
        print(f"Started monitoring {len(self.threads)} sensors")
    
    def stop_monitoring(self):
        """
        Task 6: Gracefully stop all sensor monitoring.
        
        Threading Pattern: Clean shutdown from Task 2
        TODO: Implement graceful shutdown
        """
        print("Stopping data collection...")
        self.running = False
        
        # TODO: Implement graceful shutdown
        # 1. Set running flag to False (already done)
        # 2. Wait for all threads to finish
        # 3. Close database connections
        # 4. Generate final statistics
        
        # TODO: Wait for all threads to complete
        for sensor_name, thread in self.threads.items():
            thread.join(timeout=5.0)
        
        # TODO: Close InfluxDB client connection
        self.influxdb_client.close()
        
        # TODO: Generate final report
        self.generate_collection_report()
        
        print("Data collection stopped")
    
    def generate_collection_report(self):
        """
        Task 7: Generate statistics about data collection performance.
        
        Learning Goal: Understand system monitoring and performance
        TODO: Implement comprehensive reporting
        """
        print("\n=== DATA COLLECTION REPORT ===")
        total_readings = 0
        total_errors = 0
        print(f"{'Sensor':<20} {'Readings':<10} {'Errors':<10} {'Error Rate (%)':<15} {'Last Error':<25} {'Last Error Time':<25}")
        print("-" * 105)
        for sensor_name, stats in self.data_stats.items():
            readings = stats['readings']
            errors = stats['errors']
            total_readings += readings
            total_errors += errors
            error_rate = (errors / (readings + errors) * 100) if (readings + errors) > 0 else 0.0
            last_error = stats['last_error'] if stats['last_error'] else '-'
            last_error_time = stats['last_error_timestamp'].strftime('%Y-%m-%d %H:%M:%S') if stats['last_error_timestamp'] else '-'
            print(f"{sensor_name:<20} {readings:<10} {errors:<10} {error_rate:<15.2f} {last_error:<25} {last_error_time:<25}")

        print("-" * 105)
        total_points = total_readings + total_errors
        overall_error_rate = (total_errors / total_points * 100) if total_points > 0 else 0.0
        print(f"TOTAL{'':<16} {total_readings:<10} {total_errors:<10} {overall_error_rate:<15.2f}")

        # Suggestions
        print("\nSuggestions:")
        if overall_error_rate > 5.0:
            print("- High error rate detected. Investigate sensor calibration, data quality, or network issues.")
        else:
            print("- System error rate is within acceptable limits.")
        for sensor_name, stats in self.data_stats.items():
            readings = stats['readings']
            errors = stats['errors']
            error_rate = (errors / (readings + errors) * 100) if (readings + errors) > 0 else 0.0
            if error_rate > 10.0:
                print(f"- Sensor '{sensor_name}' has a high error rate ({error_rate:.2f}%). Check sensor health or configuration.")
        print("\nReport complete.")
        
    def _create_data_point(self, sensor_name, sensor_config, value, timestamp):
        """
        Task 2: Create an InfluxDB data point from sensor reading.
        
        TODO: Use patterns from Phase 1 to create properly structured data points
        
        Args:
            sensor_name (str): Name of the sensor
            sensor_config (dict): Sensor configuration
            value (float): Sensor reading value
            timestamp (datetime): When reading was taken
            
        Returns:
            Point: InfluxDB Point object ready for writing
        """
        # TODO: Extract measurement name, tags, and field name from config
        measurement_name = sensor_config['measurement']
        tags = sensor_config['tags']
        field_name = sensor_config['field_name']

        # TODO: Create Point object using Phase 1 patterns
        point = Point(measurement_name)
        point.time(timestamp)
        if tags:
            for tag_key, tag_value in tags.items():
                if tag_value:
                    point.tag(tag_key, tag_value)
                else:
                    raise Exception(f"Tag value for {tag_key} is None")
        point.field(field_name, value)

        # TODO: Return completed Point object
        return point
    
    def _update_statistics(self, sensor_name, success, error_type=None, error_timestamp=None):
        """
        Task 3: Update collection statistics in a thread-safe manner.
        
        TODO: Implement thread-safe statistics tracking
        
        Args:
            sensor_name (str): Name of the sensor
            success (bool): Whether the operation was successful
            error_type (str): Type of error if success=False
        """
        # TODO: Use self.lock for thread-safe updates
        with self.lock:
            if success:
                self.data_stats[sensor_name]['readings'] += 1
            else:
                self.data_stats[sensor_name]['errors'] += 1
                self.data_stats[sensor_name]['last_error'] = error_type
                self.data_stats[sensor_name]['last_error_timestamp'] = error_timestamp

def main():
    """Phase 2: Build a complete data collection system"""
    print("=== PHASE 2: DATA COLLECTION & STORAGE ===")
    
    # Load configuration
    try:
        with open("config.yaml") as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print("Error: config.yaml not found. Please create it first.")
        return
    
    # TODO: Create data collector
    collector = ManufacturingDataCollector(config)
    
    try:
        # TODO: Start data collection
        collector.start_monitoring()
        
        # TODO: Run for configured duration
        duration = config['development']['simulation']['duration_minutes']
        print(f"Collecting data for {duration} minutes...")
        print("Press Ctrl+C to stop early")
        
        # TODO: Monitor collection progress
        # Consider: Should you display real-time statistics?
        # Should you check for system health periodically?
        
        time.sleep(duration * 60)  # Convert minutes to seconds
        
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
    finally:
        # TODO: Ensure clean shutdown
        collector.stop_monitoring()
        
    print("Phase 2 complete! Ready for Phase 3 (Analysis & Visualization)")

if __name__ == "__main__":
    main()
