# Environment-specific configuration
# Pattern from real project: separate dev/prod settings

development:
  # InfluxDB configuration for development
  influxdb:
    url: "http://localhost:8086"
    token: "your-development-token"
    org: "your-org"
    bucket: "dev_manufacturing"
    timeout_seconds: 30
    batch_size: 50
    flush_interval_seconds: 5
    
  # Logging configuration
  logging:
    level: "DEBUG"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: "logs/development.log"
    max_size_mb: 10
    backup_count: 5
    
  # System monitoring
  health_monitoring:
    enabled: true
    check_interval_seconds: 30
    alert_thresholds:
      cpu_percent: 80
      memory_percent: 85
      disk_percent: 90
      connection_timeout_seconds: 60
      data_rate_threshold_per_minute: 5
      error_rate_threshold_percent: 10
    
  # Performance settings
  performance:
    max_concurrent_connections: 10
    connection_pool_size: 5
    data_buffer_size: 1000
    async_timeout_seconds: 30
    retry_attempts: 3
    retry_delay_seconds: 5

testing:
  # Testing environment with simulated data
  influxdb:
    url: "http://localhost:8086"
    token: "test-token"
    org: "test-org"
    bucket: "test_manufacturing"
    timeout_seconds: 10
    batch_size: 10
    flush_interval_seconds: 1
    
  logging:
    level: "INFO"
    format: "%(asctime)s - %(levelname)s - %(message)s"
    file: "logs/testing.log"
    
  health_monitoring:
    enabled: true
    check_interval_seconds: 10
    alert_thresholds:
      cpu_percent: 95
      memory_percent: 95
      disk_percent: 95
      
  performance:
    max_concurrent_connections: 5
    connection_pool_size: 2
    data_buffer_size: 100
    async_timeout_seconds: 10
    retry_attempts: 1
    retry_delay_seconds: 1

production:
  # Production configuration matching real project
  influxdb:
    url: "http://influxdb.ami.modelfactory.sg:8086"
    token: "your-production-token"  # Store securely in environment variable
    org: "AMI"
    bucket: "ami_eqpt"
    timeout_seconds: 60
    batch_size: 100
    flush_interval_seconds: 10
    
  logging:
    level: "WARNING"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
    file: "logs/production.log"
    max_size_mb: 100
    backup_count: 10
    
  health_monitoring:
    enabled: true
    check_interval_seconds: 60
    alert_thresholds:
      cpu_percent: 70
      memory_percent: 75
      disk_percent: 80
      connection_timeout_seconds: 120
      data_rate_threshold_per_minute: 50
      error_rate_threshold_percent: 5
    alerting:
      email:
        enabled: true
        smtp_server: "smtp.company.com"
        smtp_port: 587
        username: "<EMAIL>"
        recipients: ["<EMAIL>", "<EMAIL>"]
      slack:
        enabled: false
        webhook_url: "https://hooks.slack.com/services/..."
        
  performance:
    max_concurrent_connections: 50
    connection_pool_size: 20
    data_buffer_size: 5000
    async_timeout_seconds: 60
    retry_attempts: 10
    retry_delay_seconds: 15
    
  security:
    enable_ssl: true
    certificate_path: "/etc/ssl/certs/monitoring.crt"
    private_key_path: "/etc/ssl/private/monitoring.key"
    
  # Production-specific features
  backup:
    enabled: true
    interval_hours: 24
    retention_days: 30
    location: "/backup/monitoring/"
    
  maintenance:
    scheduled_restart_time: "02:00"  # 2 AM daily restart
    log_rotation_time: "01:00"       # 1 AM log rotation
    health_report_time: "06:00"      # 6 AM daily health report
