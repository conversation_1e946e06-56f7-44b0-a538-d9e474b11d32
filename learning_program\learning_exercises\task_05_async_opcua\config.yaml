development:
  # Async system configuration
  event_loop:
    debug_mode: true
    slow_callback_duration: 0.1  # Log operations slower than 100ms
    
  # Simulated machines for learning
  machines:
    EOS_Printer_A:
      type: "3d_printer"
      opcua:
        url: "opc.tcp://localhost:4840"
        namespace: 4
        authentication:
          enabled: false
          username: null
          password: null
      nodes:
        temperature: "ns=4;s=EOS.Machine.Environment.Temperature"
        pressure: "ns=4;s=EOS.Machine.ProcessChamber.Pressure"
        humidity: "ns=4;s=EOS.Machine.Environment.Humidity"
        status: "ns=4;s=EOS.Machine.Status.Current"
        build_progress: "ns=4;s=EOS.Machine.Process.BuildProgress"
      monitoring:
        subscription_interval_ms: 500
        connection_timeout_seconds: 10
        retry_attempts: 3
        retry_delay_seconds: 5
        
    CNC_Machine_B:
      type: "cnc_machine"
      opcua:
        url: "opc.tcp://localhost:4841"
        namespace: 2
        authentication:
          enabled: false
      nodes:
        spindle_speed: "ns=2;s=CNC.Spindle.Speed"
        vibration: "ns=2;s=CNC.Sensors.Vibration"
        tool_wear: "ns=2;s=CNC.Tools.WearLevel"
        status: "ns=2;s=CNC.Status.Current"
      monitoring:
        subscription_interval_ms: 1000
        connection_timeout_seconds: 5
        retry_attempts: 5
        retry_delay_seconds: 3
        
    Power_Monitor_C:
      type: "power_meter"
      opcua:
        url: "opc.tcp://localhost:4842"
        namespace: 3
        authentication:
          enabled: false
      nodes:
        voltage_l1: "ns=3;s=Power.Voltage.L1"
        voltage_l2: "ns=3;s=Power.Voltage.L2"
        voltage_l3: "ns=3;s=Power.Voltage.L3"
        current_total: "ns=3;s=Power.Current.Total"
        power_active: "ns=3;s=Power.Active.Total"
      monitoring:
        subscription_interval_ms: 2000
        connection_timeout_seconds: 15
        retry_attempts: 3
        retry_delay_seconds: 10
        
  # OPC UA simulation settings
  opcua_simulation:
    enable_server: true
    servers:
      - port: 4840
        machine_type: "3d_printer"
        node_count: 25
        update_interval_ms: 1000
      - port: 4841
        machine_type: "cnc_machine"
        node_count: 15
        update_interval_ms: 500
      - port: 4842
        machine_type: "power_meter"
        node_count: 10
        update_interval_ms: 2000
    
  # Integration with Task 4 (InfluxDB)
  influxdb:
    url: "http://localhost:8086"
    token: "your-development-token"
    org: "your-org"
    bucket: "async_manufacturing"
    batch_size: 50
    flush_interval_seconds: 5
    connection_timeout_seconds: 10
    
  # System monitoring
  system_health:
    check_interval_seconds: 30
    connection_timeout_threshold_seconds: 60
    data_rate_threshold_per_minute: 10
    error_rate_threshold_percent: 5
    
  # Performance tuning
  performance:
    max_concurrent_connections: 10
    connection_pool_size: 5
    data_buffer_size: 1000
    async_timeout_seconds: 30

production:
  # Production configuration would include:
  # - Real machine IP addresses and ports
  # - Authentication credentials
  # - Longer timeouts and retry policies
  # - Production InfluxDB settings
  # - Enhanced monitoring and alerting
  
  machines:
    EOS_SI3654:
      type: "3d_printer"
      opcua:
        url: "opc.tcp://**************:4843"
        namespace: 4
        authentication:
          enabled: true
          username: "client1"
          password: "your-production-password"
      nodes:
        temperature: "ns=4;s=EOS.Machine.Environment.Temperature"
        pressure: "ns=4;s=EOS.Machine.ProcessChamber.Pressure"
        # ... more production nodes
      monitoring:
        subscription_interval_ms: 1000
        connection_timeout_seconds: 30
        retry_attempts: 10
        retry_delay_seconds: 15
        
  influxdb:
    url: "http://influxdb.ami.modelfactory.sg:8086"
    token: "your-production-token"
    org: "AMI"
    bucket: "ami_eqpt"
    batch_size: 100
    flush_interval_seconds: 10
