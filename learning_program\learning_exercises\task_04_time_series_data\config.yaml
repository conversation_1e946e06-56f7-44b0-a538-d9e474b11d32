# REMINDER: Each of the sensors in my machine will have a time, measurement, field and value as the baseline, then we can add further categorization for those sensors using tags.
# This is how the InfluxDB data model (time, measurement, field, value, tags) maps to pandas DataFrames and this config.yaml file.

development:
  influxdb:
    url: "http://localhost:8086"
    token: "MyInitialAdminToken0=="  # Replace with your InfluxDB token
    org: "docs"                 # Replace with your organization
    bucket: "phase4_manufacturing_sensors"
    
  # Manufacturing sensor configuration
  # TODO: Design your sensor layout - think about realistic manufacturing scenarios
  sensors:
    # EOS 3D Printer Sensors
    EOS_SI3654_temp:
      measurement: "machine_sensors"
      tags:
        machine_id: "EOS_SI3654"
        machine_type: "3d_printer"
        sensor_type: "temperature"
        location: "print_chamber"
      field_name: "temperature_celsius"
      min_value: 200.0
      max_value: 280.0
      normal_range: [240.0, 260.0]
      response_time_seconds: 0.5
      failure_rate: 0.02
      
    EOS_SI3654_pressure:
      measurement: "machine_sensors"
      tags:
        machine_id: "EOS_SI3654"
        machine_type: "3d_printer"
        sensor_type: "pressure"
        location: "build_chamber"
      field_name: "pressure_bar"
      min_value: 0.8
      max_value: 1.2
      normal_range: [0.95, 1.05]
      response_time_seconds: 1.0
      failure_rate: 0.01
      
    EOS_SI3654_power:
      measurement: "power_consumption"
      tags:
        machine_id: "EOS_SI3654"
        machine_type: "3d_printer"
        source: "power_meter"
      field_name: "power_kw"
      min_value: 5.0
      max_value: 25.0
      normal_range: [12.0, 18.0]
      response_time_seconds: 0.3
      failure_rate: 0.015
      
    # TODO: Add more sensors for other machines
    # Consider: CNC machines, environmental sensors, quality control sensors
    # Think about: What tags would help you analyze data across machine types?
    
  simulation:
    duration_minutes: 10
    collection_interval_seconds: 2
    enable_data_quality_issues: true
    enable_network_failures: true
    batch_write_size: 10  # Write data in batches for efficiency
    
  analysis:
    anomaly_detection:
      std_dev_threshold: 2.5
      window_size_minutes: 30
    
    reporting:
      dashboard_update_interval_seconds: 30
      generate_hourly_reports: true

production:
  influxdb:
    url: "http://influxdb.ami.modelfactory.sg:8086"
    token: "your-production-token"
    org: "AMI"
    bucket: "ami_eqpt"
    
  # Production sensor configuration would be similar but with:
  # - Real IP addresses and connection details
  # - Longer monitoring durations
  # - More conservative failure rates
  # - Additional sensors and machines
