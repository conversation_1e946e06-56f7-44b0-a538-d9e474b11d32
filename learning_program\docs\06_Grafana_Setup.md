# Grafana Setup and Dashboard Configuration

## Overview

Grafana provides the **real-time visualization layer** for your manufacturing data. While the data collection scripts feed data into InfluxDB, <PERSON><PERSON> creates the web-based dashboards that engineers and operators use for monitoring.

## Installation and Setup

### Step 1: Install Grafana
```powershell
# Download <PERSON><PERSON> from https://grafana.com/grafana/download
# Or use Chocolatey (Windows package manager):
choco install grafana

# Start Grafana service
net start grafana
```

**Access Grafana**: http://localhost:3000
- **Default login**: admin/admin (change on first login)

### Step 2: Configure InfluxDB Data Source

1. **Navigate to Configuration → Data Sources**
2. **Click "Add data source"**
3. **Select "InfluxDB"**
4. **Configure connection**:

```yaml
# InfluxDB Data Source Configuration
Name: EOS_Manufacturing_Data
URL: http://influxdb.ami.modelfactory.sg:8086
Database: (leave empty for InfluxDB 2.x)

# Authentication
Basic Auth: Disabled
Organization: AMI
Token: Z29... (your production token from env.yaml)
Default Bucket: ami_eqpt

# Additional Settings
Query Language: Flux
Min time interval: 5s
```

### Step 3: Test Data Source Connection
Click **"Save & Test"** - you should see: ✅ "Data source is working"

## Dashboard Templates

### Dashboard 1: EOS M290 Machine Overview

**Purpose**: High-level status of all EOS 3D printers

**Panels to Create**:

#### Panel 1: Machine Status Summary
```flux
// Query for current machine states
from(bucket: "ami_eqpt")
  |> range(start: -5m)
  |> filter(fn: (r) => r["_measurement"] == "printer stats")
  |> filter(fn: (r) => r["_field"] == "EOS.Machine.Info.ProcessState")
  |> last()
  |> group(columns: ["eqpt"])
```
**Visualization**: Stat panels showing machine status

#### Panel 2: Temperature Trends (24 hours)
```flux
from(bucket: "ami_eqpt")
  |> range(start: -24h)
  |> filter(fn: (r) => r["_measurement"] == "printer stats")
  |> filter(fn: (r) => r["_field"] == "EOS.Machine.Environment.Temperature")
  |> aggregateWindow(every: 5m, fn: mean, createEmpty: false)
```
**Visualization**: Time series graph

#### Panel 3: Current Layer Progress
```flux
from(bucket: "ami_eqpt")
  |> range(start: -1h)
  |> filter(fn: (r) => r["_field"] == "EOS.Machine.CurrentJob.LayerCountCurrent")
  |> last()
  |> group(columns: ["eqpt"])
```
**Visualization**: Gauge or bar gauge

### Dashboard 2: Power Consumption Monitoring

**Purpose**: Track energy usage across all equipment

#### Panel 1: Real-time Power Usage
```flux
from(bucket: "ami_eqpt")
  |> range(start: -15m)
  |> filter(fn: (r) => r["_measurement"] == "power_consumption")
  |> filter(fn: (r) => r["_field"] == "Active Power Total (kW)")
  |> aggregateWindow(every: 30s, fn: mean, createEmpty: false)
```

#### Panel 2: Daily Energy Consumption
```flux
from(bucket: "ami_eqpt")
  |> range(start: -7d)
  |> filter(fn: (r) => r["_field"] == "Total Import (kWh)")
  |> aggregateWindow(every: 1d, fn: max, createEmpty: false)
  |> difference()
```

### Dashboard 3: Process Quality Monitoring

**Purpose**: Track critical process parameters for quality control

#### Panel 1: Oxygen Concentration (Pump-down Monitoring)
```flux
from(bucket: "ami_eqpt")
  |> range(start: -2h)
  |> filter(fn: (r) => r["_field"] == "EOS.Machine.Sensors.ProcessChamber.O2ConcentrationTop")
  |> aggregateWindow(every: 10s, fn: mean, createEmpty: false)
```

#### Panel 2: Chamber Pressure
```flux
from(bucket: "ami_eqpt")
  |> range(start: -4h)  
  |> filter(fn: (r) => r["_field"] =~ /Pressure/)
  |> aggregateWindow(every: 30s, fn: mean, createEmpty: false)
```

## Alert Configuration

### Critical Alerts

#### 1. Machine Offline Alert
```flux
// Alert when no data received for 10 minutes
from(bucket: "ami_eqpt")
  |> range(start: -10m)
  |> filter(fn: (r) => r["_measurement"] == "printer stats")
  |> count()
  |> yield(name: "data_count")

// Alert condition: data_count < 10
```

#### 2. High Temperature Alert
```flux
from(bucket: "ami_eqpt")
  |> range(start: -5m)
  |> filter(fn: (r) => r["_field"] == "EOS.Machine.Environment.Temperature")
  |> mean()

// Alert condition: temperature > 300
```

#### 3. Power Consumption Anomaly
```flux
from(bucket: "ami_eqpt")
  |> range(start: -15m)
  |> filter(fn: (r) => r["_field"] == "Active Power Total (kW)")
  |> mean()

// Alert conditions: 
// - power > 25 (unexpectedly high)
// - power < 1 AND machine_status == "printing" (unexpected low during operation)
```

### Alert Notification Channels

**Email Notifications**:
```yaml
# Configure in Grafana → Alerting → Notification channels
Type: Email
Name: Manufacturing_Team
Addresses: <EMAIL>, <EMAIL>
Subject: EOS Manufacturing Alert: {{range .Alerts}}{{.Annotations.summary}}{{end}}
```

**Slack Integration**:
```yaml
Type: Slack
Webhook URL: https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
Channel: #manufacturing-alerts
Username: GrafanaBot
```

## Dashboard Export Files

Once you've created dashboards in the Grafana UI, export them for version control:

### How to Export:
1. **Dashboard Settings → JSON Model**
2. **Copy the JSON**
3. **Save as `.json` file**

### Recommended File Structure:
```
grafana/
├── dashboards/
│   ├── EOS_M290_Overview.json
│   ├── Power_Consumption.json
│   ├── Process_Quality.json
│   └── Maintenance_Dashboard.json
├── datasources/
│   └── influxdb_datasource.json
├── alerts/
│   ├── machine_offline.json
│   ├── temperature_alerts.json
│   └── power_anomalies.json
└── README.md
```

## Example Dashboard JSON Structure

```json
{
  "dashboard": {
    "id": null,
    "title": "EOS M290 Manufacturing Overview",
    "tags": ["manufacturing", "eos", "3d-printing"],
    "timezone": "Asia/Singapore",
    "panels": [
      {
        "id": 1,
        "title": "Machine Status",
        "type": "stat",
        "targets": [
          {
            "query": "from(bucket: \"ami_eqpt\") |> range(start: -5m) |> filter(fn: (r) => r[\"_field\"] == \"EOS.Machine.Info.ProcessState\") |> last()",
            "refId": "A"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "mappings": [
              {"options": {"0": {"text": "Idle"}}, "type": "value"},
              {"options": {"1": {"text": "Printing"}}, "type": "value"},
              {"options": {"2": {"text": "Error"}}, "type": "value"}
            ]
          }
        }
      }
    ],
    "time": {
      "from": "now-6h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

## Best Practices

### Dashboard Organization
1. **Create dashboard folders** by area (Production, Maintenance, Quality)
2. **Use consistent naming** conventions
3. **Tag dashboards** for easy searching
4. **Set appropriate refresh rates** (5s for real-time, 1m for trends)

### Query Optimization
1. **Use appropriate time ranges** (don't query years of data for real-time panels)
2. **Apply aggregation** for long time ranges
3. **Filter early** in your Flux queries
4. **Use variables** for dynamic filtering

### Visual Design
1. **Use consistent color schemes** across dashboards
2. **Group related panels** in rows
3. **Add meaningful titles** and descriptions
4. **Include units** in axis labels
5. **Set appropriate Y-axis ranges** for better readability

## Troubleshooting Common Issues

### "No data" in panels
1. Check InfluxDB connection
2. Verify bucket name and token
3. Check time range (data might be outside selected range)
4. Test query in InfluxDB directly

### Slow dashboard loading
1. Reduce time range for expensive queries
2. Add more specific filters
3. Use aggregation (mean, max) for large datasets
4. Check if too many panels are refreshing simultaneously

### Alerts not firing
1. Verify alert query returns data
2. Check evaluation frequency
3. Ensure notification channels are configured
4. Check Grafana logs for error messages

This setup will give you professional-grade monitoring dashboards for your manufacturing environment!
