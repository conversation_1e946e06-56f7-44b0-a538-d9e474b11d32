import yaml
import struct
from pyModbusTCP.client import ModbusClient
from lib_loggers import set_logger

# Load configuration
try:
    with open("modbus_config.yaml") as config_file:
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The modbus_config.yaml file should be present.')

logger = set_logger()

def registers_to_float(register_pair):
    """Convert two Modbus registers back to IEEE 754 float"""
    
    if len(register_pair) != 2:
        raise ValueError("Expected exactly 2 registers for float conversion")
    
    try:
        # Combines the 2 16-bit registers into a single 32-bit value
        combined_value = (register_pair[0] << 16) | register_pair[1]
        packed_value = struct.pack('>I', combined_value)
        float_value = struct.unpack('>f', packed_value)[0]
        return float_value

    except Exception as e:
        logger.error(f"Error converting registers {register_pair} to float: {e}")
        return None

def read_single_register_pair(client, register_address):
    """Read 2 consecutive registers (for one IEEE 754 float)"""
    """Returns a list of 2 integers if successful, None otherwise"""
    # TODO: Implement basic Modbus reading
    # Steps:
    # 1. Use client.read_input_registers(register_address, 2)
    # 2. Check if response is valid
    # 3. Return the register values as list
    # 4. Handle errors gracefully
    
    logger.debug(f"Reading registers starting at 0x{register_address:04X}")
    
    try:
        # must use holding, not input registers for reading as simulator uses databank.set_words()
        # which writes to holding registers
        response = client.read_holding_registers(register_address, 2)
        if response and len(response) == 2:
            logger.debug(f"Raw register values at 0x{register_address:04X}: {response}")
            return response
        else:
            logger.debug(f"No valid response at 0x{register_address:04X}: {response}")
            return None
    except Exception as e:
        logger.error(f"Failed to read registers at 0x{register_address:04X}: {e}")
        return None

def read_power_meter_register(client, register_name, register_address):
    """Read a single power meter measurement"""
    """Returns the float value if successful, None otherwise"""
    # TODO: Combine register reading with float conversion
    # Steps:
    # 1. Read register pair using read_single_register_pair()
    # 2. Convert to float using registers_to_float()
    # 3. Return the engineering value
    # 4. Handle errors gracefully
    
    logger.debug(f"Reading {register_name} from address 0x{register_address:04X}")
    
    try:
        register_pair = read_single_register_pair(client, register_address)
        if register_pair:
            return registers_to_float(register_pair)
        else:
            return None
    except Exception as e:
        logger.error(f"Failed to read {register_name}: {e}")
        return None

def test_modbus_connection():
    """Test basic Modbus connectivity (requires simulator running)"""
    logger.info("=== TESTING MODBUS CONNECTION ===")
    
    config_env = config['development']
    meter_config = config_env['power_meters']['PowerMeter_A']
    modbus_config = config_env['modbus']
    
    ip_address = meter_config['ip_address']
    port = meter_config['port']
    timeout = modbus_config['timeout']
    unit_id = modbus_config['unit_id']
    
    logger.info(f"Connecting to Modbus device at {ip_address}:{port}")
    
    # TODO: Create Modbus client and test connection
    # Steps:
    # 1. Create ModbusClient with IP and port from config
    # 2. Set timeout
    # 3. Test connection using client.open()
    # 4. Try reading one register pair
    # 5. Close connection properly
    
    try:
        client = ModbusClient(host=ip_address, port=port, unit_id=unit_id, timeout=timeout)
        if client.open():
            logger.info("Modbus connection successful")
            read_power_meter_register(client, 'voltage_l1', meter_config['registers']['voltage_l1'])
            client.close()
            return True
        else:
            logger.error("Failed to connect to Modbus device")
            return False
    except Exception as e:
        logger.error(f"Modbus connection test failed: {e}")
        return False
    finally:
        if client.is_open:
            client.close()

def read_all_meter_data():
    """Read all configured registers from PowerMeter_A"""
    logger.info("=== READING ALL POWER METER DATA ===")
    
    config_env = config['development']
    meter_config = config_env['power_meters']['PowerMeter_A']
    modbus_config = config_env['modbus']
    
    ip_address = meter_config['ip_address']
    port = meter_config['port']
    registers = meter_config['registers']
    timeout = modbus_config['timeout']
    unit_id = modbus_config['unit_id']
    
    logger.info(f"Reading all data from PowerMeter_A at {ip_address}:{port}")
    
    # TODO: Read all configured registers
    # Steps:
    # 1. Create and open Modbus client
    # 2. Loop through all registers in config
    # 3. Read each register and convert to engineering units
    # 4. Log results with proper units (V, A, kW)
    # 5. Close connection
    
    try:
        # Your code here
        # Hint: for register_name, register_addr in registers.items():
        # Hint: value = read_power_meter_register(client, register_name, register_addr)
        # Hint: Add appropriate units (V for voltage, A for current, kW for power)
        client = ModbusClient(host=ip_address, port=port, unit_id=unit_id, timeout=timeout)
        if client.open():
            for register_name, register_addr in registers.items():
                value = read_power_meter_register(client, register_name, register_addr)
                match register_name:
                    case 'voltage_l1' | 'voltage_l2' | 'voltage_l3':
                        logger.info(f"{register_name}: {value} V")
                    case 'current_l1' | 'current_l2' | 'current_l3':
                        logger.info(f"{register_name}: {value} A")
                    case 'power_total':
                        logger.info(f"{register_name}: {value} kW")
                    case _:
                        logger.info(f"{register_name}: {value}")
            client.close()
    except Exception as e:
        logger.error(f"Failed to read meter data: {e}")
    finally:
        if client.is_open:
            client.close()

def demonstrate_modbus_concepts():
    """Educational demonstration of Modbus concepts"""
    logger.info("=== MODBUS CONCEPTS DEMONSTRATION ===")
    
    logger.info("Modbus TCP Protocol Basics:")
    logger.info("- Client-Server model: We are the client, power meter is the server")
    logger.info("- Function codes: We use 0x04 (Read Input Registers)")
    logger.info("- Register addressing: Each measurement has a specific address")
    logger.info("- Data encoding: IEEE 754 floats stored in 2 consecutive registers")
    
    logger.info("Register Map for Power Meters:")
    logger.info("- 0x0000-0x0001: L1 Voltage (Phase 1 voltage)")
    logger.info("- 0x0002-0x0003: L2 Voltage (Phase 2 voltage)")
    logger.info("- 0x0004-0x0005: L3 Voltage (Phase 3 voltage)")
    logger.info("- 0x0006-0x0007: L1 Current (Phase 1 current)")
    logger.info("- 0x0034-0x0035: Total Power (sum of all phases)")
    
    logger.info("Why these specific addresses?")
    logger.info("- Standardized by power meter manufacturer")
    logger.info("- Documented in device manual")
    logger.info("- Different manufacturers may use different addresses")

def main():
    """Phase 2: Basic Modbus Communication"""
    logger.info("=== PHASE 2: BASIC MODBUS COMMUNICATION ===")
    
    logger.info("  IMPORTANT: Make sure to run 'python modbus_simulator.py' first!")
    logger.info("The simulator must be running for these tests to work.")
    
    # Step 1: Learn Modbus concepts
    demonstrate_modbus_concepts()
    
    # Step 2: Test basic connection
    if test_modbus_connection():
        logger.info(" Basic Modbus connection successful")
        
        # Step 3: Read all meter data
        read_all_meter_data()
        
        logger.info(" Phase 2 complete! Ready for Phase 3 (Integration)")
        logger.info("Next step: Complete 'power_meter_client.py' using your Phase 1 & 2 skills")
    else:
        logger.error(" Modbus connection failed. Check that simulator is running.")
        logger.info("Run 'python modbus_simulator.py' in another terminal first.")

if __name__ == "__main__":
    main()
