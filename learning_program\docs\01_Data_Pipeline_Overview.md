# Data Visualization Pipeline Overview

## Complete Data Flow

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   EOS 3D Printers   │    │  Power Meters       │    │    Other Sources    │
│   (OPC UA Server)   │    │  (Modbus TCP)       │    │   (Future sensors)  │
└──────────┬──────────┘    └──────────┬──────────┘    └──────────┬──────────┘
           │                          │                          │
           │ Process Data             │ Power Data               │ Additional Data
           │ (Temperature,            │ (kW, Current,            │ (Environment,
           │  Pressure,               │  Voltage, etc.)          │  Quality, etc.)
           │  Layer Count)            │                          │
           ▼                          ▼                          ▼
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│ For EOS M290 OPCUA  │    │   For power meter   │    │   Future Scripts    │
│     main.py         │    │      main.py        │    │                     │
└──────────┬──────────┘    └──────────┬──────────┘    └──────────┬──────────┘
           │                          │                          │
           └─────────────────┬────────────────────────────────────┘
                             │
                             ▼
                    ┌─────────────────────┐
                    │     InfluxDB        │
                    │   Time Series DB    │
                    │                     │
                    │ • eos_opcua bucket  │
                    │ • power bucket      │
                    │ • other buckets     │
                    └──────────┬──────────┘
                             │
           ┌─────────────────┼─────────────────┐
           │                 │                 │
           ▼                 ▼                 ▼
┌─────────────────────┐ ┌─────────────────────┐ ┌─────────────────────┐
│    InfluxDB/        │ │      Grafana        │ │   Custom Analysis   │
│  Analysis Scripts   │ │    Dashboards       │ │     Scripts         │
│                     │ │                     │ │                     │
│ • compare_m290.py   │ │ • Real-time plots   │ │ • Machine Learning  │
│ • pull_data.py      │ │ • Alerts & alarms   │ │ • Statistical       │
│ • upload_data.py    │ │ • Historical trends │ │   analysis          │
└─────────────────────┘ └─────────────────────┘ └─────────────────────┘
           │                 │                 │
           ▼                 ▼                 ▼
┌─────────────────────┐ ┌─────────────────────┐ ┌─────────────────────┐
│   Process Reports   │ │   Live Monitoring   │ │  Research Insights  │
│   (.png files)     │ │   (Web interface)   │ │   (Publications)    │
└─────────────────────┘ └─────────────────────┘ └─────────────────────┘
```

## The 4 Essential Components

### 1. **Data Collection** (Folders: `For EOS M290 OPCUA`, `For power meter`)
- **Purpose**: Gather real-time data from machines
- **Technologies**: OPC UA, Modbus TCP, Python async programming
- **Output**: Continuous data streams to InfluxDB

### 2. **Data Storage** (InfluxDB Database)
- **Purpose**: Store time-series data efficiently
- **Technology**: InfluxDB time-series database
- **Features**: High-performance writes, flexible querying

### 3. **Data Analysis** (Folder: `InfluxDB`)
- **Purpose**: Advanced analysis beyond simple visualization
- **Technologies**: Python data science stack (pandas, numpy, matplotlib)
- **Output**: Automated reports, process insights

### 4. **Data Visualization** (Missing: Grafana Dashboards)
- **Purpose**: Real-time monitoring and alerting
- **Technology**: Grafana web interface
- **Output**: Live dashboards accessible via web browser

## What's Missing?

### Grafana Dashboard Configuration
The current setup is missing the **Grafana dashboard configurations** that would show:
- Real-time machine status
- Power consumption trends
- Process parameter monitoring
- Automated alerts when parameters exceed thresholds

### Typical Grafana Dashboards Would Include:
1. **Machine Overview**: Status of all EOS printers
2. **Power Monitoring**: Real-time power consumption
3. **Process Quality**: Temperature, pressure, oxygen levels
4. **Maintenance**: Equipment health metrics
5. **Alerts**: Automated notifications for anomalies

## How Data Gets Visualized

### Real-Time Visualization (Grafana)
```
InfluxDB → Grafana → Web Dashboard → Engineers/Operators
```

### Analysis & Reporting (Python Scripts)
```
InfluxDB → Python Scripts → Matplotlib Plots → Research/Engineering Reports
```

### Example Visualization Outputs:

#### From Grafana:
- Live temperature graphs
- Power consumption gauges
- Machine status indicators
- Historical trend lines

#### From Python Scripts:
- Pump-down consistency analysis plots
- Process optimization reports
- Statistical quality control charts
- Comparative performance studies

## Benefits of This Architecture

1. **Scalability**: Easy to add new machines or sensors
2. **Flexibility**: Multiple visualization options for different needs
3. **Reliability**: Data persists even if visualization tools fail
4. **Performance**: Time-series database optimized for this use case
5. **Integration**: Can feed data to other systems (ERP, MES, etc.)
