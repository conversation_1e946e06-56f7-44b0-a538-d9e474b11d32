# 🚀 Task 6 Setup Guide - Integration & Customization Capstone

## Prerequisites

### 1. Completed All Previous Tasks
- ✅ **Task 1**: Logging fundamentals
- ✅ **Task 2**: Threading concepts  
- ✅ **Task 3**: Network communication and Modbus
- ✅ **Task 4**: Time series data and InfluxDB
- ✅ **Task 5**: Asynchronous programming and OPC UA

### 2. System Requirements

**Hardware Requirements:**
- CPU: Multi-core processor (4+ cores recommended)
- RAM: 8GB minimum, 16GB recommended
- Storage: 10GB free space for logs and data
- Network: Ethernet connection for machine communication

**Software Requirements:**
- Python 3.8+ with asyncio support
- InfluxDB 2.x (from Task 4)
- Network scanning tools (nmap)
- Git for version control

### 3. Python Environment Setup

```bash
# Create virtual environment
python -m venv task06_capstone_env
source task06_capstone_env/bin/activate  # On Windows: task06_capstone_env\Scripts\activate

# Install all dependencies
pip install -r requirements.txt

# Verify critical installations
python -c "import asyncua; print('OPC UA client ready')"
python -c "import pyModbusTCP; print('Modbus client ready')"
python -c "import influxdb_client; print('InfluxDB client ready')"
```

### 4. Network and Security Setup

**Network Access:**
- Ensure access to target machine networks
- Configure firewall rules for industrial protocols
- Set up VPN access if required for remote machines

**Security Considerations:**
- Store credentials securely (environment variables)
- Use SSL/TLS for production connections
- Implement proper authentication for all protocols

## Understanding the Capstone Challenge

### What Makes This Different

This capstone task is **fundamentally different** from previous tasks:

1. **Real-World Complexity**: You'll work with actual project patterns and production requirements
2. **Integration Focus**: Combine all previous learning into cohesive systems
3. **Adaptation Skills**: Learn to quickly adapt to new machines and protocols
4. **Production Readiness**: Build systems that can operate 24/7 in manufacturing environments

### Skills You'll Demonstrate

**Technical Skills:**
- Multi-protocol integration (OPC UA + Modbus + InfluxDB)
- Production-grade error handling and recovery
- System health monitoring and alerting
- Performance optimization and scaling
- Automated deployment and configuration

**Engineering Skills:**
- System architecture and design
- Requirements analysis and documentation
- Troubleshooting and maintenance procedures
- Adaptation to new requirements
- Knowledge transfer and documentation

## Phase-by-Phase Setup

### Phase 1: Machine Discovery & Analysis

**Setup Requirements:**
```bash
# Install network scanning tools
# On Ubuntu/Debian:
sudo apt-get install nmap

# On Windows:
# Download nmap from https://nmap.org/download.html

# On macOS:
brew install nmap
```

**Configuration:**
- Update `config/discovery_config.yaml` with your network ranges
- Configure protocol testing parameters
- Set up documentation output directories

**Testing Setup:**
```bash
# Test network connectivity
ping 192.168.1.1

# Test nmap installation
nmap --version

# Test Python network libraries
python -c "import socket; print('Network libraries ready')"
```

### Phase 2: System Integration & Architecture

**Setup Requirements:**
- InfluxDB running and accessible (from Task 4)
- OPC UA simulators or real machines available
- Modbus devices or simulators accessible
- Configuration files properly set up

**Configuration Files:**
1. **`config/environments.yaml`** - Environment-specific settings
2. **`config/machines.yaml`** - Machine and device definitions
3. **`config/system.yaml`** - System-wide configuration

**Testing Integration:**
```bash
# Test InfluxDB connection
python -c "
from influxdb_client import InfluxDBClient
client = InfluxDBClient(url='http://localhost:8086', token='your-token', org='your-org')
print('InfluxDB connection:', client.health())
"

# Test OPC UA simulator
python -c "
import asyncio
from asyncua import Client
async def test():
    async with Client('opc.tcp://localhost:4840') as client:
        print('OPC UA connection successful')
asyncio.run(test())
"
```

### Phase 3: Production Deployment & Monitoring

**Setup Requirements:**
- System monitoring tools configured
- Alerting mechanisms set up (email, Slack, etc.)
- Log rotation and management configured
- Backup and recovery procedures established

**Production Checklist:**
- [ ] All credentials stored securely
- [ ] SSL/TLS certificates configured
- [ ] Monitoring and alerting tested
- [ ] Backup procedures verified
- [ ] Documentation complete and accessible

### Phase 4: Customization & Adaptation

**Setup Requirements:**
- Template system configured
- Documentation generation tools ready
- Version control system set up
- Deployment automation tested

## Common Setup Issues and Solutions

### Network Discovery Issues

**Issue**: "nmap command not found"
```bash
# Solution: Install nmap
sudo apt-get install nmap  # Linux
brew install nmap          # macOS
# Windows: Download from nmap.org
```

**Issue**: "Permission denied for network scanning"
```bash
# Solution: Run with appropriate permissions
sudo python machine_discovery.py  # Linux/macOS
# Windows: Run as Administrator
```

### OPC UA Connection Issues

**Issue**: "Connection refused to OPC UA server"
- **Check**: Server is running and accessible
- **Check**: Firewall allows port 4840/4843
- **Check**: Authentication credentials are correct
- **Solution**: Test with OPC UA client tools first

**Issue**: "Certificate validation failed"
- **Check**: Server certificate configuration
- **Solution**: Disable certificate validation for testing
- **Production**: Properly configure certificates

### Modbus Connection Issues

**Issue**: "Modbus connection timeout"
- **Check**: Device IP address and port
- **Check**: Network connectivity to device
- **Check**: Modbus unit ID is correct
- **Solution**: Test with Modbus testing tools

### InfluxDB Integration Issues

**Issue**: "Bucket not found"
- **Check**: Bucket name in configuration
- **Check**: Organization and token permissions
- **Solution**: Create bucket or update configuration

**Issue**: "Write timeout"
- **Check**: InfluxDB server performance
- **Check**: Batch size and flush intervals
- **Solution**: Optimize write parameters

## Performance Optimization

### System Performance

**CPU Optimization:**
- Use appropriate async concurrency levels
- Implement efficient data processing algorithms
- Monitor CPU usage and adjust accordingly

**Memory Optimization:**
- Implement proper data buffering strategies
- Use memory-efficient data structures
- Monitor memory usage and implement cleanup

**Network Optimization:**
- Batch network operations where possible
- Implement connection pooling
- Use appropriate timeout values

### Database Performance

**InfluxDB Optimization:**
- Use appropriate batch sizes (50-100 points)
- Implement proper data retention policies
- Use efficient tag and field structures
- Monitor query performance

## Debugging and Troubleshooting

### Debugging Tools

**Python Debugging:**
```bash
# Enable asyncio debug mode
export PYTHONASYNCIODEBUG=1

# Use detailed logging
export LOG_LEVEL=DEBUG

# Memory profiling
python -m memory_profiler your_script.py
```

**Network Debugging:**
```bash
# Monitor network traffic
tcpdump -i any port 4840  # OPC UA traffic
tcpdump -i any port 502   # Modbus traffic

# Test connectivity
telnet machine_ip 4840    # Test OPC UA port
telnet machine_ip 502     # Test Modbus port
```

### Common Debugging Scenarios

**Scenario 1**: Data not appearing in InfluxDB
- Check write API configuration
- Verify data point structure
- Check batch writing and flush intervals
- Monitor InfluxDB logs

**Scenario 2**: OPC UA subscriptions not working
- Verify subscription creation
- Check node IDs and permissions
- Monitor subscription handler calls
- Test with manual node reads first

**Scenario 3**: System performance degradation
- Monitor CPU and memory usage
- Check async task management
- Analyze network latency
- Review error rates and retry logic

## Success Metrics

### Phase 1 Success Criteria
- [ ] Successfully discovers machines on network
- [ ] Identifies protocols and capabilities
- [ ] Generates comprehensive documentation
- [ ] Provides integration recommendations

### Phase 2 Success Criteria
- [ ] Integrates multiple protocols seamlessly
- [ ] Handles errors gracefully
- [ ] Stores data efficiently in InfluxDB
- [ ] Provides system health monitoring

### Phase 3 Success Criteria
- [ ] Operates reliably for 24+ hours
- [ ] Handles simulated failures gracefully
- [ ] Provides meaningful alerts and monitoring
- [ ] Includes comprehensive documentation

### Phase 4 Success Criteria
- [ ] Adapts quickly to new machine types
- [ ] Provides reusable templates and components
- [ ] Includes deployment automation
- [ ] Demonstrates mastery of all concepts

## Getting Help

### Documentation Resources
- [Real Project Files](../../../For%20power%20meter/) - Study actual implementation patterns
- [InfluxDB Documentation](https://docs.influxdata.com/)
- [OPC UA Specification](https://reference.opcfoundation.org/)
- [Modbus Protocol Guide](https://modbus.org/docs/Modbus_Application_Protocol_V1_1b3.pdf)

### Debugging Resources
- Enable detailed logging in all components
- Use network monitoring tools to verify communication
- Test individual components before integration
- Create minimal test cases for troubleshooting

### Community Resources
- Industrial automation forums
- Python async programming communities
- OPC UA and Modbus user groups
- InfluxDB community forums

Ready to build production-ready manufacturing monitoring systems? Start with Phase 1! 🔧
