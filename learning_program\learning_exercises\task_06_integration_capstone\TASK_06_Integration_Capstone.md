# 🔧 Task 6: Integration & Customization - Capstone Project

## 🎯 **The Manufacturing Integration Challenge**

### **Welcome to the Real World**

Congratulations! You've mastered the fundamentals. Now it's time to tackle the **real challenge** that manufacturing engineers face every day:

**The Scenario**: You're tasked with monitoring a new manufacturing facility with:
- **Unknown machines** from different manufacturers
- **Mixed protocols** (some OPC UA, some Modbus, some proprietary)
- **Varying data requirements** (real-time alerts vs historical analysis)
- **Production constraints** (24/7 operation, minimal downtime)
- **Budget limitations** (reuse existing infrastructure where possible)

**Your Mission**: Build a complete, production-ready monitoring system that can:
1. **Discover** what machines and protocols are available
2. **Integrate** multiple data sources seamlessly
3. **Scale** to handle dozens of machines
4. **Adapt** quickly when new equipment is added
5. **Operate reliably** in production environments

### **Why This Matters**

This capstone task bridges the gap between learning exercises and **real industrial deployment**. The patterns you'll learn here are exactly what you'll use when working with the actual project files:

- **`For power meter/main.py`** - Production power monitoring with threading and error handling
- **`For EOS M290 OPCUA/main.py`** - Real-time machine monitoring with async OPC UA
- **`InfluxDB/` scripts** - Data analysis and visualization for manufacturing insights

---

## 🧠 **Core Integration Concepts**

### **1. System Architecture Patterns**

**Layered Architecture** (like the real project):
```
┌─────────────────────────────────────────────────────────────┐
│                    Analysis & Visualization                 │
│              (InfluxDB queries, Grafana dashboards)        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Data Storage Layer                       │
│                     (InfluxDB TSDB)                        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Data Collection Layer                      │
│         (OPC UA clients, Modbus clients, processors)       │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Machine Interface Layer                   │
│              (Industrial machines and devices)             │
└─────────────────────────────────────────────────────────────┘
```

### **2. Configuration Management Strategy**

**Environment-Based Configuration** (from real project):
```yaml
# config/environments.yaml
development:
  influxdb:
    url: "http://localhost:8086"
    token: "dev-token"
    bucket: "dev-manufacturing"
  
production:
  influxdb:
    url: "http://influxdb.ami.modelfactory.sg:8086"
    token: "prod-token"
    bucket: "ami_eqpt"

# config/machines.yaml
machines:
  EOS_SI3654:
    type: "3d_printer"
    protocols:
      opcua:
        url: "opc.tcp://**************:4843"
        authentication: true
      power_monitoring:
        protocol: "modbus"
        ip: "***************"
        port: 8899
```

### **3. Error Handling and Recovery Patterns**

**Production-Grade Error Handling** (from real project patterns):
```python
# Multi-level error handling strategy
class ProductionMonitoringSystem:
    def __init__(self):
        self.retry_strategies = {
            'connection_error': ExponentialBackoffRetry(max_attempts=5),
            'data_error': ImmediateRetry(max_attempts=3),
            'timeout_error': LinearBackoffRetry(max_attempts=10)
        }
        
    async def handle_machine_error(self, machine_id, error_type, error):
        # Log error with context
        logger.error(f"Machine {machine_id} error: {error_type} - {error}")
        
        # Apply appropriate retry strategy
        strategy = self.retry_strategies.get(error_type)
        if strategy and strategy.should_retry():
            await strategy.wait()
            return True  # Retry
        else:
            # Escalate to system administrator
            await self.alert_system_admin(machine_id, error)
            return False  # Don't retry
```

### **4. Data Flow Integration Patterns**

**Real Project Data Flow**:
```python
# Pattern from power meter main.py
class IntegratedDataCollector:
    def __init__(self):
        # Multiple data sources
        self.opcua_clients = {}
        self.modbus_clients = {}
        self.influx_client = InfluxDBClient(...)
        
    async def collect_and_store_data(self):
        # Concurrent data collection
        tasks = []
        
        # OPC UA data collection
        for machine_id, client in self.opcua_clients.items():
            tasks.append(self.collect_opcua_data(machine_id, client))
            
        # Modbus data collection  
        for meter_id, client in self.modbus_clients.items():
            tasks.append(self.collect_modbus_data(meter_id, client))
            
        # Execute all collections concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process and store results
        await self.process_and_store_results(results)
```

---

## 🔍 **Phase 1: Machine Discovery & Analysis**

### **Learning Objectives**
- Master network discovery and protocol identification techniques
- Learn to analyze machine capabilities and data structures
- Build comprehensive machine documentation and mapping
- Develop systematic approaches to unknown system integration

### **Real-World Connection**
This phase teaches the skills you need when encountering new machines in production:
- How to identify what protocols a machine supports
- How to discover available data points and their meanings
- How to test connectivity before full integration
- How to document findings for future reference

### **Phase 1 Implementation: `machine_discovery.py`**

```python
"""
Phase 1: Machine Discovery & Analysis

Learning Goals:
- Master network discovery and protocol identification
- Analyze machine capabilities systematically
- Build comprehensive documentation and mapping
- Develop systematic integration approaches

This phase teaches real-world skills for unknown machine integration.
"""

import asyncio
import socket
import yaml
from datetime import datetime
import subprocess
import json
from asyncua import Client
from pyModbusTCP.client import ModbusClient

class MachineDiscoverySystem:
    """
    Comprehensive machine discovery and analysis system.
    
    Learning Goal: Master systematic approach to unknown machine integration
    Real Project Pattern: Skills needed when adding new machines to production
    """
    
    def __init__(self, config):
        """
        Initialize machine discovery system.
        
        TODO: Set up discovery infrastructure
        """
        self.config = config
        self.discovered_machines = {}
        self.protocol_testers = {
            'opcua': self.test_opcua_connectivity,
            'modbus': self.test_modbus_connectivity,
            'http': self.test_http_connectivity
        }
        
        print("=== MACHINE DISCOVERY SYSTEM ===")
        print("Goal: Systematically discover and analyze industrial machines\n")
        
    async def discover_network_devices(self, ip_range="***********/24"):
        """
        Discover devices on the network using multiple techniques.
        
        TODO: Implement comprehensive network discovery
        Learning Goal: Understand how to find unknown machines
        
        Args:
            ip_range (str): IP range to scan (CIDR notation)
            
        Returns:
            list: List of discovered devices with basic info
        """
        print(f"=== NETWORK DEVICE DISCOVERY ===")
        print(f"Scanning network range: {ip_range}")
        
        discovered_devices = []
        
        # TODO: Method 1 - Ping sweep to find active devices
        print("1. Performing ping sweep...")
        # TODO: Implement ping sweep across IP range
        # Hint: Use subprocess to call ping command
        # Consider: How do you handle different OS ping commands?
        
        # TODO: Method 2 - Port scanning for common industrial ports
        print("2. Scanning for industrial protocol ports...")
        industrial_ports = {
            4840: 'OPC UA',
            502: 'Modbus TCP',
            1883: 'MQTT',
            80: 'HTTP',
            443: 'HTTPS',
            8080: 'HTTP Alt',
            8899: 'Custom Modbus (PW11)'
        }
        
        # TODO: For each discovered device, scan for industrial ports
        # TODO: Document which ports are open and what they might be
        
        # TODO: Method 3 - Protocol-specific discovery
        print("3. Testing protocol-specific connectivity...")
        # TODO: Try to connect using different protocols
        # TODO: Document successful connections and capabilities
        
        return discovered_devices
    
    async def analyze_machine_capabilities(self, device_info):
        """
        Analyze what a discovered machine can do.
        
        TODO: Implement comprehensive capability analysis
        Learning Goal: Understand how to explore unknown machine features
        
        Args:
            device_info (dict): Basic device information from discovery
            
        Returns:
            dict: Detailed capability analysis
        """
        print(f"\n=== ANALYZING MACHINE: {device_info['ip']} ===")
        
        capabilities = {
            'ip': device_info['ip'],
            'protocols': {},
            'data_points': {},
            'analysis_timestamp': datetime.now().isoformat(),
            'recommendations': []
        }
        
        # TODO: Test each potential protocol
        for protocol, port in device_info.get('open_ports', {}).items():
            print(f"Testing {protocol} on port {port}...")
            
            if protocol == 'OPC UA':
                # TODO: Analyze OPC UA capabilities
                opcua_info = await self.analyze_opcua_machine(device_info['ip'], port)
                capabilities['protocols']['opcua'] = opcua_info
                
            elif protocol == 'Modbus TCP':
                # TODO: Analyze Modbus capabilities
                modbus_info = await self.analyze_modbus_device(device_info['ip'], port)
                capabilities['protocols']['modbus'] = modbus_info
                
            # TODO: Add analysis for other protocols
            
        # TODO: Generate integration recommendations
        capabilities['recommendations'] = self.generate_integration_recommendations(capabilities)
        
        return capabilities
    
    async def analyze_opcua_machine(self, ip, port):
        """
        Analyze OPC UA machine capabilities in detail.
        
        TODO: Implement comprehensive OPC UA analysis
        Learning Goal: Master OPC UA machine exploration
        """
        opcua_info = {
            'connection_status': 'unknown',
            'server_info': {},
            'node_structure': {},
            'data_types': {},
            'security_requirements': {}
        }
        
        try:
            # TODO: Test basic connection
            url = f"opc.tcp://{ip}:{port}/"
            
            # TODO: Try connection without authentication
            # TODO: If that fails, try with common credentials
            # TODO: Explore server information and capabilities
            # TODO: Browse node structure and document data points
            # TODO: Identify data types and update frequencies
            
            async with Client(url=url) as client:
                # TODO: Get server information
                # TODO: Browse node tree and document structure
                # TODO: Identify key data points for monitoring
                # TODO: Test read operations and data quality
                pass
                
        except Exception as e:
            opcua_info['connection_error'] = str(e)
            
        return opcua_info
    
    async def analyze_modbus_device(self, ip, port):
        """
        Analyze Modbus device capabilities.
        
        TODO: Implement comprehensive Modbus analysis
        Learning Goal: Master Modbus device exploration
        """
        modbus_info = {
            'connection_status': 'unknown',
            'device_info': {},
            'register_map': {},
            'data_types': {}
        }
        
        try:
            # TODO: Test Modbus connection
            client = ModbusClient(host=ip, port=port, auto_open=True, auto_close=True)
            
            # TODO: Test connection and basic operations
            # TODO: Scan for available registers
            # TODO: Identify data types and meanings
            # TODO: Document register map for future use
            
        except Exception as e:
            modbus_info['connection_error'] = str(e)
            
        return modbus_info
    
    def generate_integration_recommendations(self, capabilities):
        """
        Generate recommendations for integrating this machine.
        
        TODO: Implement intelligent recommendation system
        Learning Goal: Develop systematic integration planning
        """
        recommendations = []
        
        # TODO: Analyze available protocols and recommend best approach
        # TODO: Identify key data points for monitoring
        # TODO: Suggest data collection frequencies
        # TODO: Recommend error handling strategies
        # TODO: Suggest configuration templates
        
        return recommendations
    
    async def generate_machine_documentation(self, machine_analysis):
        """
        Generate comprehensive documentation for discovered machine.
        
        TODO: Create detailed machine documentation
        Learning Goal: Build maintainable system documentation
        """
        # TODO: Create machine profile document
        # TODO: Include connection details and protocols
        # TODO: Document data points and their meanings
        # TODO: Include integration recommendations
        # TODO: Create configuration templates
        
        pass

async def main():
    """Phase 1: Master machine discovery and analysis"""
    print("=== PHASE 1: MACHINE DISCOVERY & ANALYSIS ===")
    print("Building skills for real-world machine integration\n")
    
    # Load configuration
    with open("config/discovery_config.yaml") as f:
        config = yaml.safe_load(f)
    
    discovery_system = MachineDiscoverySystem(config)
    
    # TODO: Complete discovery workflow
    # 1. Discover network devices
    devices = await discovery_system.discover_network_devices()
    
    # 2. Analyze each discovered machine
    for device in devices:
        analysis = await discovery_system.analyze_machine_capabilities(device)
        await discovery_system.generate_machine_documentation(analysis)
    
    print("\n=== PHASE 1 COMPLETE ===")
    print("Key skills mastered:")
    print("✓ Network discovery and device identification")
    print("✓ Protocol testing and capability analysis")
    print("✓ Machine documentation and mapping")
    print("✓ Integration planning and recommendations")
    print("\nReady for Phase 2: System Integration & Architecture")

if __name__ == "__main__":
    asyncio.run(main())
```

---

## 🎯 **Critical Thinking Challenges for Phase 1**

### **Challenge 1: Unknown Machine Protocol**
You discover a machine at IP ************ with these open ports:
- Port 80 (HTTP)
- Port 8080 (HTTP Alt)
- Port 9999 (Unknown)
- Port 12345 (Unknown)

**Your Task**: Develop a systematic approach to:
- Identify what protocols these ports might be using
- Test connectivity and discover capabilities
- Document findings for future integration
- Recommend integration strategies

**Think About**: How do you balance thorough investigation with time constraints?

### **Challenge 2: Mixed Protocol Environment**
You find a manufacturing cell with:
- 3 machines supporting OPC UA (different namespaces and security)
- 2 power meters using Modbus TCP (different register layouts)
- 1 environmental sensor with HTTP API
- 1 legacy machine with proprietary protocol

**Your Task**: Design an integration strategy that:
- Handles all protocols efficiently
- Provides unified data access
- Scales to additional machines
- Maintains performance and reliability

**Think About**: How do you balance complexity vs maintainability?

---

## 🏗️ **Phase 2: System Integration & Architecture**

### **Learning Objectives**
- Design and implement complete monitoring system architectures
- Master integration patterns from the real project files
- Build production-ready configuration management systems
- Implement robust error handling and recovery strategies

### **Real-World Connection**
This phase teaches you to build systems like the actual project:
- Multi-protocol integration (OPC UA + Modbus + InfluxDB)
- Production configuration management
- Error handling and recovery patterns
- Performance optimization for industrial scale

### **Phase 2 Implementation: `integrated_monitoring_system.py`**

```python
"""
Phase 2: System Integration & Architecture

Learning Goals:
- Design complete monitoring system architectures
- Master integration patterns from real project
- Build production-ready configuration management
- Implement robust error handling and recovery

This phase builds production-ready systems using real project patterns.
"""

import asyncio
import yaml
import threading
from datetime import datetime, timezone
from pathlib import Path
import logging
from typing import Dict, List, Any

# Import all the components from previous tasks
from asyncua import Client as OPCUAClient
from pyModbusTCP.client import ModbusClient
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import ASYNCHRONOUS

class IntegratedMonitoringSystem:
    """
    Complete production monitoring system integrating all protocols.

    Learning Goal: Build systems matching real project architecture
    Real Project Pattern: Integration of power meter + OPC UA + InfluxDB systems
    """

    def __init__(self, config_path="config/"):
        """
        Initialize integrated monitoring system.

        TODO: Set up complete system architecture
        """
        self.config_path = Path(config_path)
        self.config = self.load_configuration()

        # System components
        self.opcua_clients = {}
        self.modbus_clients = {}
        self.influx_client = None
        self.data_processors = {}

        # System state
        self.running = False
        self.monitoring_tasks = {}
        self.system_health = {}

        # Error handling
        self.error_handlers = {}
        self.retry_strategies = {}

        print("=== INTEGRATED MONITORING SYSTEM ===")
        print("Goal: Build production-ready multi-protocol monitoring\n")

    def load_configuration(self):
        """
        Load configuration from multiple files (real project pattern).

        TODO: Implement comprehensive configuration loading
        Learning Goal: Master production configuration management
        """
        config = {}

        # TODO: Load environment configuration
        env_file = self.config_path / "environments.yaml"
        # TODO: Load machine configuration
        machines_file = self.config_path / "machines.yaml"
        # TODO: Load system configuration
        system_file = self.config_path / "system.yaml"

        # TODO: Merge configurations with environment override
        # TODO: Validate configuration completeness
        # TODO: Apply environment-specific settings

        return config

    async def initialize_system(self):
        """
        Initialize all system components.

        TODO: Implement complete system initialization
        Real Project Pattern: Initialization from main.py files
        """
        print("Initializing integrated monitoring system...")

        # TODO: Initialize InfluxDB client
        await self.initialize_influxdb()

        # TODO: Initialize OPC UA clients for each machine
        await self.initialize_opcua_clients()

        # TODO: Initialize Modbus clients for power meters
        await self.initialize_modbus_clients()

        # TODO: Initialize data processors
        await self.initialize_data_processors()

        # TODO: Initialize error handling systems
        await self.initialize_error_handling()

        print("✅ System initialization complete")

    async def initialize_influxdb(self):
        """
        Initialize InfluxDB client with production settings.

        TODO: Implement InfluxDB initialization
        Real Project Pattern: InfluxDB setup from actual project
        """
        # TODO: Get InfluxDB configuration
        influx_config = self.config['influxdb']

        # TODO: Create InfluxDB client
        # TODO: Test connection
        # TODO: Verify bucket exists
        # TODO: Set up write API with appropriate settings

    async def initialize_opcua_clients(self):
        """
        Initialize OPC UA clients for all configured machines.

        TODO: Implement OPC UA client initialization
        Real Project Pattern: OPC UA setup from EOS M290 project
        """
        opcua_machines = self.config.get('machines', {})

        for machine_id, machine_config in opcua_machines.items():
            if machine_config.get('protocols', {}).get('opcua'):
                # TODO: Create OPC UA client for this machine
                # TODO: Configure authentication if required
                # TODO: Test initial connection
                # TODO: Store client reference
                pass

    async def initialize_modbus_clients(self):
        """
        Initialize Modbus clients for power meters and other devices.

        TODO: Implement Modbus client initialization
        Real Project Pattern: Modbus setup from power meter project
        """
        # TODO: Initialize Modbus clients for each configured device
        # TODO: Test connections
        # TODO: Validate register access
        # TODO: Store client references

    async def start_monitoring(self):
        """
        Start monitoring all configured machines and devices.

        TODO: Implement complete monitoring startup
        Learning Goal: Master concurrent monitoring patterns
        """
        print("=== STARTING INTEGRATED MONITORING ===")
        self.running = True

        # TODO: Start OPC UA monitoring tasks
        for machine_id, client in self.opcua_clients.items():
            task = asyncio.create_task(
                self.monitor_opcua_machine(machine_id, client)
            )
            self.monitoring_tasks[f"opcua_{machine_id}"] = task

        # TODO: Start Modbus monitoring tasks
        for device_id, client in self.modbus_clients.items():
            task = asyncio.create_task(
                self.monitor_modbus_device(device_id, client)
            )
            self.monitoring_tasks[f"modbus_{device_id}"] = task

        # TODO: Start system health monitoring
        health_task = asyncio.create_task(self.monitor_system_health())
        self.monitoring_tasks["system_health"] = health_task

        # TODO: Start data processing and storage
        storage_task = asyncio.create_task(self.process_and_store_data())
        self.monitoring_tasks["data_storage"] = storage_task

        print(f"✅ Started {len(self.monitoring_tasks)} monitoring tasks")

    async def monitor_opcua_machine(self, machine_id, client):
        """
        Monitor single OPC UA machine (real project pattern).

        TODO: Implement OPC UA monitoring
        Real Project Pattern: Monitoring from EOS M290 main.py
        """
        print(f"Starting OPC UA monitoring for {machine_id}")

        while self.running:
            try:
                # TODO: Connect to machine
                # TODO: Set up subscriptions for real-time data
                # TODO: Handle data changes
                # TODO: Process and queue data for storage
                # TODO: Monitor connection health

                pass

            except Exception as e:
                # TODO: Handle OPC UA specific errors
                # TODO: Implement retry logic
                # TODO: Log errors appropriately
                await self.handle_opcua_error(machine_id, e)

    async def monitor_modbus_device(self, device_id, client):
        """
        Monitor single Modbus device (real project pattern).

        TODO: Implement Modbus monitoring
        Real Project Pattern: Monitoring from power meter main.py
        """
        print(f"Starting Modbus monitoring for {device_id}")

        while self.running:
            try:
                # TODO: Read Modbus registers
                # TODO: Process and validate data
                # TODO: Queue data for storage
                # TODO: Handle communication errors

                pass

            except Exception as e:
                # TODO: Handle Modbus specific errors
                await self.handle_modbus_error(device_id, e)

    async def process_and_store_data(self):
        """
        Process and store data from all sources.

        TODO: Implement unified data processing
        Learning Goal: Master data integration patterns
        """
        while self.running:
            try:
                # TODO: Collect data from all monitoring tasks
                # TODO: Apply data validation and processing
                # TODO: Create InfluxDB points
                # TODO: Batch write to InfluxDB
                # TODO: Handle storage errors

                await asyncio.sleep(1)  # Processing interval

            except Exception as e:
                # TODO: Handle data processing errors
                pass

class ConfigurationManager:
    """
    Manage configuration files and environment settings.

    Learning Goal: Master production configuration patterns
    Real Project Pattern: Configuration management from actual project
    """

    def __init__(self, config_path="config/"):
        self.config_path = Path(config_path)

    def load_environment_config(self, environment="development"):
        """
        Load environment-specific configuration.

        TODO: Implement environment configuration loading
        """
        # TODO: Load base configuration
        # TODO: Apply environment overrides
        # TODO: Validate configuration
        # TODO: Return merged configuration
        pass

    def validate_configuration(self, config):
        """
        Validate configuration completeness and correctness.

        TODO: Implement comprehensive configuration validation
        """
        # TODO: Check required fields
        # TODO: Validate connection parameters
        # TODO: Check data type consistency
        # TODO: Validate security settings
        pass

class ErrorHandlingSystem:
    """
    Comprehensive error handling and recovery system.

    Learning Goal: Master production error handling
    Real Project Pattern: Error handling from actual project files
    """

    def __init__(self):
        self.error_counts = {}
        self.recovery_strategies = {}

    async def handle_connection_error(self, component, error):
        """
        Handle connection errors with appropriate recovery.

        TODO: Implement connection error handling
        """
        # TODO: Log error with context
        # TODO: Determine recovery strategy
        # TODO: Implement exponential backoff
        # TODO: Alert if persistent failures
        pass

    async def handle_data_error(self, component, error):
        """
        Handle data processing errors.

        TODO: Implement data error handling
        """
        # TODO: Validate data quality
        # TODO: Apply data correction if possible
        # TODO: Log data quality issues
        # TODO: Continue operation with degraded data
        pass

async def main():
    """Phase 2: Build complete integrated monitoring system"""
    print("=== PHASE 2: SYSTEM INTEGRATION & ARCHITECTURE ===")
    print("Building production-ready monitoring systems\n")

    # Create integrated monitoring system
    monitoring_system = IntegratedMonitoringSystem()

    try:
        # Initialize all components
        await monitoring_system.initialize_system()

        # Start monitoring
        await monitoring_system.start_monitoring()

        # Run until interrupted
        print("System running. Press Ctrl+C to stop.")
        while True:
            await asyncio.sleep(1)

    except KeyboardInterrupt:
        print("\nShutdown requested...")
    except Exception as e:
        print(f"System error: {e}")
    finally:
        # TODO: Implement graceful shutdown
        pass

if __name__ == "__main__":
    asyncio.run(main())
```

---

## 📊 **Phase 3: Production Deployment & Monitoring**

### **Learning Objectives**
- Master production deployment procedures and best practices
- Implement comprehensive system health monitoring and alerting
- Build performance optimization and scaling strategies
- Develop maintenance and troubleshooting procedures

### **Real-World Connection**
This phase teaches production-ready deployment skills:
- 24/7 operation requirements and monitoring
- System health checking and alerting
- Performance optimization for industrial scale
- Maintenance procedures and troubleshooting

### **Phase 3 Implementation: `system_health_monitor.py`**

```python
"""
Phase 3: Production Deployment & Monitoring

Learning Goals:
- Master production deployment procedures
- Implement system health monitoring and alerting
- Build performance optimization strategies
- Develop maintenance and troubleshooting procedures

This phase ensures systems are production-ready and maintainable.
"""

import asyncio
import psutil
import yaml
from datetime import datetime, timezone, timedelta
import logging
import smtplib
from email.mime.text import MIMEText
from pathlib import Path

class SystemHealthMonitor:
    """
    Comprehensive system health monitoring and alerting.

    Learning Goal: Master production system monitoring
    Real Project Pattern: Health monitoring for 24/7 operation
    """

    def __init__(self, config):
        """
        Initialize system health monitor.

        TODO: Set up health monitoring infrastructure
        """
        self.config = config
        self.health_metrics = {}
        self.alert_thresholds = config.get('health_monitoring', {})
        self.alert_history = {}

        print("=== SYSTEM HEALTH MONITOR ===")
        print("Goal: Ensure 24/7 production system reliability\n")

    async def monitor_system_health(self):
        """
        Continuously monitor system health metrics.

        TODO: Implement comprehensive health monitoring
        """
        while True:
            try:
                # TODO: Collect system metrics
                metrics = await self.collect_health_metrics()

                # TODO: Analyze metrics against thresholds
                alerts = await self.analyze_health_metrics(metrics)

                # TODO: Send alerts if necessary
                if alerts:
                    await self.send_health_alerts(alerts)

                # TODO: Store health metrics for trending
                await self.store_health_metrics(metrics)

                await asyncio.sleep(30)  # Health check every 30 seconds

            except Exception as e:
                logging.error(f"Health monitoring error: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def collect_health_metrics(self):
        """
        Collect comprehensive system health metrics.

        TODO: Implement health metrics collection
        """
        metrics = {
            'timestamp': datetime.now(timezone.utc),
            'system': {},
            'application': {},
            'network': {},
            'database': {}
        }

        # TODO: System metrics (CPU, memory, disk)
        metrics['system'] = {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'load_average': psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0
        }

        # TODO: Application metrics
        # TODO: Network connectivity metrics
        # TODO: Database health metrics

        return metrics

    async def analyze_health_metrics(self, metrics):
        """
        Analyze metrics and identify issues.

        TODO: Implement intelligent health analysis
        """
        alerts = []

        # TODO: Check system resource thresholds
        # TODO: Check application performance metrics
        # TODO: Check connectivity and data flow
        # TODO: Check database performance

        return alerts

class DeploymentManager:
    """
    Manage production deployment procedures.

    Learning Goal: Master production deployment practices
    """

    def __init__(self, config):
        self.config = config

    async def deploy_system(self, environment="production"):
        """
        Deploy system to production environment.

        TODO: Implement production deployment procedures
        """
        print(f"=== DEPLOYING TO {environment.upper()} ===")

        # TODO: Pre-deployment checks
        await self.run_pre_deployment_checks()

        # TODO: Deploy configuration
        await self.deploy_configuration(environment)

        # TODO: Deploy application
        await self.deploy_application()

        # TODO: Post-deployment verification
        await self.run_post_deployment_checks()

        print("✅ Deployment complete")

    async def run_pre_deployment_checks(self):
        """
        Run checks before deployment.

        TODO: Implement pre-deployment validation
        """
        # TODO: Check system requirements
        # TODO: Validate configuration files
        # TODO: Test database connectivity
        # TODO: Verify network access
        pass

class PerformanceOptimizer:
    """
    Optimize system performance for production scale.

    Learning Goal: Master performance optimization
    """

    def __init__(self, monitoring_system):
        self.monitoring_system = monitoring_system

    async def optimize_performance(self):
        """
        Continuously optimize system performance.

        TODO: Implement performance optimization
        """
        # TODO: Monitor performance metrics
        # TODO: Identify bottlenecks
        # TODO: Apply optimization strategies
        # TODO: Validate improvements
        pass

async def main():
    """Phase 3: Master production deployment and monitoring"""
    print("=== PHASE 3: PRODUCTION DEPLOYMENT & MONITORING ===")
    print("Building production-ready deployment and monitoring\n")

    # Load configuration
    with open("config/system.yaml") as f:
        config = yaml.safe_load(f)

    # TODO: Initialize health monitoring
    health_monitor = SystemHealthMonitor(config)

    # TODO: Initialize deployment manager
    deployment_manager = DeploymentManager(config)

    # TODO: Run deployment procedures
    await deployment_manager.deploy_system()

    # TODO: Start health monitoring
    await health_monitor.monitor_system_health()

if __name__ == "__main__":
    asyncio.run(main())
```

---

## 🔧 **Phase 4: Customization & Adaptation**

### **Learning Objectives**
- Master adaptation techniques for new machine types
- Build reusable components and templates
- Create comprehensive documentation and deployment guides
- Complete final capstone project demonstrating all skills

### **Final Capstone Project Implementation**

Choose one of these capstone projects to demonstrate mastery:

### **Option A: Multi-Protocol Manufacturing Cell**
Build a complete monitoring system for a simulated manufacturing cell:
- 2+ OPC UA machines (EOS-style 3D printers)
- 3+ Modbus devices (power meters, environmental sensors)
- Complete InfluxDB integration with analysis
- Production-ready deployment and monitoring

### **Option B: Adaptive Monitoring Framework**
Create a framework that automatically adapts to new machines:
- Automatic network discovery and protocol identification
- Dynamic configuration generation
- Plug-and-play machine integration
- Self-documenting system capabilities

### **Option C: Production System Replica**
Replicate the actual project architecture:
- Mirror the power meter monitoring system exactly
- Implement EOS-style OPC UA monitoring
- Create production-grade InfluxDB integration
- Build comprehensive analysis and reporting

---

## 🎯 **Final Integration Challenge**

### **The Complete Manufacturing Monitoring System**

**Your Mission**: Build a production-ready system that demonstrates mastery of:

1. **Machine Discovery**: Automatically find and analyze new machines
2. **Multi-Protocol Integration**: Handle OPC UA, Modbus, and other protocols
3. **Data Management**: Efficient collection, processing, and storage
4. **Error Handling**: Robust recovery from all types of failures
5. **Production Deployment**: 24/7 operation with monitoring and alerting
6. **Adaptability**: Easy extension for new machine types

**Success Criteria**:
- System operates reliably for 24+ hours without intervention
- Handles simulated failures gracefully (network outages, machine disconnections)
- Provides meaningful insights through data analysis and visualization
- Includes comprehensive documentation and deployment procedures
- Demonstrates understanding of all previous task concepts

**Bonus Challenges**:
- Implement automatic machine discovery and configuration
- Add predictive maintenance capabilities
- Create web-based monitoring dashboard
- Build automated deployment and scaling procedures

---

## 📚 **Preparing for Real Project Work**

### **Working with Actual Project Files**

After completing this capstone, you'll be ready to:

**`For power meter/main.py`**:
- Understand the threading and Modbus patterns
- Modify for new power meter types
- Extend data collection capabilities
- Improve error handling and recovery

**`For EOS M290 OPCUA/main.py`**:
- Work with async OPC UA patterns
- Add new machine types and protocols
- Enhance subscription and data handling
- Optimize performance for multiple machines

**`InfluxDB/` analysis scripts**:
- Create new analysis and visualization scripts
- Build automated reporting systems
- Develop predictive analytics capabilities
- Optimize query performance

### **Next Steps in Your Journey**

1. **Master the Real Project**: Work with actual project files confidently
2. **Extend Capabilities**: Add new machines and protocols
3. **Optimize Performance**: Scale to production requirements
4. **Build New Features**: Add analytics, alerting, and automation
5. **Share Knowledge**: Document and teach others

---

## 🚀 **Congratulations!**

You've completed the comprehensive manufacturing monitoring learning path! You now have the skills to:

- **Discover and integrate** any industrial machine or protocol
- **Build production-ready** monitoring systems
- **Handle real-world challenges** with confidence
- **Adapt and extend** systems for new requirements
- **Work effectively** with complex industrial software

**You're ready for real manufacturing engineering challenges!** 🎯
