"""OPC UA Node Discovery Package

A modular Python package for discovering OPC UA nodes and generating machine configuration files.
"""

from .discoverer import OPCUANodeDiscoverer
from .config_generator import ConfigGenerator
from .field_normalizer import FieldNameNormalizer
from .connection import OPCUAConnection
from .node_browser import Node<PERSON>rowser

__all__ = [
    'OPCUANodeDiscoverer', 
    'ConfigGenerator', 
    'FieldNameNormalizer',
    'OPCUAConnection',
    'NodeBrowser'
]
__version__ = "1.0.0"
