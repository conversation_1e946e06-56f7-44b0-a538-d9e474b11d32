''' To use:
from lib_loggers import set_logger
logger = set_logger()
'''

import os
import logging
import inspect

def set_logger():
    # Identify caller
    # inspect.currentframe() gets the current execution frame
    # .f_back() identifies the next outer frame object (this frame's caller)
    # This allows us to identify the caller of the block in the call stack
    caller_frame = inspect.currentframe().f_back

    #  Get caller's filename
    # inspect.getframeinfo(caller_frame) gives us detailed information about the frame
    # .filename() accesses the filename attribute of the frame
    # os.path.basename strips away the directory path, leaving only the filename
    # Consequently, this allows us to identify which file is making a log call
    caller_file = os.path.basename(inspect.getframeinfo(caller_frame).filename)
    
    # Build log paths
    # os.path.dirname(__file__) gets the directory containing the current Python file
    # os.path.join() combines this directory with 'logs' using the appropriate path separator for the os
    # This creates a log_dir path
    log_dir = os.path.join(os.path.dirname(__file__),'logs')
    # os.path.dirname(__file__) gets the directory containing the current Python file
    # caller_file.rsplit('.',1)[0] removes the file extension from the calling file's name
    preferred_log_filename = os.path.join(os.path.dirname(__file__),'logs',caller_file.rsplit('.',1)[0]+'.log')
    
    # Create log directories if doesn't exist
    try:
        os.makedirs(os.path.dirname(preferred_log_filename))
    except FileExistsError:
        pass

    # 1. Set global logging level (Root logger, Global, catch logging from other libraries)
    # Configures the global logging system
    # Sets up the root logger with default settings that applies to all loggers
    # format= defines how log messages will be structured.
    # \t inserts tabs
    # datefmt= specifies exact format for timestamps
    # level= means only ERROR and above (CRITICAL) will be processed by the root logger
    logging.basicConfig(format='%(asctime)s\t%(name)s\t%(thread)d\t%(levelname)s\t%(message)s',
                        datefmt="%Y-%m-%d %H:%M:%S",
                        level=logging.ERROR)

    # 2. Create a specific logger for this calling file (Custom logger, per file, handles logging for this script only)
    # getLogger() is a factory function that creates or retrieves larger objects
    # Creates a logger using the name of the calling file
    # Follows a singleton pattern, only one instance. Stored in global logger dict
    logger = logging.getLogger(caller_file.rsplit('.',1)[0])
    # Isolates the logger from the logging hierarchy
    # Messages will not bubble up to parent loggers
    logger.propagate = False  # Don't send to parent loggers

    # 3. Create formatter for consistent log format
    # This formatter applied to specific handlers rather than global console output with basicConfig()
    formatter = logging.Formatter('%(asctime)s\t%(name)s\t%(thread)d\t%(levelname)s\t%(message)s',"%Y-%m-%d %H:%M:%S")

    # 4. File handler - writes to log file (output destinations for loggers)
    # Creates a FileHandler that writes all log messages to the specified log file
    # logger.addHandler(fh) registers this as one of the logger's output destinations
    fh = logging.FileHandler(preferred_log_filename)
    fh.setLevel(logging.DEBUG)  # Log everything to file
    fh.setFormatter(formatter)
    logger.addHandler(fh)

    # 5. Console handler - writes to screen
    # .StreamHandler() defaults to writing to stdout/console
    # Inherits the logger's level by default
    ch = logging.StreamHandler()
    ch.setFormatter(formatter)
    logger.addHandler(ch)

    # 6. Set logger level and return
    logger.setLevel(logging.DEBUG)
    return logger


# 1. Set root logger settings
# 2. Create local logger for specific files
# 3. logger.propagate=false to prevent bubbling up to root ledger
# 4. Create formatter
# 5. Create handlers
# 6. Connect handlers to logger
# 7. Use logger in actual code