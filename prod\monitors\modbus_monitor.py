import asyncio
import logging
import struct
import time
from datetime import datetime
from pyModbusTCP.client import ModbusClient

log = logging.getLogger("manufacturing_monitor.modbus")

async def monitor_modbus_machine(machine_name, machine_config, influx_writer):
    """
    Continuously monitors a single Modbus machine (e.g., power meter).

    Connects to the Modbus server, polls specified registers, and writes
    data to InfluxDB. It will automatically attempt to reconnect
    if the connection is lost.

    Args:
        machine_name (str): The name of the machine being monitored.
        machine_config (dict): Configuration dictionary for the machine, including Modbus connection details and register mappings.
        influx_writer (InfluxWriter): Instance responsible for writing data points to InfluxDB.
    """
    modbus_config = machine_config.get("modbus", {})
    host = modbus_config.get("host")
    port = modbus_config.get("port", 502)  # Default Modbus port
    timeout = modbus_config.get("timeout", 1)

    if not host:
        log.error(f"Modbus host is missing for machine '{machine_name}'. Skipping this monitor.")
        return

    log.info(f"Starting Modbus monitor for '{machine_name}' at {host}:{port}")
    
    monitoring_config = machine_config.get("monitoring", {})
    poll_interval = monitoring_config.get("poll_interval_seconds", 5)
    timeout_duration = monitoring_config.get("connection_timeout_seconds", 10)
    retry_delay = monitoring_config.get("retry_delay_seconds", 5)
    retry_attempts = monitoring_config.get("retry_attempts", 3)
    
    registers = machine_config.get("registers", {})
    if not registers:
        log.warning(f"No registers configured for machine '{machine_name}'")
        return

    static_tags = {
        "type": machine_config["type"],
        "name": machine_name,
        "protocol": "modbus"
    }

    # Initialize Modbus client
    modbus_handler = ModbusDataHandler(host, port, timeout, influx_writer, machine_name, registers, static_tags)
    
    # Main monitoring loop with reconnection logic
    while True:
        try:
            if await modbus_handler.connect():
                log.info(f"Successfully connected to Modbus server: {host}:{port}")
                
                # Main polling loop
                while modbus_handler.is_connected():
                    try:
                        await modbus_handler.poll_and_write_data()
                        await asyncio.sleep(poll_interval)
                    except Exception as e:
                        log.error(f"Error during data polling for '{machine_name}': {e}")
                        break
            else:
                log.error(f"Failed to connect to Modbus server after {retry_attempts} attempts")
                
        except Exception as e:
            log.error(f"Error monitoring Modbus server at {host}:{port}: {e}")
        
        log.info(f"Attempting to reconnect in {retry_delay} seconds...")
        await asyncio.sleep(retry_delay)


class ModbusDataHandler:
    """Handles Modbus communication and data processing."""
    
    def __init__(self, host, port, timeout, influx_writer, machine_name, registers, static_tags=None):
        """
        Initializes the Modbus data handler.

        Args:
            host (str): Modbus server host address
            port (int): Modbus server port
            timeout (int): Connection timeout in seconds
            influx_writer: An instance of our InfluxWriter class
            machine_name (str): The name of the machine for tagging
            registers (dict): Dictionary mapping register names to their configurations
            static_tags (dict): A dictionary of constant metadata tags for the machine
        """
        self.host = host
        self.port = port
        self.timeout = timeout
        self.influx_writer = influx_writer
        self.machine_name = machine_name
        self.registers = registers
        self.static_tags = static_tags or {}
        self.client = None
        self.connected = False
        
        # Performance counters
        self.counters = {
            'read_success': 0,
            'read_failure': 0,
            'consecutive_read_errors': 0,
            'connect_success': 0,
            'connect_failure': 0,
            'connection_dropped': 0
        }
        
        log.info(f"ModbusDataHandler initialized for machine '{machine_name}' with {len(registers)} registers")

    async def connect(self):
        """
        Establish connection to the Modbus server using asyncio.to_thread.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Run ModbusClient creation in a thread
            self.client = await asyncio.to_thread(
                ModbusClient,
                host=self.host,
                port=self.port,
                auto_open=True,
                auto_close=False,
                timeout=self.timeout
            )
            
            # Test the connection by attempting to read a register
            if self.client.is_open:
                first_register = next(iter(self.registers.values()))
                test_result = await asyncio.to_thread(
                    self.client.read_input_registers,
                    first_register['address'],
                    first_register['count']
                )
                
                if test_result is not None:
                    self.connected = True
                    self.counters['connect_success'] += 1
                    log.debug(f"Successfully connected and tested Modbus connection to {self.host}:{self.port}")
                    return True
                else:
                    log.error(f"Modbus connection test failed for {self.host}:{self.port}")
                    self.connected = False
                    self.counters['connect_failure'] += 1
                    return False
            else:
                log.error(f"Failed to open Modbus connection to {self.host}:{self.port}")
                self.connected = False
                self.counters['connect_failure'] += 1
                return False
                
        except Exception as e:
            log.error(f"Exception during Modbus connection to {self.host}:{self.port}: {e}")
            self.connected = False
            self.counters['connect_failure'] += 1
            return False

    def is_connected(self):
        """Check if the Modbus client is connected."""
        return self.connected and self.client and self.client.is_open

    async def poll_and_write_data(self):
        """
        Poll all configured registers and write data to InfluxDB.
        """
        if not self.is_connected():
            log.warning(f"Modbus client for '{self.machine_name}' is not connected")
            return

        timestamp = datetime.utcnow()
        fields = {}
        
        for register_name, register_config in self.registers.items():
            try:
                value = await self._read_register(register_config)
                if value is not None:
                    fields[register_name] = value
                    log.debug(f"Read {register_name}: {value} from {self.machine_name}")
                else:
                    log.warning(f"Failed to read register '{register_name}' from {self.machine_name}")
                    
            except Exception as e:
                log.error(f"Error reading register '{register_name}' from {self.machine_name}: {e}")
                self.counters['consecutive_read_errors'] += 1
                
                # If we have too many consecutive errors, mark as disconnected
                if self.counters['consecutive_read_errors'] > 3:
                    log.error(f"Too many consecutive read errors for {self.machine_name}. Marking as disconnected.")
                    self.connected = False
                    self.counters['connection_dropped'] += 1
                    return

        # Write collected data to InfluxDB
        if fields:
            try:
                self.influx_writer.write("power_consumption", fields, self.static_tags, timestamp)
                self.counters['read_success'] += 1
                self.counters['consecutive_read_errors'] = 0
                
                # Throttled logging
                if self._should_log_success():
                    log.debug(f"Successfully wrote {len(fields)} fields to InfluxDB for {self.machine_name}")
                    
            except Exception as e:
                log.error(f"Error writing data to InfluxDB for {self.machine_name}: {e}")
                self.counters['read_failure'] += 1

    async def _read_register(self, register_config):
        """
        Read a single register and decode the value using asyncio.to_thread.
        
        Args:
            register_config (dict): Register configuration containing address, count, type, etc.
            
        Returns:
            float or None: Decoded register value or None if read failed
        """
        address = register_config['address']
        count = register_config['count']
        data_type = register_config.get('type', 'float32')
        
        try:
            # Read the registers using asyncio.to_thread
            raw_data = await asyncio.to_thread(
                self.client.read_input_registers,
                address,
                count
            )
            
            if raw_data is None:
                self.counters['read_failure'] += 1
                return None
                
            # Decode based on data type
            if data_type == 'float32' and count == 2:
                return self._decode_float32(raw_data)
            elif data_type == 'uint16' and count == 1:
                return float(raw_data[0])
            elif data_type == 'int16' and count == 1:
                # Convert from unsigned to signed if needed
                value = raw_data[0]
                if value > 32767:
                    value -= 65536
                return float(value)
            else:
                log.warning(f"Unsupported data type '{data_type}' with count {count}")
                return None
                
        except Exception as e:
            log.error(f"Error reading register at address 0x{address:04X}: {e}")
            self.counters['read_failure'] += 1
            return None

    @staticmethod
    def _decode_float32(register_pair):
        """
        Decode two 16-bit registers into a 32-bit float.
        
        Args:
            register_pair (list): List of two 16-bit register values
            
        Returns:
            float: Decoded float value
        """
        if len(register_pair) != 2:
            raise ValueError(f"Expected 2 registers for float32, got {len(register_pair)}")
            
        # Combine the two 16-bit registers into a 32-bit value
        combined_value = (register_pair[0] << 16) | register_pair[1]
        
        # Pack as unsigned 32-bit integer, then unpack as float
        packed_value = struct.pack('>I', combined_value)
        float_value = struct.unpack('>f', packed_value)[0]
        
        return float_value

    def _should_log_success(self):
        """
        Determine if we should log this successful read operation.
        Used to throttle log output.
        
        Returns:
            bool: True if we should log, False otherwise
        """
        total_reads = self.counters['read_success'] + self.counters['read_failure']
        
        if total_reads < 50:
            return True
        elif (50 <= total_reads < 500) and (total_reads % 20 == 0):
            return True
        elif (500 <= total_reads < 5000) and (total_reads % 100 == 0):
            return True
        elif (total_reads >= 5000) and (total_reads % 1000 == 0):
            return True
        else:
            return False

    def close(self):
        """Close the Modbus connection."""
        if self.client:
            try:
                self.client.close()
                self.connected = False
                log.debug(f"Closed Modbus connection for {self.machine_name}")
            except Exception as e:
                log.error(f"Error closing Modbus connection for {self.machine_name}: {e}")