development:
  machines:
    Machine_localhost_4840:
      monitoring:
        connection_timeout_seconds: 10
        retry_attempts: 3
        retry_delay_seconds: 5
        subscription_interval_ms: 1000
      nodes:
        buildprogress: ns=2;i=9
        current: ns=2;i=7
        humidity: ns=2;i=5
        pressure: ns=2;i=4
        temperature: ns=2;i=3
      opcua:
        authentication:
          enabled: false
          password: null
          username: null
        namespaces:
        - 2
        url: opc.tcp://localhost:4840
      type: unknown

  influxdb:
    url: "http://localhost:8086"
    token: "MyInitialAdminToken0=="
    org: "docs"
    bucket: "phase4_manufacturing_sensors"
    batch_size: 50
    flush_interval_seconds: 5
    connection_timeout_seconds: 10
production:
  influxdb:
    url: "http://influxdb.ami.modelfactory.sg:8086"
    token: "BAUefNIXOaHhiGcJlHSOXUPeZFsECwKuw3Cz72EUr9fOtWZdLwYlHLUQn7Pzq__qqesPFjXYV2hyjCGItHx__Q==" # Token for "Generic RW token for test bucket"
    org: "266e2e2e067fbe5b" #AMI
    bucket: "testbucket"
    batch_size: 100
    flush_interval_seconds: 10
    connection_timeout_seconds: 15
