# ⚡ Task 5: Asynchronous Programming & OPC UA Integration

## 🎯 **The Industrial Real-Time Challenge**

### **Why Async Programming is Critical in Manufacturing**

Imagine you're monitoring a manufacturing facility with multiple machines:
- **EOS 3D Printer**: Needs temperature monitoring every 2 seconds
- **CNC Machine**: Requires vibration data every 1 second  
- **Power Meters**: Collect energy data every 5 seconds
- **Environmental Sensors**: Sample air quality every 10 seconds

**The Problem with Sequential Programming**:
```python
# This blocks everything while waiting for each machine
def monitor_sequential():
    temp = read_eos_temperature()      # Takes 2 seconds
    vibration = read_cnc_vibration()   # Takes 1 second  
    power = read_power_meter()         # Takes 3 seconds
    air = read_air_quality()           # Takes 1 second
    # Total: 7 seconds per cycle - too slow for real-time monitoring!
```

**The Async Solution**:
```python
# This monitors all machines simultaneously
async def monitor_concurrent():
    tasks = [
        read_eos_temperature(),    # All start at the same time
        read_cnc_vibration(),      # No waiting for others
        read_power_meter(),        # Concurrent execution
        read_air_quality()         # Maximum efficiency
    ]
    results = await asyncio.gather(*tasks)  # Wait for all to complete
    # Total: 3 seconds (limited by slowest operation)
```

### **Real Manufacturing Benefits**
- **Faster Response**: Detect problems in seconds, not minutes
- **Higher Throughput**: Monitor more machines with same resources
- **Better Reliability**: One slow machine doesn't block others
- **Real-time Alerts**: Immediate notification of critical events

---

## 🧠 **Core Async Concepts You Must Master**

### **1. Understanding the Event Loop**

Think of the event loop as a factory supervisor managing multiple workers:

```python
# The event loop coordinates multiple tasks
async def factory_supervisor():
    # Start multiple monitoring tasks
    eos_task = asyncio.create_task(monitor_eos_printer())
    cnc_task = asyncio.create_task(monitor_cnc_machine())
    power_task = asyncio.create_task(monitor_power_meter())
    
    # Supervisor coordinates all tasks
    await asyncio.gather(eos_task, cnc_task, power_task)
```

**Key Insight**: The event loop switches between tasks when they're waiting (I/O operations), maximizing CPU utilization.

### **2. Async vs Await Patterns**

```python
# async: Declares a function as asynchronous
async def read_sensor_data():
    # await: Pauses this function until operation completes
    # Other tasks can run while this waits
    data = await sensor.read()  # Non-blocking wait
    return data

# Calling async functions
result = await read_sensor_data()  # In async context
# OR
result = asyncio.run(read_sensor_data())  # From sync context
```

### **3. Async Context Managers**

Critical for resource management in industrial systems:

```python
# Automatic connection cleanup
async with OPCUAClient(url="opc.tcp://machine:4840") as client:
    # Connection automatically established
    data = await client.read_node("temperature")
    # Connection automatically closed, even if error occurs
```

### **4. Concurrent Task Management**

```python
# Pattern 1: Fire and forget
task = asyncio.create_task(background_monitoring())

# Pattern 2: Wait for completion
result = await some_operation()

# Pattern 3: Wait for multiple tasks
results = await asyncio.gather(task1, task2, task3)

# Pattern 4: Race condition (first to complete wins)
winner = await asyncio.wait_for(slow_operation(), timeout=5.0)
```

---

## 📡 **OPC UA Protocol Fundamentals**

### **What is OPC UA?**

**OPC UA (Open Platform Communications Unified Architecture)** is the industrial standard for machine communication:

- **Standardized**: Works across different manufacturers and platforms
- **Secure**: Built-in authentication and encryption
- **Real-time**: Supports subscriptions for immediate data updates
- **Rich Data Model**: Complex data structures, not just simple values

### **OPC UA Data Model**

Think of OPC UA as a file system for machine data:

```
Machine Root
├── 📁 Machine Info
│   ├── 📄 SerialNumber: "EOS_SI3654"
│   ├── 📄 Model: "M290"
│   └── 📄 Status: "Running"
├── 📁 Sensors
│   ├── 📄 Temperature: 245.6°C
│   ├── 📄 Pressure: 2.3 bar
│   └── 📄 Humidity: 45.2%
└── 📁 Process Data
    ├── 📄 LayerHeight: 0.03mm
    ├── 📄 PowerLevel: 85%
    └── 📄 BuildProgress: 67%
```

### **Node Identifiers**

Each data point has a unique identifier:
```python
# Examples from real EOS machine
temperature_node = "ns=4;s=EOS.Machine.Environment.Temperature"
pressure_node = "ns=4;s=EOS.Machine.ProcessChamber.Pressure"
status_node = "ns=4;s=EOS.Machine.Status.Current"

# ns=4: Namespace 4 (machine-specific)
# s=: String identifier type
# EOS.Machine.Environment.Temperature: Hierarchical path
```

### **OPC UA Operations**

```python
# Read single value (one-time)
value = await node.read_value()

# Read with metadata (timestamp, quality)
data_value = await node.read_data_value()
print(f"Value: {data_value.Value}")
print(f"Timestamp: {data_value.SourceTimestamp}")
print(f"Quality: {data_value.StatusCode}")

# Subscribe to changes (real-time)
subscription = await client.create_subscription(500, handler)
await subscription.subscribe_data_change([node])
```

---

## 🔧 **Phase 1: Async Fundamentals**

### **Learning Objectives**
- Master async/await syntax and concepts
- Understand event loops and task management
- Practice concurrent programming patterns
- Learn async context managers and resource handling

### **Setup Requirements**

Create `requirements.txt`:
```
asyncio-mqtt>=0.11.0
asyncua>=1.0.0
influxdb-client>=1.36.0
PyYAML>=6.0
matplotlib>=3.5.0
numpy>=1.21.0
aiofiles>=0.8.0
```

### **Configuration Design Challenge**

Create `config.yaml` - think about async system configuration:

```yaml
development:
  # Async system configuration
  event_loop:
    debug_mode: true
    slow_callback_duration: 0.1  # Log slow operations
    
  # Simulated machines for learning
  machines:
    # TODO: Design machine configurations
    # Think about: How would you configure multiple machines for concurrent monitoring?
    # Consider: Connection timeouts, retry policies, data collection intervals
    
  # OPC UA simulation settings
  opcua_simulation:
    enable_server: true
    server_port: 4840
    update_interval_ms: 1000
    node_count: 50
    
  # Integration with Task 4 (InfluxDB)
  influxdb:
    url: "http://localhost:8086"
    token: "your-token"
    org: "your-org"
    bucket: "async_manufacturing"
    batch_size: 100
    flush_interval_seconds: 5
```

### **Phase 1 Implementation: `phase1_async_fundamentals.py`**

```python
"""
Phase 1: Asynchronous Programming Fundamentals

Learning Goals:
- Master async/await syntax and concepts
- Understand event loops and concurrency
- Practice task management and coordination
- Learn async context managers and resource handling

This phase builds the foundation for industrial async programming.
"""

import asyncio
import time
import random
from datetime import datetime
import yaml

class AsyncFundamentals:
    def __init__(self):
        """
        Initialize async learning environment.
        
        TODO: Set up async learning infrastructure
        """
        print("Async Fundamentals Learning Environment")
        
    async def demonstrate_sync_vs_async(self):
        """
        Show the dramatic difference between sync and async approaches.
        
        Learning Goal: Understand why async matters for industrial systems
        """
        print("=== SYNC VS ASYNC DEMONSTRATION ===")
        
        # Simulate machine operations with different response times
        machines = {
            "EOS_Printer": 2.0,      # 2 second response time
            "CNC_Machine": 1.5,      # 1.5 second response time  
            "Power_Meter": 3.0,      # 3 second response time
            "Air_Quality": 1.0       # 1 second response time
        }
        
        # TODO: Implement synchronous version
        print("1. Synchronous (blocking) approach:")
        sync_start = time.time()
        # TODO: Call each machine sequentially and measure total time
        sync_total = time.time() - sync_start
        print(f"Synchronous total time: {sync_total:.2f} seconds")
        
        # TODO: Implement asynchronous version
        print("\n2. Asynchronous (concurrent) approach:")
        async_start = time.time()
        # TODO: Call all machines concurrently and measure total time
        async_total = time.time() - async_start
        print(f"Asynchronous total time: {async_total:.2f} seconds")
        
        # TODO: Calculate and display performance improvement
        improvement = (sync_total - async_total) / sync_total * 100
        print(f"Performance improvement: {improvement:.1f}%")
        
    async def simulate_machine_read(self, machine_name, response_time):
        """
        Simulate reading data from a machine with realistic timing.
        
        TODO: Implement realistic machine simulation
        Args:
            machine_name (str): Name of the machine
            response_time (float): Simulated response time in seconds
            
        Returns:
            dict: Simulated machine data
        """
        print(f"  Starting read from {machine_name}...")
        
        # TODO: Use await asyncio.sleep() to simulate network delay
        # TODO: Generate realistic machine data
        # TODO: Return structured data with timestamp
        
        pass
    
    async def practice_task_management(self):
        """
        Learn different patterns for managing async tasks.
        
        Learning Goal: Master task creation, coordination, and cleanup
        """
        print("\n=== TASK MANAGEMENT PATTERNS ===")
        
        # TODO: Pattern 1 - Fire and forget tasks
        print("1. Fire and forget pattern:")
        # TODO: Create background tasks that run independently
        
        # TODO: Pattern 2 - Gather results from multiple tasks
        print("2. Gather pattern (wait for all):")
        # TODO: Start multiple tasks and wait for all to complete
        
        # TODO: Pattern 3 - Race condition (first to complete)
        print("3. Race pattern (first wins):")
        # TODO: Start multiple tasks, return when first completes
        
        # TODO: Pattern 4 - Timeout handling
        print("4. Timeout pattern:")
        # TODO: Implement operations with timeout limits
        
    async def learn_async_context_managers(self):
        """
        Master async context managers for resource management.
        
        Learning Goal: Understand automatic resource cleanup in async code
        Critical for industrial systems that must handle connection failures
        """
        print("\n=== ASYNC CONTEXT MANAGERS ===")
        
        # TODO: Implement and demonstrate async context manager
        # TODO: Show automatic cleanup even when errors occur
        # TODO: Compare with manual resource management
        
    async def handle_async_errors(self):
        """
        Learn error handling patterns in async code.
        
        Manufacturing Reality: Networks fail, machines disconnect, timeouts occur
        """
        print("\n=== ASYNC ERROR HANDLING ===")
        
        # TODO: Demonstrate different error scenarios
        # TODO: Show how to handle timeouts
        # TODO: Implement retry logic with exponential backoff
        # TODO: Show graceful degradation when some machines fail
        
class AsyncMachineSimulator:
    """
    Simulate async machine behavior for learning.
    
    TODO: Implement realistic async machine simulation
    """
    
    def __init__(self, name, response_time, failure_rate=0.05):
        self.name = name
        self.response_time = response_time
        self.failure_rate = failure_rate
        
    async def __aenter__(self):
        """Async context manager entry"""
        # TODO: Simulate connection establishment
        print(f"Connecting to {self.name}...")
        await asyncio.sleep(0.1)  # Connection delay
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        # TODO: Simulate connection cleanup
        print(f"Disconnecting from {self.name}")
        
    async def read_data(self):
        """
        Simulate reading data from machine.
        
        TODO: Implement realistic data reading with potential failures
        """
        # TODO: Simulate network delay
        # TODO: Simulate occasional failures
        # TODO: Return realistic machine data
        pass

async def main():
    """Phase 1: Master async fundamentals before building complex systems"""
    print("=== PHASE 1: ASYNCHRONOUS PROGRAMMING FUNDAMENTALS ===")
    
    fundamentals = AsyncFundamentals()
    
    # TODO: Complete each learning exercise
    await fundamentals.demonstrate_sync_vs_async()
    await fundamentals.practice_task_management()
    await fundamentals.learn_async_context_managers()
    await fundamentals.handle_async_errors()
    
    print("\n=== PHASE 1 COMPLETE ===")
    print("Key concepts mastered:")
    print("✓ Async/await syntax and event loops")
    print("✓ Concurrent task management patterns")
    print("✓ Async context managers for resource cleanup")
    print("✓ Error handling in asynchronous code")
    print("\nReady for Phase 2: OPC UA Client Development")

if __name__ == "__main__":
    asyncio.run(main())
```

---

## 🎯 **Critical Thinking Challenges for Phase 1**

### **Challenge 1: Concurrency Design**
You need to monitor 5 machines with different characteristics:
- Machine A: Fast response (0.5s), critical data, needs immediate alerts
- Machine B: Slow response (5s), historical data, can tolerate delays  
- Machine C: Variable response (1-10s), unreliable network
- Machine D: Batch operations (30s), large data transfers
- Machine E: Real-time events, unpredictable timing

**Your Task**: Design an async architecture that:
- Prioritizes critical machines
- Handles variable response times efficiently
- Manages resources effectively
- Provides appropriate error handling

**Think About**: How would you balance responsiveness vs resource usage?

### **Challenge 2: Error Recovery Strategy**
In manufacturing, failures are common:
- Network timeouts during data collection
- Machines going offline unexpectedly
- Temporary communication errors
- Resource exhaustion under high load

**Your Task**: Design error handling that:
- Distinguishes between recoverable and fatal errors
- Implements appropriate retry strategies
- Maintains system stability when components fail
- Provides meaningful diagnostics for operators

**Think About**: How do you balance robustness vs performance?

---

## 📡 **Phase 2: OPC UA Client Development**

### **Learning Objectives**
- Master OPC UA protocol fundamentals and client operations
- Implement asynchronous OPC UA connections and data reading
- Build real-time subscription systems for live data monitoring
- Handle OPC UA-specific error conditions and recovery

### **Real-World Connection**
This phase mirrors the actual project patterns:
- `For EOS M290 OPCUA/main.py` uses async OPC UA subscriptions for real-time monitoring
- `For EOS M290 OPCUA/opcua_test.py` demonstrates basic client operations
- Industrial systems rely on OPC UA for standardized machine communication

### **OPC UA Deep Dive**

#### **Understanding Node Hierarchies**
OPC UA organizes data in a tree structure:

```
Root
├── Objects
│   ├── Server (Server information)
│   └── Machine_EOS_SI3654
│       ├── Info
│       │   ├── SerialNumber: "SI3654"
│       │   ├── Model: "M290"
│       │   └── Status: "Running"
│       ├── Environment
│       │   ├── Temperature: 245.6
│       │   ├── Pressure: 2.3
│       │   └── Humidity: 45.2
│       └── Process
│           ├── LayerHeight: 0.03
│           ├── PowerLevel: 85
│           └── BuildProgress: 67
```

#### **Node Addressing Patterns**
```python
# String-based identifiers (most common in industrial systems)
temp_node = "ns=4;s=EOS.Machine.Environment.Temperature"
pressure_node = "ns=4;s=EOS.Machine.ProcessChamber.Pressure"

# Numeric identifiers (standard OPC UA nodes)
server_time = "i=2258"  # Standard server timestamp node

# GUID identifiers (less common)
guid_node = "ns=2;g=12345678-1234-1234-1234-123456789abc"
```

### **Phase 2 Implementation: `phase2_opcua_client.py`**

```python
"""
Phase 2: OPC UA Client Development

Learning Goals:
- Master OPC UA protocol and client operations
- Implement async connections and data reading
- Build real-time subscription systems
- Handle OPC UA errors and connection recovery

This phase builds on async fundamentals to create industrial communication systems.
"""

import asyncio
import yaml
from datetime import datetime, timezone
from asyncua import Client, Node
from asyncua.common.subscription import DataChangeNotif
import logging

class OPCUAClientBasics:
    """
    Learn OPC UA client fundamentals with async patterns.

    Learning Goal: Master industrial communication protocols
    Real Project Pattern: Similar to EOS M290 OPCUA implementation
    """

    def __init__(self, config):
        """
        Initialize OPC UA client learning environment.

        TODO: Set up client configuration and connection parameters
        """
        self.config = config
        self.client = None
        self.subscription = None

        print("OPC UA Client Learning Environment")

    async def learn_basic_connections(self):
        """
        Master OPC UA connection patterns.

        Learning Goal: Understand async connection management
        Real Project Pattern: Connection handling from main.py
        """
        print("=== OPC UA CONNECTION FUNDAMENTALS ===")

        # TODO: Learn different connection patterns

        # Pattern 1: Basic connection
        print("1. Basic connection pattern:")
        # TODO: Implement basic async connection
        # TODO: Test connection health
        # TODO: Handle connection errors

        # Pattern 2: Authenticated connection
        print("2. Authenticated connection pattern:")
        # TODO: Implement connection with username/password
        # TODO: Handle authentication failures

        # Pattern 3: Connection with automatic retry
        print("3. Robust connection with retry:")
        # TODO: Implement connection retry logic
        # TODO: Handle network timeouts

    async def explore_node_operations(self):
        """
        Learn different ways to read and interact with OPC UA nodes.

        Learning Goal: Master node operations and data access
        """
        print("\n=== NODE OPERATIONS ===")

        # TODO: Connect to OPC UA server (simulator or real)
        server_url = self.config['development']['opcua_simulation']['server_url']

        async with Client(url=server_url) as client:
            # TODO: Basic node reading
            print("1. Basic node reading:")
            # TODO: Read single values from different node types
            # TODO: Compare read_value() vs read_data_value()

            # TODO: Node browsing and discovery
            print("2. Node browsing:")
            # TODO: Browse server structure
            # TODO: Find available nodes programmatically
            # TODO: Display node hierarchy

            # TODO: Batch operations
            print("3. Batch operations:")
            # TODO: Read multiple nodes efficiently
            # TODO: Compare single vs batch read performance

    async def implement_subscriptions(self):
        """
        Build real-time data subscription system.

        Learning Goal: Master real-time data monitoring
        Real Project Pattern: Subscription handling from main.py
        """
        print("\n=== REAL-TIME SUBSCRIPTIONS ===")

        server_url = self.config['development']['opcua_simulation']['server_url']

        async with Client(url=server_url) as client:
            # TODO: Create subscription handler
            handler = OPCUADataHandler()

            # TODO: Create subscription with appropriate interval
            subscription = await client.create_subscription(500, handler)

            # TODO: Subscribe to multiple nodes
            nodes_to_monitor = [
                # TODO: Define nodes to monitor from config
            ]

            # TODO: Subscribe to data changes
            await subscription.subscribe_data_change(nodes_to_monitor)

            # TODO: Monitor for specified duration
            print("Monitoring real-time data for 30 seconds...")
            await asyncio.sleep(30)

            # TODO: Clean up subscription
            await subscription.delete()

    async def handle_opcua_errors(self):
        """
        Learn OPC UA-specific error handling patterns.

        Manufacturing Reality: OPC UA has specific error conditions
        """
        print("\n=== OPC UA ERROR HANDLING ===")

        # TODO: Handle common OPC UA errors
        # TODO: BadNodeIdUnknown - node doesn't exist
        # TODO: BadTimeout - operation timed out
        # TODO: BadSessionClosed - connection lost
        # TODO: BadTooManySessions - server overloaded

        # TODO: Implement recovery strategies for each error type

class OPCUADataHandler:
    """
    Handle incoming OPC UA data changes.

    Learning Goal: Process real-time industrial data
    Real Project Pattern: SubscriptionHandler from main.py
    """

    def __init__(self):
        """
        Initialize data handler.

        TODO: Set up data processing infrastructure
        """
        self.data_count = 0
        self.last_data_time = None

    def datachange_notification(self, node: Node, val, data: DataChangeNotif):
        """
        Handle incoming data change notifications.

        This is called automatically when subscribed data changes.

        Args:
            node: The OPC UA node that changed
            val: The new value
            data: Additional metadata about the change

        TODO: Implement data processing logic
        Real Project Pattern: Similar to datachange_notification in main.py
        """
        # TODO: Extract node information
        node_id = str(node.nodeid.Identifier)
        timestamp = data.monitored_item.Value.SourceTimestamp

        # TODO: Process the data change
        # TODO: Apply filtering logic
        # TODO: Store data or trigger actions
        # TODO: Update statistics

        self.data_count += 1
        self.last_data_time = datetime.now(timezone.utc)

        print(f"Data change: {node_id} = {val} at {timestamp}")

class OPCUAServerSimulator:
    """
    Simple OPC UA server simulator for learning.

    TODO: Implement basic OPC UA server for testing
    Learning Goal: Understand both sides of OPC UA communication
    """

    def __init__(self, config):
        """
        Initialize OPC UA server simulator.

        TODO: Set up simulated server with realistic nodes
        """
        self.config = config
        self.server = None

    async def start_server(self):
        """
        Start simulated OPC UA server.

        TODO: Implement server startup with simulated machine data
        """
        # TODO: Create server instance
        # TODO: Set up node structure
        # TODO: Add simulated machine nodes
        # TODO: Start server and begin data simulation
        pass

    async def simulate_machine_data(self):
        """
        Generate realistic machine data changes.

        TODO: Implement realistic data simulation
        """
        # TODO: Update temperature, pressure, status values
        # TODO: Simulate realistic manufacturing patterns
        # TODO: Include occasional anomalies or alerts
        pass

async def main():
    """Phase 2: Master OPC UA client development"""
    print("=== PHASE 2: OPC UA CLIENT DEVELOPMENT ===")

    # Load configuration
    with open("config.yaml") as f:
        config = yaml.safe_load(f)

    # TODO: Start OPC UA server simulator if enabled
    if config['development']['opcua_simulation']['enable_server']:
        simulator = OPCUAServerSimulator(config)
        # TODO: Start server in background task

    # Learn OPC UA client operations
    client_basics = OPCUAClientBasics(config)

    # TODO: Complete each learning exercise
    await client_basics.learn_basic_connections()
    await client_basics.explore_node_operations()
    await client_basics.implement_subscriptions()
    await client_basics.handle_opcua_errors()

    print("\n=== PHASE 2 COMPLETE ===")
    print("Key concepts mastered:")
    print("✓ OPC UA client connections and authentication")
    print("✓ Node operations and data reading")
    print("✓ Real-time subscriptions and event handling")
    print("✓ OPC UA error handling and recovery")
    print("\nReady for Phase 3: Industrial Monitoring System")

if __name__ == "__main__":
    asyncio.run(main())
```

---

## 📊 **Phase 3: Industrial Monitoring System**

### **Learning Objectives**
- Build complete multi-machine monitoring system
- Integrate async OPC UA with InfluxDB from Task 4
- Implement robust error handling and connection recovery
- Create production-ready industrial monitoring patterns

### **Real-World Integration**
This phase combines everything into a system similar to the actual project:
- Multiple machine monitoring like the real EOS system
- Data storage integration with InfluxDB
- Error handling and recovery patterns
- Performance monitoring and optimization

### **Phase 3 Implementation: `phase3_industrial_monitor.py`**

```python
"""
Phase 3: Industrial Monitoring System

Learning Goals:
- Build complete multi-machine monitoring system
- Integrate async OPC UA with InfluxDB
- Implement robust error handling and recovery
- Create production-ready monitoring patterns

This phase integrates all previous learning into a realistic industrial system.
"""

import asyncio
import yaml
from datetime import datetime, timezone
from asyncua import Client, Node
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import ASYNCHRONOUS
import logging

class IndustrialMonitoringSystem:
    """
    Complete industrial monitoring system using async patterns.

    Learning Goal: Build production-ready monitoring systems
    Real Project Pattern: Integration of all project components
    """

    def __init__(self, config):
        """
        Initialize industrial monitoring system.

        TODO: Set up multi-machine monitoring infrastructure
        """
        self.config = config
        self.machines = {}
        self.monitoring_tasks = {}
        self.influx_client = None
        self.running = False

        print("Industrial Monitoring System initialized")

    async def start_monitoring(self):
        """
        Start monitoring all configured machines.

        TODO: Implement concurrent machine monitoring
        Learning Goal: Apply async patterns to real industrial scenarios
        """
        print("=== STARTING INDUSTRIAL MONITORING SYSTEM ===")

        # TODO: Initialize InfluxDB client
        # TODO: Create monitoring tasks for each machine
        # TODO: Start all monitoring tasks concurrently
        # TODO: Set up system health monitoring

        self.running = True

        machines_config = self.config['development']['machines']

        # TODO: Create monitoring task for each machine
        for machine_name, machine_config in machines_config.items():
            # TODO: Create async task for machine monitoring
            # TODO: Store task reference for management
            pass

        # TODO: Start system health monitoring
        health_task = asyncio.create_task(self.monitor_system_health())

        # TODO: Wait for all tasks or handle shutdown
        try:
            # TODO: Run monitoring tasks
            pass
        except KeyboardInterrupt:
            print("Shutdown requested...")
        finally:
            await self.stop_monitoring()

    async def monitor_single_machine(self, machine_name, machine_config):
        """
        Monitor a single machine using async OPC UA patterns.

        TODO: Implement robust single-machine monitoring
        Args:
            machine_name (str): Name of the machine
            machine_config (dict): Machine configuration

        Learning Goal: Apply all previous learning to real monitoring
        """
        print(f"Starting monitoring for {machine_name}")

        while self.running:
            try:
                # TODO: Connect to machine using async OPC UA
                # TODO: Set up subscriptions for real-time data
                # TODO: Handle data changes and store to InfluxDB
                # TODO: Monitor connection health
                # TODO: Handle disconnections and reconnect

                pass

            except Exception as e:
                # TODO: Implement comprehensive error handling
                # TODO: Log errors appropriately
                # TODO: Implement retry logic
                # TODO: Handle different error types differently

                print(f"Error monitoring {machine_name}: {e}")
                await asyncio.sleep(5)  # Wait before retry

    async def monitor_system_health(self):
        """
        Monitor overall system health and performance.

        TODO: Implement system-wide health monitoring
        Learning Goal: System-level monitoring and diagnostics
        """
        while self.running:
            # TODO: Check connection status for all machines
            # TODO: Monitor data collection rates
            # TODO: Check InfluxDB connection health
            # TODO: Generate system health metrics
            # TODO: Alert on system issues

            await asyncio.sleep(30)  # Health check every 30 seconds

    async def stop_monitoring(self):
        """
        Gracefully stop all monitoring operations.

        TODO: Implement clean shutdown
        """
        print("Stopping monitoring system...")
        self.running = False

        # TODO: Cancel all monitoring tasks
        # TODO: Close all OPC UA connections
        # TODO: Close InfluxDB client
        # TODO: Generate final statistics

class MachineDataProcessor:
    """
    Process and store machine data efficiently.

    Learning Goal: Efficient data processing and storage patterns
    """

    def __init__(self, influx_client, machine_name):
        """
        Initialize data processor for a specific machine.

        TODO: Set up data processing infrastructure
        """
        self.influx_client = influx_client
        self.machine_name = machine_name
        self.data_buffer = []
        self.last_flush = datetime.now(timezone.utc)

    async def process_data_change(self, node_id, value, timestamp):
        """
        Process incoming data change from OPC UA subscription.

        TODO: Implement efficient data processing
        Args:
            node_id (str): OPC UA node identifier
            value: The new value
            timestamp (datetime): When the change occurred

        Learning Goal: Real-time data processing patterns
        """
        # TODO: Validate data quality
        # TODO: Apply filtering rules
        # TODO: Create InfluxDB data point
        # TODO: Buffer data for batch writing
        # TODO: Flush buffer when appropriate

    async def flush_data_buffer(self):
        """
        Flush buffered data to InfluxDB.

        TODO: Implement efficient batch writing
        """
        # TODO: Write buffered data to InfluxDB
        # TODO: Handle write errors
        # TODO: Clear buffer after successful write
        # TODO: Update statistics

async def main():
    """Phase 3: Build complete industrial monitoring system"""
    print("=== PHASE 3: INDUSTRIAL MONITORING SYSTEM ===")

    # Load configuration
    with open("config.yaml") as f:
        config = yaml.safe_load(f)

    # Create and start monitoring system
    monitoring_system = IndustrialMonitoringSystem(config)

    try:
        await monitoring_system.start_monitoring()
    except KeyboardInterrupt:
        print("Monitoring stopped by user")
    except Exception as e:
        print(f"Monitoring system error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
```

---

## 🎯 **Final Integration Challenge**

### **The Complete Async Industrial System**

**Your Mission**: Build a production-ready monitoring system that:

1. **Monitors multiple machines** concurrently using async OPC UA
2. **Handles real-time data streams** with subscriptions
3. **Stores data efficiently** in InfluxDB using batch operations
4. **Recovers from failures** gracefully without losing data
5. **Provides system health monitoring** and diagnostics

**Success Criteria**:
- System monitors 3+ simulated machines simultaneously
- Handles machine disconnections and reconnections automatically
- Maintains data collection during partial system failures
- Provides real-time performance metrics
- Demonstrates understanding of async programming concepts

**Bonus Challenges**:
- Implement data prioritization (critical vs non-critical)
- Add real-time alerting for anomalous conditions
- Create async web API for system status
- Optimize performance for 100+ concurrent connections

---

## 📚 **Additional Learning Resources**

### **Async Programming**
- [Real Python - Async IO](https://realpython.com/async-io-python/)
- [Python asyncio Documentation](https://docs.python.org/3/library/asyncio.html)
- [Async/Await Best Practices](https://docs.python.org/3/library/asyncio-dev.html)

### **OPC UA Protocol**
- [OPC Foundation](https://opcfoundation.org/)
- [asyncua Documentation](https://asyncua.readthedocs.io/)
- [OPC UA Specification](https://reference.opcfoundation.org/)

### **Industrial Communication**
- [Industrial IoT Protocols](https://www.influxdata.com/solutions/industrial-iot/)
- [Manufacturing Data Architecture](https://www.automationworld.com/factory/iiot)

---

## 🚀 **Ready to Master Async Industrial Programming?**

This task will challenge you to think like an industrial software engineer while mastering advanced async programming concepts. Focus on understanding the **patterns** and **principles** that make industrial systems reliable and efficient.

**Remember**: Async programming is about coordination and efficiency, not just speed. Master the concepts, and you'll be able to build systems that handle real-world industrial complexity.

Good luck! ⚡
